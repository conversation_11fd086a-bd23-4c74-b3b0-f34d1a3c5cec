; (function () {
  window.OvationMessenger = window.OvationMessenger || {}
  window.OvationMessenger.config = {
    integrationId: 'JMAC2L',
    partnerToken: 'B3C6B7890DCD078CB7F1',
    partnerTeam: '9AM-7X6',
    hidden: true,
  }

  // Add a global function to identify user that can be called after login
  window.identifyMessengerUser = function(userData, retryCount = 0) {
    if (window.OvationMessenger && typeof window.OvationMessenger.identifyUser === 'function') {
      window.OvationMessenger.identifyUser({
        email: userData.email || '',
        phone: userData.phone || '',
        firstName: userData.firstName || userData.name || '',
      });
    } else {
      if (retryCount < 5) {
        console.warn(`OvationMessenger not ready yet, will try again in 1 second (attempt ${retryCount + 1}/5)`);
        setTimeout(function() {
          window.identifyMessengerUser(userData, retryCount + 1);
        }, 1000);
      } else {
        console.error('OvationMessenger failed to initialize after 5 attempts, giving up');
      }
    }
  }
  
  var loadMessengerConnect = function () {
    var script = document.createElement('script')
    script.type = 'text/javascript'
    script.async = true
    script.src = 'https://messenger.goboomtown.com/js/messengerConnect.js'
    document.body.appendChild(script)
  }

  if (
    document.readyState === 'complete' ||
    document.readyState === 'interactive'
  ) {
    loadMessengerConnect()
  } else {
    document.addEventListener(
      'DOMContentLoaded',
      loadMessengerConnect,
      false,
    )
  }

})() 