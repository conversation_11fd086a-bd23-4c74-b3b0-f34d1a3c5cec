{"name": "cxm-ui", "version": "0.18.66", "private": true, "type": "module", "scripts": {"dev": "vite --host", "build-old": "run-p type-check \"build-only {@}\" --", "build": "vite build", "preview": "vite preview", "test:unit": "vitest", "test:ci": "vitest run --coverage --reporter=junit --reporter=basic --outputFile.junit=./test-report.xml --watch false --passWithNoTests", "test:e2e": "playwright test", "test:e2e-ci": "playwright test --reporter=junit", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "lint:ci": "eslint . --fix", "format": "prettier --write src/", "release:patch": "./scripts/release.sh patch full", "release:minor": "./scripts/release.sh minor full", "release:major": "./scripts/release.sh major full", "release:explicit": "./scripts/release.sh explicit full"}, "dependencies": {"@datadog/browser-rum": "^6.6.3", "@services/ui-component-library": "^1.0.98", "@tinymce/tinymce-vue": "^6.1.0", "@vueuse/core": "^13.1.0", "@xmpp/client": "^0.13.4", "@xmpp/debug": "^0.13.3", "announcekit-vue": "^3.1.0", "codemirror": "^5.65.19", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "html2pdf.js": "^0.10.3", "linkify-html": "^4.3.1", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.3.0", "primevue": "^4.3.3", "quill": "^2.0.3", "tailwindcss": "^3.4.17", "uuid": "^11.1.0", "vue": "^3.5.13", "vue-froala-wysiwyg": "^4.5.2", "vue-i18n": "^9.14.3", "vue-router": "^4.5.0", "vue3-ace-editor": "^2.2.4", "vuedraggable": "^4.1.0"}, "devDependencies": {"@intlify/unplugin-vue-i18n": "^6.0.8", "@pinia/testing": "^1.0.0", "@playwright/test": "^1.51.0", "@tailwindcss/postcss": "^4.1.7", "@tsconfig/node22": "^22.0.0", "@types/codemirror": "^5.60.16", "@types/jsdom": "^21.1.7", "@types/node": "^22.13.9", "@types/uuid": "^10.0.0", "@vitejs/plugin-vue": "^5.2.1", "@vitest/coverage-v8": "^3.0.8", "@vitest/eslint-plugin": "^1.1.36", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "eslint": "^9.21.0", "eslint-plugin-playwright": "^2.2.0", "eslint-plugin-vue": "~10.0.0", "graphql": "^16.11.0", "jiti": "^2.4.2", "jsdom": "^26.0.0", "msw": "^2.7.3", "npm-run-all2": "^7.0.2", "playwright-msw": "^3.0.1", "postcss": "^8.5.3", "postcss-import": "^16.1.0", "postcss-nesting": "^13.0.1", "postcss-preset-env": "^10.1.5", "prettier": "3.5.3", "sass": "^1.85.1", "sass-loader": "^16.0.5", "typescript": "~5.8.0", "vite": "^6.2.1", "vite-plugin-vue-devtools": "^7.7.2", "vitest": "^3.0.8", "vue-tsc": "^2.2.8"}, "msw": {"workerDirectory": ["public"]}}