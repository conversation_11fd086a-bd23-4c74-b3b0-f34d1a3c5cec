export interface Product {
    id: string;
    name: string;
    description?: string;
    price?: number;
    category?: string;
    status?: number;
    created?: string;
    updated?: string;
}

export interface ProductListResponse {
    success: boolean;
    partners_product_list: {
        totalCount: number;
        results: Product[];
    };
}

// New Partner-related interfaces
export interface PartnerDeviceAlertPrefs {
    [key: string]: {
        [subKey: string]: string[];
    };
}

export interface PartnerCommChannel {
    id: string;
    object: string;
    object_id: string;
    object_scope: string;
    comm_type: number;
    comm_label: string;
    comm_status: number;
    comm_state: number;
    object_source: string;
    billing_status: number;
    title: string;
    subtitle: string;
    duration_plus: number;
    duration: number;
    user_message_cnt: Record<string, number>;
    external_rpid: string;
    external_lpid: string;
    external_id: string;
    external_status: string;
    created: string;
    updated: string;
    occupied: string;
    completed: string;
    c__status: string;
    c__state: string;
    _highlighted: boolean;
    _highlightmap: Record<string, any>;
    c__avatar: string;
}

export interface PartnerFieldSetting {
    key: string;
    fieldType: string;
    fieldLabel: string;
    fieldDescription: string;
    value: boolean | string[];
    plOptions: Array<{ label: string; value: string }> | null;
}

export interface PartnerUIAccess {
    edit: boolean;
    delete: boolean;
    clone: boolean;
    merge: boolean;
}

export interface Partner {
    id: string;
    package_id: string;
    name: string;
    name_legal: string;
    nickname: string;
    time_zone: string | null;
    description: string;
    street_1: string;
    street_2: string;
    city: string;
    state_id: number;
    zipcode: string;
    country_id: number;
    latitude: number;
    longitude: number;
    email: string;
    phone: string;
    website: string;
    custom_email_domain: string;
    subdomain: string;
    smtp_relay_host: string | null;
    types: string[];
    status: number;
    target_response_time: number;
    issue_view_override: boolean;
    avatar: string;
    api_token: string;
    api_token_sandbox: boolean;
    default_partners_teams_id: string;
    chart_paths: string;
    issues_reassigned: string;
    email_domains: string;
    email_aliases: string;
    project_partner_ids: string[];
    partners_invoices_plans_id: string;
    ml_isolation: boolean;
    password_policy: number;
    user_limit: number | null;
    inbox_settings: any | null;
    created: string;
    updated: string;
    country: string;
    stale_issue_ttl: number;
    state: string;
    c__d_state_id: string;
    c__d_status: string;
    is_service_provider: boolean;
    is_standard: boolean;
    c__issues_count: number;
    c__user_count: number;
    c__team_count: number;
    c__package_user_limit: number;
    url_avatar: string;
    c__time_zone: string | null;
    c__smtp_relay_host: string | null;
    c__user_limit: number | null;
    c__inbox_settings: string;
    _highlighted: boolean;
    _highlightmap: Record<string, any>;
    m__preamble_url: string;
    m__preamble_text: string;
    m__use_global_tags: boolean;
    m__extra_file_types: any[];
    m__relay_audio_only: boolean;
    m__device_alert_prefs: PartnerDeviceAlertPrefs;
    m__send_advice_emails: boolean;
    m__work_order_form_url: string;
    m__auto_redact_patterns: any[];
    m__realtime_auto_redact: boolean;
    m__button_redact_patterns: any[];
    m__diagnostic_report_email: string;
    m__auto_send_customer_survey: boolean;
    m__max_open_tickets_per_user: number;
    m__monitoring_reports_enabled: boolean;
    m__disable_concurrent_sessions: boolean;
    m__issue_public_notification_ttl: number;
    _has_detail: boolean;
    _canWrite: boolean;
    _uiAccess: PartnerUIAccess;
    availableComm: PartnerCommChannel[];
    is__btn_open: boolean;
    is__btn_snooze: boolean;
    is__btn_watch: boolean;
    is__btn_claim: boolean;
    is__btn_chat: boolean;
    is__col1_customer_name: boolean;
    is__col1_customer_user_name: boolean;
    is__col1_customer_user_sms: boolean;
    is__col1_customer_user_email: boolean;
    is__col1_location_mid: boolean;
    is__col1_latest_transcript: boolean;
    is__col1_technician_name: boolean;
    is__col1_scheduled_time: boolean;
    is__col1_issue_name: boolean;
    is__col1_sponsor_team_name: boolean;
    is__col1_owner_team_name: boolean;
    is__col2_source_icon: boolean;
    is__col2_reference_num: boolean;
    is__col2_timer: boolean;
    is__sort_mine_by: string[];
    is__sort_unclaimed_by: string[];
    fields_inbox_field_settings: Record<string, PartnerFieldSetting>;
    fields_inbox_sort_settings: Record<string, PartnerFieldSetting>;
    _teams_boomtown_visible: string[];
    _teams_email_enabled: string[];
}

export interface PartnerResponse {
    success: boolean;
    partners: {
        totalCount: number;
        results: Partner[];
    };
    current_server_time: string;
}

export interface CurrentPartnerResponse {
    success: boolean;
    partner: Partner;
    current_server_time: string;
}
