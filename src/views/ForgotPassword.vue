<script setup lang="ts">
import { ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useAuthStore } from '../stores/auth';
import BravoPasswordReset from '@services/ui-component-library/components/BravoPasswordReset.vue';
import { useI18n } from 'vue-i18n';

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();
const { t } = useI18n();

// Get email from query parameters if available
const initialEmail = (route.query.email as string) || '';

const isLoading = ref(false);
const errorMessage = ref('');
const successMessage = ref('');

const handleSubmit = async (email: string) => {
    isLoading.value = true;
    errorMessage.value = '';
    successMessage.value = '';

    try {
        await authStore.userResetPassword({ email });
        successMessage.value = t('auth.passwordResetSent', 'Password reset instructions have been sent to your email.');
    } catch (error) {
        console.error('❌ Password reset failed:', error);
        errorMessage.value = error instanceof Error
            ? error.message
            : t('auth.passwordResetFailed', 'Failed to send password reset email. Please try again.');
    } finally {
        isLoading.value = false;
    }
};

const backToLogin = () => {
    router.push('/login');
};

</script>

<template>
    <div class="forgot-password-page" data-testid="login-page">
        <div class="logo-container" data-testid="logo-container">
            <img src="../assets/ovationcxm-login-logo.png" alt="OvationCXM Logo" data-testid="logo-image" />
        </div>

        <BravoPasswordReset
            :initialEmail="initialEmail"
            :submitHandler="handleSubmit"
            @backToLogin="backToLogin"
        />
    </div>
</template>

<style scoped>
.forgot-password-page {
    background-image: url('../assets/login-ovationcxm-background.svg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    min-height: 100vh;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 2rem;
}

.logo-container {
    max-width: 300px;
}

.logo-container img {
    width: 100%;
    height: auto;
}

/* Override the BravoLoginScreenSSO's wrapper background */
:deep(.login-wrapper) {
    position: relative !important;
    background: transparent !important;
}
</style>
