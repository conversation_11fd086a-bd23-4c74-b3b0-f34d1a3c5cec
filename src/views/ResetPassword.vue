<script setup lang="ts">
import { ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useAuthStore } from '../stores/auth';
import BravoPasswordResetConfirm from '@services/ui-component-library/components/BravoPasswordResetConfirm.vue';
import { useI18n } from 'vue-i18n';

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();
const { t } = useI18n();

const token = route.params.token as string;
const email = decodeURIComponent(route.params.email as string);

const isLoading = ref(false);
const errorMessage = ref('');
const successMessage = ref('');

const handleSubmit = async (password: string) => {
    isLoading.value = true;
    errorMessage.value = '';
    successMessage.value = '';

    try {
        await authStore.userChangePassword({
            email,
            password,
            confirm_password: password, // Use same value for confirm_password
            token
        });

        successMessage.value = t('auth.passwordChangeSuccess', 'Your password has been successfully changed.');

        // Redirect to login after a short delay
        setTimeout(() => {
            router.push('/login');
        }, 2000);

    } catch (error) {
        console.error('❌ Password change failed:', error);
        errorMessage.value = error instanceof Error
            ? error.message
            : t('auth.passwordChangeFailed', 'Failed to change password. Please try again.');
    } finally {
        isLoading.value = false;
    }
};

const backToLogin = () => {
    router.push('/login');
};

</script>

<template>
    <div class="forgot-password-page" data-testid="login-page">
        <div class="logo-container" data-testid="logo-container">
            <img src="../assets/ovationcxm-login-logo.png" alt="OvationCXM Logo" data-testid="logo-image" />
        </div>

        <BravoPasswordResetConfirm
            :userEmail="email"
            :submitHandler="handleSubmit"
            :onBackToLogin="backToLogin"
            :isLoading="isLoading"
            :errorMessage="errorMessage"
            :successMessage="successMessage"
        />
    </div>
</template>

<style scoped>
.forgot-password-page {
    background-image: url('../assets/login-ovationcxm-background.svg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    min-height: 100vh;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 2rem;
}

.logo-container {
    max-width: 300px;
}

.logo-container img {
    width: 100%;
    height: auto;
}

/* Override the BravoLoginScreenSSO's wrapper background */
:deep(.login-wrapper) {
    position: relative !important;
    background: transparent !important;
}
</style>
