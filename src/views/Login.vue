<script setup lang="ts">
import { useUserAPI } from '@/composables/services/useUserAPI';
import { useRememberMe } from '@/composables/useRememberMe';
import BravoLoginScreenSSO from '@services/ui-component-library/components/BravoLoginScreenSSO.vue';
import BravoTag from '@services/ui-component-library/components/BravoTag.vue';
import { onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import SsoProviderDialog from '../components/SsoProviderDialog.vue';
import { useAuthStore } from '../stores/auth';

const router = useRouter();
const authStore = useAuthStore();
const { checkSSO } = useUserAPI();
const { rememberedEmail, isRemembered, handleRememberMe } = useRememberMe();
const bravoLoginRef = ref<InstanceType<typeof BravoLoginScreenSSO>>();
const { locale } = useI18n();

// SSO state
const ssoProviders = ref<Array<{logo: string | null; name: string; description: string; url: string}>>([]);
const showSsoProviderDialog = ref(false);

onMounted(() => {
    
    // Focus the email input field when component is mounted
    setTimeout(() => {
        const emailInput = document.querySelector('input[type="email"]') as HTMLInputElement;
        if (emailInput) {
            // If we have a remembered email, set it in the input first
            if (rememberedEmail.value) {
                emailInput.value = rememberedEmail.value;
                
                // Trigger all the events the component needs to recognize the change
                emailInput.dispatchEvent(new Event('input', { bubbles: true }));
                emailInput.dispatchEvent(new Event('change', { bubbles: true }));
                emailInput.dispatchEvent(new Event('blur', { bubbles: true }));
            }
            
            // Focus after setting the value and triggering events
            emailInput.focus();
        }
        
        // Auto-check the remember me checkbox if user is remembered
        if (isRemembered.value) {
            const rememberMeCheckbox = document.querySelector('input[type="checkbox"]') as HTMLInputElement;
            if (rememberMeCheckbox && !rememberMeCheckbox.checked) {
                rememberMeCheckbox.checked = true;
                // Trigger change event to ensure component state is updated
                rememberMeCheckbox.dispatchEvent(new Event('change', { bubbles: true }));
                rememberMeCheckbox.dispatchEvent(new Event('input', { bubbles: true }));
            }
        }
    }, 100);
});

// Email check handler for BravoLoginScreenSSO
// Returns false if SSO is not available, otherwise returns whether internal auth is available
const emailCheckHandler = async (email: string): Promise<boolean> => {
    // Test cookie setting here
    authStore.setRememberMeCookie();
    
    // Store email for remember me functionality
    if (email) {
        handleRememberMe(email, true);
    }
    
    console.debug(`Checking SSO for email: ${email}`);
    try {
        console.debug('Calling checkSSO...');
        const response = await checkSSO(email);
        console.debug('SSO check response:', response);
        
        // Store SSO providers for later use
        ssoProviders.value = response.externalIdPs || [];
        console.debug(`Found ${ssoProviders.value.length} SSO provider(s):`, ssoProviders.value);
        console.debug(`Internal authentication ${response.canAuthenticateInternal ? 'is' : 'is not'} available`);
        console.debug(`External authentication ${response.canAuthenticateExternal ? 'is' : 'is not'} available`);
        
        // Return false if external auth is not available
        if (response.canAuthenticateExternal) {
            console.log('External authentication available, handling SSO');
            return true;
        }
        return false;
    } catch (error) {
        console.error('Error checking SSO:', error);
        console.error('Stack trace:', error instanceof Error ? error.stack : 'No stack trace available');
        console.debug('Defaulting to showing password field due to error');
    }
    return false; // Default to showing password field on error
};

// Handle SSO redirect event from BravoLoginScreenSSO
const handleSsoRedirect = (eventData: { email: string }) => {
    console.debug('handle sso redirect');
    // If we have multiple SSO providers, show the selection dialog
    if (ssoProviders.value.length > 1) {
        showSsoProviderDialog.value = true;
    } 
    // If we have only one SSO provider, redirect directly
    else if (ssoProviders.value.length === 1) {
        window.location.href = ssoProviders.value[0].url;
    }
    // Otherwise, show error (this shouldn't happen if component is working correctly)
    console.error('No SSO providers available');
};

// Handle provider selection from dialog
const handleProviderSelected = (url: string) => {
    window.location.href = url;
};

const handleSubmit = async (credentials: { email: string; password: string; rememberMe: boolean }) => {
    try {
        // Call the login method from the user store with the provided credentials
                await authStore.login({
            email: credentials.email,
            password: credentials.password,
            rememberMe: credentials.rememberMe,
        });

        // Handle remember me based on checkbox state on successful login
        handleRememberMe(credentials.email, credentials.rememberMe);

        // If login successful, redirect to home
        // check auth store for redirect after login
        // otherwise go to inbox view home
        if (authStore.redirectAfterLogin) {
            
            router.push(authStore.redirectAfterLogin);
            // clear from auth store 
            authStore.redirectAfterLogin = '';

        } else {
            router.push('/inbox?view=home');
        }
    } catch (error) {
        // The BravoLoginScreenSSO component should handle displaying the error
        throw error;
    }
};

const handleForgotPassword = (eventData: { email: string }) => {
    if (eventData?.email) {
        router.push({ path: '/passwordreset', query: { email: eventData.email } });
    } else {
        router.push('/passwordreset');
    }
};
</script>

<template>
    <div class="login-page" data-testid="login-page">
        <BravoTag severity="warn">Alpha</BravoTag>
        
        <div class="logo-container" data-testid="logo-container">
            <img src="../assets/ovationcxm-login-logo.png" alt="OvationCXM Logo" data-testid="logo-image" />
        </div>

        <BravoLoginScreenSSO 
            ref="bravoLoginRef" 
            :submitHandler="handleSubmit"
            :emailCheckHandler="emailCheckHandler"
            :initialEmail="rememberedEmail"
            :initialRememberMe="isRemembered"
            @ssoRedirect="handleSsoRedirect"
            data-testid="bravo-login-screen" 
            @forgotPassword="handleForgotPassword"

        />

        <!-- SSO Provider Selection Dialog -->
        <SsoProviderDialog
            v-model:visible="showSsoProviderDialog"
            :providers="ssoProviders"
            @provider-selected="handleProviderSelected"
        />
    </div>
</template>

<style scoped>
.login-page {
    background-image: url('../assets/login-ovationcxm-background.svg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    min-height: 100vh;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 2rem;
}

.logo-container {
    max-width: 300px;
}

.logo-container img {
    width: 100%;
    height: auto;
}

/* Override the BravoLoginScreenSSO's wrapper background */
:deep(.login-wrapper) {
    position: relative !important;
    background: transparent !important;
}
</style>
