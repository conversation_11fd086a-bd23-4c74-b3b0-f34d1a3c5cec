import { useUserStore } from '@/stores/user';
import { createTesting<PERSON>inia } from '@pinia/testing';
import { mount } from '@vue/test-utils';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import Login from '../Login.vue';

// Mock the components
vi.mock('@services/ui-component-library/components/BravoLoginScreenSSO.vue', () => ({
    default: {
        name: 'BravoLoginScreenSSO',
        props: {
            submitHandler: Function,
            emailCheckHandler: Function,
        },
        template: '<div class="mock-login-screen"><slot></slot></div>',
        emits: ['onSsoRedirect'],
    },
}));

vi.mock('@/components/SsoProviderDialog.vue', () => ({
    default: {
        name: 'SsoProviderDialog',
        props: {
            visible: Boolean,
            providers: Array,
        },
        template: '<div class="mock-sso-dialog"></div>',
        emits: ['update:visible', 'provider-selected'],
    },
}));

// Mock window.location
const mockLocation = {
    href: '',
    assign: vi.fn(),
    reload: vi.fn(),
};
Object.defineProperty(window, 'location', {
    value: mockLocation,
    writable: true,
});

// Mock vue-router
vi.mock('vue-router', () => ({
    useRouter: () => ({
        push: vi.fn(),
    }),
}));

// Mock i18n
vi.mock('vue-i18n', () => ({
    useI18n: () => ({
        locale: { value: 'en' },
    }),
}));

// Mock setLocale
vi.mock('@/i18n', () => ({
    setLocale: vi.fn(),
}));

// Mock checkSSO function
const mockCheckSSO = vi.fn();

// Mock useUserAPI composable
vi.mock('@/composables/services/useUserAPI', () => ({
    useUserAPI: () => ({
        checkSSO: mockCheckSSO,
    }),
}));

describe('Login.vue', () => {
    let wrapper: any;
    let userStore: any;

    beforeEach(() => {
        // Reset location href
        window.location.href = '';

        // Reset mocks
        vi.resetAllMocks();

        // Create a fresh pinia and wrapper before each test
        wrapper = mount(Login, {
            global: {
                plugins: [
                    createTestingPinia({
                        createSpy: vi.fn,
                    }),
                ],
                stubs: {
                    RouterLink: true,
                    BravoLoginScreenSSO: true,
                    SsoProviderDialog: true,
                },
            },
        });

        userStore = useUserStore();
        userStore.login = vi.fn();
    });

    it('renders the login page with logo and language selector', () => {
        expect(wrapper.find('[data-testid="logo-container"]').exists()).toBe(true);
        expect(wrapper.find('[data-testid="logo-image"]').exists()).toBe(true);
        expect(wrapper.find('[data-testid="language-selector"]').exists()).toBe(true);
    });

    it('calls user store login method when submitHandler is called', async () => {
        const credentials = {
            email: '<EMAIL>',
            password: 'password123',
            rememberMe: false,
        };

        userStore.login.mockResolvedValue(undefined);

        await wrapper.vm.handleSubmit(credentials);

        expect(userStore.login).toHaveBeenCalledWith({
            email: '<EMAIL>',
            password: 'password123',
        });
    });

    it('returns correct values from emailCheckHandler based on SSO response', async () => {
        // Test case 1: No SSO available
        mockCheckSSO.mockResolvedValueOnce({
            success: true,
            canAuthenticateInternal: true,
            canAuthenticateExternal: false,
            externalIdPs: [],
        });

        let result = await wrapper.vm.emailCheckHandler('<EMAIL>');
        expect(result).toBe(true); // Show password field
        expect(mockCheckSSO).toHaveBeenCalledWith('<EMAIL>');

        // Test case 2: SSO available with internal auth
        mockCheckSSO.mockResolvedValueOnce({
            success: true,
            canAuthenticateInternal: true,
            canAuthenticateExternal: true,
            externalIdPs: [{ name: 'Test SSO', url: 'https://sso.example.com' }],
        });

        result = await wrapper.vm.emailCheckHandler('<EMAIL>');
        expect(result).toBe(true); // Still show password field

        // Test case 3: SSO only (no internal auth)
        mockCheckSSO.mockResolvedValueOnce({
            success: true,
            canAuthenticateInternal: false,
            canAuthenticateExternal: true,
            externalIdPs: [{ name: 'Test SSO', url: 'https://sso.example.com' }],
        });

        result = await wrapper.vm.emailCheckHandler('<EMAIL>');
        expect(result).toBe(false); // Don't show password field
    });

    it('handles SSO redirect with one provider', async () => {
        // Setup single SSO provider
        wrapper.vm.ssoProviders = [
            {
                name: 'Test SSO',
                description: 'Test SSO Provider',
                logo: null,
                url: 'https://sso.example.com',
            },
        ];

        await wrapper.vm.handleSsoRedirect();

        expect(window.location.href).toBe('https://sso.example.com');
    });

    it('shows provider dialog with multiple SSO providers', async () => {
        // Setup multiple SSO providers
        wrapper.vm.ssoProviders = [
            {
                name: 'Test SSO 1',
                description: 'Test SSO Provider 1',
                logo: null,
                url: 'https://sso1.example.com',
            },
            {
                name: 'Test SSO 2',
                description: 'Test SSO Provider 2',
                logo: null,
                url: 'https://sso2.example.com',
            },
        ];

        await wrapper.vm.handleSsoRedirect();

        expect(wrapper.vm.showSsoProviderDialog).toBe(true);
    });

    it('redirects to selected provider URL', async () => {
        const testUrl = 'https://selected-sso.example.com';

        await wrapper.vm.handleProviderSelected(testUrl);

        expect(window.location.href).toBe(testUrl);
    });
});
