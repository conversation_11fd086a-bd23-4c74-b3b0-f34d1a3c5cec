/**
 * Text formatting utilities
 */

/**
 * Converts basic markdown formatting to HTML
 * @param text - The markdown text to convert
 * @returns HTML formatted string
 */
export const convertMarkdownToHtml = (text: string): string => {
  if (!text) return text;
  
  let html = text;
  
  // Convert headers first (### Header -> <h3>Header</h3>)
  html = html.replace(/^### (.+)$/gm, '<h3>$1</h3>');
  html = html.replace(/^## (.+)$/gm, '<h2>$1</h2>');
  html = html.replace(/^# (.+)$/gm, '<h1>$1</h1>');
  
  // Convert bold text first (**text** -> <strong>text</strong>)
  // Simple non-greedy match that works with any content between **
  html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
  
  // Convert italic text (*text* -> <em>text</em>) after bold
  html = html.replace(/\*([^*]+)\*/g, '<em>$1</em>');
  
  // Convert line breaks - handle both \n and \r\n
  html = html.replace(/\r?\n/g, '<br>');
  
  // Convert double line breaks to paragraph breaks
  html = html.replace(/<br><br>/g, '</p><p>');
  
  // Wrap the content in paragraph tags if it doesn't start with a header
  if (!html.startsWith('<h')) {
    html = '<p>' + html + '</p>';
  }
  
  // Clean up any empty paragraphs
  html = html.replace(/<p><\/p>/g, '');
  html = html.replace(/<p><br><\/p>/g, '');
  
  return html;
};

/**
 * Converts plain text newlines to HTML line breaks
 * @param text - The plain text to convert
 * @returns HTML formatted string with <br> tags
 */
export const convertNewlinesToHtml = (text: string): string => {
  if (!text) return text;
  // Replace \n with <br> and ensure proper HTML formatting
  return text.replace(/\n/g, '<br>');
}; 