<script setup lang="ts">
import { useKnowledgeAPI } from '@/composables/services/useKnowledgeAPI';
import { useMetaStore } from '@/stores/meta';
import { usePartnerStore } from '@/stores/partner';
import BravoAccordionContent from '@services/ui-component-library/components/BravoAccordionContent.vue';
import BravoAccordionHeader from '@services/ui-component-library/components/BravoAccordionHeader.vue';
import BravoAccordionPanel from '@services/ui-component-library/components/BravoAccordionPanel.vue';
import { storeToRefs } from 'pinia';
import Skeleton from 'primevue/skeleton';

import { computed, onMounted, onUnmounted, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useKnowledgeStore } from '../stores/knowledge';
import BravoFormField from './BravoFormField.vue';
import LabelsFormInput from './LabelsFormInput.vue';
import LibraryFormInput from './LibraryFormInput.vue';
import OrganizationAccessFormInput from './OrganizationAccessFormInput.vue';
import OwnerFormInput from './OwnerFormInput.vue';
import ProductFormInput from './ProductFormInput.vue';
import SearchTermsFormInput from './SearchTermsFormInput.vue';
import TagFormInput from './TagFormInput.vue';
import TeamAccessFormInput from './TeamAccessFormInput.vue';
import UrlFormInput from './UrlFormInput.vue';

const { t } = useI18n();
const knowledgeStore = useKnowledgeStore();
const metaStore = useMetaStore();
const partnerStore = usePartnerStore();
const { availableKnowledgeBases, loadingKnowledgeBases, labelTree, labelMap } = storeToRefs(knowledgeStore);

const knowledgeAPI = useKnowledgeAPI();

// Define props and emits
const props = defineProps<{
    article: any;
    isLoading?: boolean;
    isWideMode?: boolean;
    isReady: boolean;
    isEditing?: boolean;
}>();

const emit = defineEmits<{
    (e: 'update:field', field: string, value: any): void;
    (e: 'update', article: any): void;
}>();

// State for editable fields - most moved to separate components

// Reference to the input elements - most moved to separate components

// Visibility options
const visibilityOptions = [
    { label: 'Public', value: 0 },
    { label: 'Internal', value: 1 },
    { label: 'Partner Ecosystem', value: 2 },
];

// Map visibility value to the article's visibility property
const getVisibilityValueFromArticle = (article: any) => {
    if (article.c__d_visibility === 'Internal' || article.isPrivate) {
        return 1;
    } else if (article.c__d_visibility === 'Partner Ecosystem') {
        return 2;
    }
    return 0; // Public by default
};

// Map visibility value to label
const getVisibilityLabel = (value: number) => {
    const option = visibilityOptions.find((opt) => opt.value === value);
    return option ? option.label : 'Public';
};

// Computed properties for BravoFormField
const visibilityValue = computed(() => getVisibilityValueFromArticle(props.article));
const visibilityDisplayValue = computed(() => getVisibilityLabel(visibilityValue.value));
const visibilityIconClass = computed(() => {
    return props.article.c__d_visibility === 'Internal' || props.article.isPrivate ? 'pi pi-lock' : 'pi pi-globe';
});

// Visibility form field ref and loading state
const visibilityFormFieldRef = ref<any>(null);
const isVisibilitySaving = ref(false);

// Handle visibility update
const handleVisibilityUpdate = (fieldName: string, value: any) => {
    // No special handling needed - just pass through
};

// Handle visibility save
const handleVisibilitySave = async (fieldName: string, value: any) => {
    if (value !== getVisibilityValueFromArticle(props.article)) {
        isVisibilitySaving.value = true;
        try {
            await submitArticleFieldUpdate('visibility', value);
            emit('update:field', 'visibility', value.toString());
            visibilityFormFieldRef.value?.handleSaveComplete(true);
        } catch (error) {
            visibilityFormFieldRef.value?.handleSaveComplete(false);
        } finally {
            isVisibilitySaving.value = false;
        }
    } else {
        visibilityFormFieldRef.value?.handleSaveComplete(true);
    }
};

// URL focus handling is now managed by BravoFormField

// URL loading state is now handled by BravoFormField

// Visibility loading state is now handled by BravoFormField

// Add loading state for owner


// Team and organization data for legacy functionality
const teams = ref<any[]>([]);
const loadingTeams = ref(false);

// Load libraries and teams when the component is mounted
onMounted(async () => {
    document.addEventListener('click', handleClickOutside);

    // Check if we already have teams in the store
    if (knowledgeStore.teams && knowledgeStore.teams.length > 0) {
        teams.value = knowledgeStore.teams;
    } else {
        // Otherwise load them
        loadingTeams.value = true;
        try {
            const result = await knowledgeStore.fetchTeamsOnly();
            teams.value = result;
        } catch (error) {
            console.error('Error loading teams:', error);
        } finally {
            loadingTeams.value = false;
        }
    }

    // Ensure we have partner metadata loaded
    if (!metaStore.partnerMetaData) {
        await metaStore.loadPartnerMetaData();
    }

    if (partnerStore.products.length === 0) {
        await partnerStore.fetchProducts();
    }

    await knowledgeStore.fetchAvailableKnowledgeBasesOnly();

    // Ensure labelMap is loaded on mount if not present
    // This is critical for page refreshes where the store is empty
    if (!Object.keys(knowledgeStore.labelMap).length) {
        console.log('ArticleInformationBlock: Loading label tree on mount (empty store)');
        await knowledgeStore.fetchLabelTree();
        console.log('ArticleInformationBlock: Label tree loaded, labelMap now has:', Object.keys(knowledgeStore.labelMap).length, 'entries');
    }
    
    // Mark component as mounted
    isMounted.value = true;
});

// Library focus handling moved to LibraryFormInput component



// Organization access focus handling moved to OrganizationAccessFormInput component

// Product, tag, and search term editing functions moved to separate components

// Click handler for legacy inline editing fields only (URL, Visibility, Library, Labels, Related Articles)
const handleClickOutside = (event: MouseEvent) => {
    const target = event.target as HTMLElement;

    // Don't interfere with BravoFormField components - check if click is inside any BravoFormField
    const bravoFormFields = document.querySelectorAll('[data-testid*="team-access"], [data-testid*="organization-access"], [data-testid*="products"], [data-testid*="tags"], [data-testid*="search-terms"]');
    const isInsideBravoFormField = Array.from(bravoFormFields).some(field => field.contains(target));
    
    // Also check for dropdown panels that might be from BravoFormField components
    const dropdownPanels = document.querySelectorAll('.p-dropdown-panel, .p-multiselect-panel, .p-overlay-panel, [data-pc-section="panel"]');
    const isInsideDropdownPanel = Array.from(dropdownPanels).some(panel => panel.contains(target));
    
    // If click is inside a BravoFormField or its dropdown, don't handle it here
    if (isInsideBravoFormField || isInsideDropdownPanel) {
        return;
    }

    // Handle other legacy inline editing fields (library, labels, related articles) here if needed
};

// Add and remove document click event listener
onMounted(() => {
    document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside);
    // Clear any pending label loading timeout
    if (labelLoadingTimeout.value) {
        clearTimeout(labelLoadingTimeout.value);
        labelLoadingTimeout.value = null;
    }
});

// Team and organization helper functions moved to separate components






// Helper functions and computed properties moved to separate components

// Loading state for labels
const isLoadingLabels = ref(false);
const labelLoadingTimeout = ref<NodeJS.Timeout | null>(null);
const isMounted = ref(false);

// Add a generic submit handler
const submitArticleFieldUpdate = async (field: string, value: any) => {
    try {
        const revisionId = props.article.id;
        const response = await knowledgeAPI.updateArticleField(revisionId, field, value);
        if (response && response.success) {
            emit('update', props.article);
            // add 500ms delay
            await new Promise((resolve) => setTimeout(resolve, 500));

            return true;
        } else {
            return false;
        }
    } catch (error: any) {
        return false;
    }
};

// Visibility access computed properties moved to separate components

const headerText = computed(() => {
    return props.article?.type === 1 ? t('knowledge.template_information') : t('knowledge.article_information');
});

// Add this method near other helper methods
const getLastUrlSegment = (url: string) => {
    if (!url) return '';
    const segments = url.split('/');
    return segments[segments.length - 1];
};

// Node selection and library tracking moved to LabelsFormInput component

// Helper to fetch a label by ID and update the labelMap
async function fetchAndAddLabelToMap(labelId: string) {
  console.log(`fetchAndAddLabelToMap: Starting fetch for ${labelId}`);
  try {
    const result = await knowledgeAPI.loadLabels({
      sAction: 'metaKBLabels',
      query: JSON.stringify([{ property: 'id', value: labelId }]),
    });
    console.log(`fetchAndAddLabelToMap: API result for ${labelId}:`, result);
    
    if (result?.pl__kb_labels?.length) {
      console.log(`fetchAndAddLabelToMap: Found ${result.pl__kb_labels.length} labels in response`);
      
      // The API returns all labels from the library, so we need to find the specific one we want
      // and also add all the others to the map while we're at it
      let foundTargetLabel = false;
      
      for (const label of result.pl__kb_labels) {
        console.log(`fetchAndAddLabelToMap: Processing label ${label.id} -> ${label.lbl}`);
        knowledgeStore.addLabelToMap(label.id, label.lbl);
        
        if (label.id === labelId) {
          foundTargetLabel = true;
          console.log(`fetchAndAddLabelToMap: Found target label ${labelId} -> ${label.lbl}`);
        }
      }
      
      if (!foundTargetLabel) {
        console.warn(`fetchAndAddLabelToMap: Target label ${labelId} not found in response`);
        // Add a fallback entry to prevent infinite loading
        knowledgeStore.addLabelToMap(labelId, `Label ${labelId}`);
      }
      
      console.log(`fetchAndAddLabelToMap: Added ${result.pl__kb_labels.length} labels to store`);
    } else {
      console.warn(`fetchAndAddLabelToMap: No labels found in response for ${labelId}`);
      // Add a fallback entry to prevent infinite loading
      knowledgeStore.addLabelToMap(labelId, `Label ${labelId}`);
    }
  } catch (error) {
    console.error(`fetchAndAddLabelToMap: Error fetching ${labelId}:`, error);
    // Add a fallback entry to prevent infinite loading
    knowledgeStore.addLabelToMap(labelId, `Label ${labelId}`);
  }
}

// On mount or when article label IDs change, ensure all label names are loaded
watch(
  () => props.article.bc__tags_object_kb_labels,
  async (newLabels, oldLabels) => {
    console.log('Label IDs watcher triggered with:', newLabels);
    console.log('Label IDs watcher - old labels:', oldLabels);
    console.log('Label IDs watcher - current labelMap keys:', Object.keys(knowledgeStore.labelMap));
    
    // Clear any existing timeout
    if (labelLoadingTimeout.value) {
      clearTimeout(labelLoadingTimeout.value);
      labelLoadingTimeout.value = null;
    }
    
    if (!newLabels || newLabels.length === 0) {
      console.log('Label IDs watcher - no labels to process');
      isLoadingLabels.value = false;
      return;
    }
    
    // Ensure the store is initialized before trying to load individual labels
    // This is critical for page refreshes where the store might be empty
    if (Object.keys(knowledgeStore.labelMap).length === 0) {
      console.log('Label IDs watcher - labelMap is empty, initializing store first');
      isLoadingLabels.value = true;
      try {
        await knowledgeStore.fetchLabelTree();
        console.log('Label IDs watcher - store initialized, labelMap now has:', Object.keys(knowledgeStore.labelMap).length, 'entries');
      } catch (error) {
        console.error('Label IDs watcher - Error initializing store:', error);
        isLoadingLabels.value = false;
        return;
      }
    }
    
    // Check if we need to load any labels
    const missingLabels = newLabels.filter((id: string) => !knowledgeStore.labelMap[id]);
    if (missingLabels.length === 0) {
      console.log('Label IDs watcher - all labels already loaded');
      isLoadingLabels.value = false;
      return;
    }
    
    console.log('Label IDs watcher - processing labels...', missingLabels);
    isLoadingLabels.value = true;
    
    try {
      // Set a timeout to prevent infinite loading
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Label loading timeout')), 10000); // 10 second timeout
      });
      
      const loadingPromise = Promise.all(
        missingLabels.map(async (id: string) => {
          if (!knowledgeStore.labelMap[id]) {
            console.log(`Label IDs watcher - Fetching label name for ID: ${id}`);
            try {
              await fetchAndAddLabelToMap(id);
              console.log(`Label IDs watcher - Added to labelMap: ${id} -> ${knowledgeStore.labelMap[id]}`);
            } catch (error) {
              console.error(`Label IDs watcher - Error fetching label ${id}:`, error);
            }
          } else {
            console.log(`Label IDs watcher - Label ${id} already in labelMap: ${knowledgeStore.labelMap[id]}`);
          }
        })
      );
      
      await Promise.race([loadingPromise, timeoutPromise]);
      console.log('Label IDs watcher - finished processing labels');
    } catch (error) {
      console.error('Label IDs watcher - Error or timeout:', error);
      // Ensure all missing labels have fallback entries
      for (const id of missingLabels) {
        if (!knowledgeStore.labelMap[id]) {
          knowledgeStore.addLabelToMap(id, `Label ${id}`);
        }
      }
    } finally {
      isLoadingLabels.value = false;
    }
  },
  { immediate: true, deep: true }
);

// Field interaction computed properties moved to BravoFormField

</script>

<template>
    <BravoAccordionPanel value="0" data-testid="article-info-panel">
        <BravoAccordionHeader data-testid="article-info-header">{{
            headerText
        }}</BravoAccordionHeader>
        <BravoAccordionContent data-testid="article-info-content">
            <div v-if="isLoading" class="skeleton-content">
                <Skeleton height="1.5rem" class="mb-2" />
                <Skeleton height="1.5rem" class="mb-2" />
                <Skeleton height="1.5rem" class="mb-2" />
                <Skeleton height="1.5rem" class="mb-2" />
            </div>
            <div v-else class="data-fields" data-testid="article-info-fields">
                <!-- <OwnerFormInput
                    :article="article"
                    :isLoading="isLoading"
                    :isSaving="false"
                    :isHorizontal="isWideMode"
                    :isEditing="isEditing"
                    :onSubmit="submitArticleFieldUpdate"
                    @update="() => emit('update', article)"
                    data-testid="article-owner-input"
                /> -->
               
                <UrlFormInput
                    :article="article"
                    :isLoading="isLoading"
                    :isSaving="false"
                    :isHorizontal="isWideMode"
                    :isEditing="isEditing"
                    :onSubmit="submitArticleFieldUpdate"
                    @update="() => emit('update', article)"
                    data-testid="article-url-input"
                />
                <BravoFormField
                    ref="visibilityFormFieldRef"
                    label="Access"
                    fieldName="visibility"
                    :value="visibilityValue"
                    :displayValue="visibilityDisplayValue"
                    inputType="dropdown"
                    displayType="text"
                                :options="visibilityOptions"
                                optionLabel="label"
                                optionValue="value"
                    :isLoading="isLoading"
                    :isHorizontal="isWideMode"
                    :isSaving="isVisibilitySaving"
                    :isEditing="isEditing"
                    :iconClass="visibilityIconClass"
                    noValueText="No visibility set"
                    dataTestId="article-visibility"
                    @update="handleVisibilityUpdate"
                    @save="handleVisibilitySave"
                    @submit-start="() => {}"
                    @cancel="() => {}"
                />
                <TeamAccessFormInput
                    :article="article"
                    :isLoading="isLoading"
                    :isSaving="false"
                    :isHorizontal="isWideMode"
                    :isEditing="isEditing"
                    :onSubmit="submitArticleFieldUpdate"
                    @update="() => emit('update', article)"
                    data-testid="article-team-access-input"
                />
                <OrganizationAccessFormInput
                    :article="article"
                    :isLoading="isLoading"
                    :isSaving="false"
                    :isHorizontal="isWideMode"
                    :isEditing="isEditing"
                    :onSubmit="submitArticleFieldUpdate"
                    @update="() => emit('update', article)"
                    data-testid="article-organization-access-input"
                />
                <LibraryFormInput
                    :article="article"
                    :isLoading="isLoading"
                    :isSaving="false"
                    :isHorizontal="isWideMode"
                    :isEditing="isEditing"
                    :onSubmit="submitArticleFieldUpdate"
                    @update="(updatedArticle) => emit('update', updatedArticle)"
                    data-testid="article-library-input"
                />
                <LabelsFormInput
                    :article="article"
                    :isLoading="isLoading"
                    :isSaving="false"
                    :isHorizontal="isWideMode"
                    :isEditing="isEditing"
                    :onSubmit="submitArticleFieldUpdate"
                    @update="(updatedArticle) => emit('update', updatedArticle)"
                    data-testid="article-labels-input"
                />

                <TagFormInput
                    v-if="article.bc__tags_support"
                    :article="article"
                    :isLoading="isLoading"
                    :isSaving="false"
                    :isHorizontal="isWideMode"
                    :isEditing="isEditing"
                    :onSubmit="submitArticleFieldUpdate"
                    @update="() => emit('update', article)"
                    data-testid="article-tags-input"
                />
                <ProductFormInput
                    v-if="article.bc__tags_object_members_devices_dict"
                    :article="article"
                    :isLoading="isLoading"
                    :isSaving="false"
                    :isHorizontal="isWideMode"
                    :isEditing="isEditing"
                    :onSubmit="submitArticleFieldUpdate"
                    @update="() => emit('update', article)"
                    data-testid="article-products-input"
                />
                <SearchTermsFormInput
                    :article="article"
                    :isLoading="isLoading"
                    :isSaving="false"
                    :isHorizontal="isWideMode"
                    :isEditing="isEditing"
                    :onSubmit="submitArticleFieldUpdate"
                    @update="() => emit('update', article)"
                />
                <!-- related articles -->

            </div>
        </BravoAccordionContent>
    </BravoAccordionPanel>
    <!-- Library change modal moved to LibraryFormInput component -->
</template>

<style scoped>
.data-fields {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.data-field {
    display: flex;
    flex-direction: column;
}

/* This class will be applied when sidebar is wider */
.data-field.horizontal {
    flex-direction: row;
    align-items: flex-start;
    justify-content: flex-start;
    gap: 0.5rem;
}

.data-field.horizontal label,
.data-field.horizontal :deep(.bravo-label) {
    flex: 0 0 30%;
    margin-bottom: 0;
    padding-top: 4px;
}

.data-field.horizontal .field-value {
    flex: 0 0 70%;
}

.data-field label,
.data-field :deep(.bravo-label) {
    font-size: 0.75rem;
    color: #64748b;
    display: block;
    margin-bottom: 0.25rem;
}

.data-field .field-value {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.data-field .field-value i {
    color: var(--color-secondary);
    font-size: 1rem;
}

.skeleton-content {
    padding: 0.5rem 0;
}

.skeleton-content .p-skeleton {
    margin-bottom: 0.75rem;
}

.mb-2 {
    margin-bottom: 0.5rem;
}

/* Specific styling for BravoLabel */
.article-field-label {
    flex: 0 0 30%;
    font-size: 0.75rem;
    color: #64748b;
    display: block;
    margin-bottom: 0.25rem;
}

.data-field.horizontal .article-field-label {
    margin-bottom: 0;
    padding-top: 4px;
    align-items: flex-start;
}
</style>
