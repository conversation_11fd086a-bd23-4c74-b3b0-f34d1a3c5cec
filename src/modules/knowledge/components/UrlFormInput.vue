<script setup lang="ts">
import { computed, ref } from 'vue';
import BravoForm<PERSON>ield from './BravoFormField.vue';
import BravoLabel from '@services/ui-component-library/components/BravoLabel.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import { useI18n } from 'vue-i18n';
import { useToast } from 'primevue/usetoast';

const { t } = useI18n();
const toast = useToast();

// Define props
const props = defineProps<{
    article: any;
    isLoading?: boolean;
    isSaving?: boolean;
    isHorizontal?: boolean;
    isEditing?: boolean;
    onSubmit: (field: string, value: any) => Promise<boolean>;
}>();

// Define emits
const emit = defineEmits<{
    (e: 'update'): void;
}>();

// Refs
const formInputRef = ref<any>(null);
const isLocalSaving = ref(false);

// Computed values
const urlValue = computed(() => props.article.short_name || '');

const urlDisplayValue = computed(() => {
    return props.article.url
        ? '/' + getLastUrlSegment(props.article.url)
        : '/' + getLastUrlSegment(`/article/${props.article.id}`);
});

// Article URL with login parameter
const articleUrl = computed(() => {
    if (!props.article.url) return undefined;

    const url = new URL(props.article.url, window.location.origin);
    url.searchParams.set('auto_login', '1');
    return url.href;
});

// Helper to get last URL segment
const getLastUrlSegment = (url: string) => {
    if (!url) return '';
    const segments = url.split('/');
    return segments[segments.length - 1];
};

// Handle update
const handleUpdate = (fieldName: string, value: any) => {
    // No special handling needed - just pass through
};

// Handle save
const handleSave = async (fieldName: string, value: any) => {
    if (value !== props.article.short_name) {
        isLocalSaving.value = true;
        try {
            const success = await props.onSubmit('short_name', value);
            formInputRef.value?.handleSaveComplete(success);
            if (success) {
                emit('update');
            }
        } catch (error) {
            formInputRef.value?.handleSaveComplete(false);
        } finally {
            isLocalSaving.value = false;
        }
    } else {
        formInputRef.value?.handleSaveComplete(true);
    }
};

// Handle submit start
const handleSubmitStart = () => {
    isLocalSaving.value = true;
};

// Handle cancel
const handleCancel = () => {
    // Optional: add any cancel logic
};

// Start editing the URL field
const startEditing = () => {
    formInputRef.value?.startEditing();
};

// Handle copy URL to clipboard
const copyUrlToClipboard = async () => {
    if (!props.article.url) return;
    
    // Get clean URL without auto_login parameter
    const cleanUrl = props.article.url;
    
    try {
        await navigator.clipboard.writeText(cleanUrl);
        toast.add({
            severity: 'success',
            summary: 'Copied',
            detail: 'URL copied to clipboard',
            life: 2000
        });
    } catch (error) {
        // Fallback for older browsers
        try {
            const textArea = document.createElement('textarea');
            textArea.value = cleanUrl;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            
            toast.add({
                severity: 'success',
                summary: 'Copied',
                detail: 'URL copied to clipboard',
                life: 2000
            });
        } catch (fallbackError) {
            toast.add({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to copy URL to clipboard',
                life: 3000
            });
        }
    }
};
</script>

<template>
    <BravoFormField
        ref="formInputRef"
        label="URL"
        fieldName="url"
        :value="urlValue"
        :displayValue="urlDisplayValue"
        inputType="text"
        displayType="text"
        :isLoading="isLoading"
        :isHorizontal="isHorizontal"
        :isSaving="isLocalSaving || isSaving"
        :isEditing="isEditing"
        iconClass="pi pi-link"
        noValueText="No URL"
        dataTestId="article-url"
        @update="handleUpdate"
        @save="handleSave"
        @submit-start="handleSubmitStart"
        @cancel="handleCancel"
    >
        <template #display="{ displayValue }">
            <div v-if="articleUrl" class="url-display-container">
                <a
                    :href="articleUrl"
                    target="_blank"
                    class="url-link"
                    data-testid="article-url-link"
                    @click.stop
                >
                    {{ displayValue }}
                </a>
                <!-- Show different button based on edit state -->
                <BravoButton
                    v-if="!formInputRef?.isEditingInternal && isEditing"
                    icon="pi pi-pencil"
                    severity="secondary"
                    text
                    class="edit-url-button"
                    @click.stop="startEditing"
                    v-tooltip.top="{ value: 'Edit URL', showDelay: 300 }"
                    data-testid="edit-url-button"
                />
                <BravoButton
                    v-else-if="!formInputRef?.isEditingInternal && !isEditing"
                    icon="pi pi-copy"
                    severity="secondary"
                    text
                    class="copy-url-button"
                    @click.stop="copyUrlToClipboard"
                    v-tooltip.top="{ value: 'Copy URL to clipboard', showDelay: 300 }"
                    data-testid="copy-url-button"
                />
            </div>
            <span v-else class="no-url-text">
                No URL
            </span>
        </template>
    </BravoFormField>
</template>



<style scoped>
/* URL display container */
.url-display-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    width: 100%;
}

/* URL link styling within BravoFormField display slot */
.url-link {
    color: var(--blue-650);
    text-decoration: none;
    cursor: pointer;
    word-break: break-all;
    line-height: 1.4;
    transition: text-decoration 0.2s ease;
    flex: 1;
}

.url-link:hover {
    color: var(--blue-650);
    text-decoration: underline;
}

.url-link:visited {
    color: var(--blue-650);
}

/* Copy button styling */
.copy-url-button {
    opacity: 0;
    transition: opacity 0.2s ease;
    flex-shrink: 0;
    padding: 0.25rem;
    min-width: auto;
    width: 1.5rem;
    height: 1.5rem;
}

.url-display-container:hover .copy-url-button {
    opacity: 1;
}

.copy-url-button:hover {
    background-color: var(--surface-100);
}

/* Edit button styling */
.edit-url-button {
    opacity: 0;
    transition: opacity 0.2s ease;
    flex-shrink: 0;
    padding: 0.25rem;
    min-width: auto;
    width: 1.5rem;
    height: 1.5rem;
}

.url-display-container:hover .edit-url-button {
    opacity: 1;
}

.edit-url-button:hover {
    background-color: var(--surface-100);
}

.no-url-text {
    color: #9ca3af;
    font-style: italic;
}

/* Override BravoFormField's disabled pointer events for URL links and buttons */
:deep(.field-section-disabled .url-link),
:deep(.field-section-disabled .copy-url-button),
:deep(.field-section-disabled .edit-url-button) {
    pointer-events: auto !important;
    cursor: pointer !important;
}

:deep(.field-disabled .url-link),
:deep(.field-disabled .copy-url-button),
:deep(.field-disabled .edit-url-button) {
    pointer-events: auto !important;
    cursor: pointer !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .url-link {
        font-size: 0.875rem;
    }
    
    .copy-url-button,
    .edit-url-button {
        width: 1.75rem;
        height: 1.75rem;
    }
}
</style> 