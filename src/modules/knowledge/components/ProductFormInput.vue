<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import BravoFormField from './BravoFormField.vue';
import { usePartnerStore } from '@/stores/partner';

// Define props
const props = defineProps<{
  article: any;
  isLoading?: boolean;
  isSaving?: boolean;
  isHorizontal?: boolean;
  isEditing?: boolean;
  onSubmit?: (field: string, value: any) => Promise<boolean>;
}>();

// Define emits
const emit = defineEmits<{
  (e: 'update', article: any): void;
}>();

// Get partner store for products data
const partnerStore = usePartnerStore();

// Helper to get product name from ID
const getProductNameFromId = (id: string) => {
  const product = partnerStore.products.find(p => p.dict_id === id);
  return product ? product.c__lbl : id;
};

// Compute the display values for the products
const productDisplayValues = computed(() => {
  if (!partnerStore.products || partnerStore.products.length === 0) return [];
  const productIds = props.article.bc__tags_object_members_devices_dict || [];
  return productIds.map((id: string) => getProductNameFromId(id));
  
});

// Compute if there are any products to display
const hasProductRestrictions = computed(() => {
  return props.article.bc__tags_object_members_devices_dict && props.article.bc__tags_object_members_devices_dict.length > 0;
});

// Transform products for the dropdown - map from dict_id to id for the MultiSelect
const transformedProductOptions = computed(() => {
  return partnerStore.products.map(product => ({
    id: product.id,
    dict_id: product.dict_id,
    c__lbl: product.c__lbl
  }));
});

// Track the edited value locally
const editedProducts = ref<string[]>([]);

// Initialize the edited products when the article or products change
watch(
  [() => props.article.bc__tags_object_members_devices_dict, () => partnerStore.products],
  ([newProducts, products]) => {
    if (!products || products.length === 0) return; // Wait for products to load
    editedProducts.value = (newProducts || []).map((dictId: string) => {
      const product = products.find((p: any) => p.dict_id === dictId);
      return product?.id || dictId;
    });
  },
  { immediate: true }
);

// Track loading state locally
const isLocalSaving = ref(false);

// Reference to the BravoFormField component
const formInputRef = ref<InstanceType<typeof BravoFormField> | null>(null);

// Handle the update event from BravoFormField
const handleUpdate = (fieldName: string, value: any) => {
  editedProducts.value = value;
};

// Handle submission start
const handleSubmitStart = () => {
  isLocalSaving.value = true;
};

// Handle the save event from BravoFormField
const handleSave = async (fieldName: string, value: any) => {
  try {
    // Map back from id to dict_id for storage
    const newDictIds = value.map((id: string) => {
      const product = partnerStore.products.find(p => p.id === id);
      return product?.dict_id || id;
    });

    // Check if the arrays are different
    const currentProductIds = props.article.bc__tags_object_members_devices_dict || [];
    const isChanged = 
      newDictIds.length !== currentProductIds.length ||
      newDictIds.some((id: string) => !currentProductIds.includes(id)) ||
      currentProductIds.some((id: string) => !newDictIds.includes(id));
      
    // Only submit if the values have changed
    if (isChanged && props.onSubmit) {
      const success = await props.onSubmit('bc__tags_object_members_devices_dict', newDictIds);
      formInputRef.value?.handleSaveComplete(success);
    } else {
      // No change needed, just complete
      formInputRef.value?.handleSaveComplete(true);
    }
  } catch (error) {
    console.error('Error saving products:', error);
    formInputRef.value?.handleSaveComplete(false);
  } finally {
    isLocalSaving.value = false;
  }
};

// Handle cancel event
const handleCancel = () => {
  // Reset the edited value back to the original
  editedProducts.value = (props.article.bc__tags_object_members_devices_dict || []).map((dictId: string) => {
    const product = partnerStore.products.find(p => p.dict_id === dictId);
    return product?.id || dictId;
  });
};

// Only show the component if the article has the bc__tags_object_members_devices_dict field
const showComponent = computed(() => {
  return !!props.article.bc__tags_object_members_devices_dict;
});
</script>

<template>
  <BravoFormField
    ref="formInputRef"
    v-if="showComponent"
    label="Products"
    fieldName="products"
    :value="editedProducts"
    :displayValue="productDisplayValues"
    inputType="multiselect"
    displayType="chips"
    :options="transformedProductOptions"
    optionLabel="c__lbl"
    optionValue="id"
    :isLoading="isLoading || partnerStore.loading"
    :isHorizontal="isHorizontal"
    :isSaving="isLocalSaving || isSaving"
    :isEditing="isEditing"
    iconClass="pi pi-desktop"
    noValueText="No products"
    dataTestId="article-products"
    showFilter
    filterPlaceholder="Search products..."
    @update="handleUpdate"
    @save="handleSave"
    @submit-start="handleSubmitStart"
    @cancel="handleCancel"
  />
</template> 