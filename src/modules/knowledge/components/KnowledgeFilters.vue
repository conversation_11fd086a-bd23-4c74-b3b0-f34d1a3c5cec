<!-- KnowledgeFilters.vue -->
<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import BravoInputText from '@services/ui-component-library/components/BravoInputText.vue';
import BravoIconField from '@services/ui-component-library/components/BravoIconField.vue';
import BravoInputIcon from '@services/ui-component-library/components/BravoInputIcon.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import BravoMenu from '@services/ui-component-library/components/BravoMenu.vue';
import BravoSelect from '@services/ui-component-library/components/BravoSelectField.vue';
import BravoMultiSelect from '@services/ui-component-library/components/BravoMultiSelect.vue';
import BravoDatePicker from '@services/ui-component-library/components/BravoDatePicker.vue';
import FilterDropdown from './FilterDropdown.vue';
import { useI18n } from 'vue-i18n';
import ArchiveArticlesDialog from './dialogs/ArchiveArticles.vue';
import UnpublishArticlesDialog from './dialogs/UnpublishArticles.vue';
import PublishArticlesDialog from './dialogs/PublishArticles.vue';
import ShareArticlesDialog from './dialogs/ShareArticles.vue';
import UnshareArticlesDialog from './dialogs/UnshareArticles.vue';
import UpdateArticlesTagDialog from './dialogs/UpdateArticlesTag.vue';
import UpdateProductsDialog from './dialogs/UpdateProducts.vue';
import UpdateAccessControlsDialog from './dialogs/UpdateAccessControls.vue';
import UpdateLabelsDialog from './dialogs/UpdateLabels.vue';
import { useKnowledgeStore } from '../stores/knowledge';
import { usePermissions } from '@/composables/usePermissions';
import { usePartnerStore } from '@/stores/partner';
import { useUsersStore } from '@/stores/users';
import { useKnowledgeAPI } from '@/composables/services/useKnowledgeAPI';
import { onMounted } from 'vue';

const props = defineProps<{
  isLoading?: boolean;
  currentPage?: number;
  rowsPerPage?: number;
  sortField?: string;
  sortOrder?: number;
  selectedArticlesCount?: number;
  selectedArticles?: any[];
}>();

const emit = defineEmits<{
  (e: 'search', params: any): void; 
  (e: 'reset'): void;
}>();

const { t } = useI18n();
const router = useRouter();
const route = useRoute();
const store = useKnowledgeStore();
const partnerStore = usePartnerStore();
const usersStore = useUsersStore();
const knowledgeAPI = useKnowledgeAPI();
const { can } = usePermissions();
// Search related
const searchInputValue = ref('');
const searchDelayTimeout = ref<number | null>(null);
const isUpdatingFromUrl = ref(false);

// Status and Access filter
const activeFilters = ref<string[]>([]);
const statusValues = ref<string[]>([]);
const accessValues = ref<string[]>([]);
const libraryValues = ref<string | null>(null);
const labelValues = ref<string | null>(null);
const productValues = ref<string[]>([]);
const tagValues = ref<string[]>([]);
const hasDraftValue = ref<string | null>(null);
const filterMenu = ref();
const actionsMenu = ref();

// Filter types for the dropdown
const filterTypes = [
  { label: 'Status', value: 'status', icon: 'pi pi-tag' },
  { label: 'Has Draft', value: 'hasDraft', icon: 'pi pi-file-edit' },
  { label: 'Access', value: 'access', icon: 'pi pi-lock' },
  { label: 'Library', value: 'library', icon: 'pi pi-book' },
  { label: 'Label', value: 'label', icon: 'pi pi-tag' },
  { label: 'Tags', value: 'tags', icon: 'pi pi-tags' },
  { label: 'Products', value: 'products', icon: 'pi pi-desktop' }
];

// Filter options
const statusOptions = [
  { label: 'Draft', value: '1' },
  { label: 'Published', value: '0' },
  { label: 'Archived', value: '98' }
];

const accessOptions = [
  { label: 'Internal', value: 'internal' },
  { label: 'Public', value: 'public' },
  { label: 'Ecosystem', value: 'private' }
];

// Library and Label options - populated from store
const libraryOptions = computed(() => {
  const options = [];
  // Get libraries from tree nodes (top-level items)
  for (const node of store.treeNodes) {
    if (node.isLibrary) {
      options.push({
        label: node.text || 'Untitled Library',
        value: node.id
      });
    }
  }
  return options;
});

const labelOptions = computed(() => {
  const options: Array<{ label: string; value: string }> = [];
  
  if (libraryValues.value) {
    // If a library is selected, show only labels for that library
    const selectedLibrary = store.treeNodes.find(node => node.id === libraryValues.value && node.isLibrary);
    
    if (selectedLibrary && selectedLibrary.children) {
      // Get labels that belong to the selected library with full path
      const addLabelsFromNode = (node: any, parentPath: string = '') => {
        // Add this node if it's a label (not a library)
        if (!node.isLibrary && node.text) {
          const currentPath = parentPath ? `${parentPath} / ${node.text}` : node.text;
          options.push({
            label: currentPath,
            value: node.id
          });
        }
        
        // Recursively add children labels
        if (node.children) {
          const currentPath = parentPath ? `${parentPath} / ${node.text}` : node.text;
          node.children.forEach((child: any) => addLabelsFromNode(child, currentPath));
        }
      };
      
              selectedLibrary.children.forEach((child: any) => addLabelsFromNode(child));
    }
  } else {
    // If no library is selected, show all labels from all libraries with full path
    const addLabelsFromNode = (node: any, parentPath: string = '') => {
      // Add this node if it's a label (not a library)
      if (!node.isLibrary && node.text) {
        const currentPath = parentPath ? `${parentPath} / ${node.text}` : node.text;
        options.push({
          label: currentPath,
          value: node.id
        });
      }
      
      // Recursively add children labels
      if (node.children) {
        const currentPath = parentPath ? `${parentPath} / ${node.text}` : node.text;
        node.children.forEach((child: any) => addLabelsFromNode(child, currentPath));
      }
    };
    
    // Iterate through all libraries and their children
    store.treeNodes.forEach(libraryNode => {
      if (libraryNode.isLibrary && libraryNode.children) {
        libraryNode.children.forEach((child: any) => addLabelsFromNode(child, libraryNode.text));
      }
    });
  }
  
  // Sort options by label for better UX
  return options.sort((a, b) => a.label.localeCompare(b.label));
});

// Products options - we'll load these from the knowledge API
const productOptions = ref<Array<{ label: string; value: string }>>([]);

// Tags options - we'll need to load these from the API
const tagOptions = ref<Array<{ label: string; value: string }>>([]);



// Has Draft options
const hasDraftOptions = [
  { label: 'Yes', value: 'true' },
  { label: 'No', value: 'false' }
];

// Computed to show active filter count
const activeFilterCount = computed(() => {
  let count = 0;
  if (statusValues.value.length > 0) count++;
  if (accessValues.value.length > 0) count++;
  if (libraryValues.value) count++;
  if (labelValues.value) count++;
  if (productValues.value.length > 0) count++;
  if (tagValues.value.length > 0) count++;
  if (hasDraftValue.value) count++;
  return count;
});

const menuItems: { label: string; value: string; icon: string; command: () => void }[] = [];

if (can.editArticle()) {
  menuItems.push(...[{
    label: t('knowledge.actionsMenu.update_labels'),
    value: 'label',
    icon: 'pi pi-tag',
    command: () => onUpdateActionClick('label')
  },
  {
    label: t('knowledge.actionsMenu.update_access_controls'),
    value: 'access',
    icon: 'pi pi-lock',
    command: () => onUpdateActionClick('access')
  },
  {
    label: t('knowledge.actionsMenu.add_rem_products'),
    value: 'products',
    icon: 'pi pi-box',
    command: () => onUpdateActionClick('products')
  },
  {
    label: t('knowledge.actionsMenu.add_rem_tags'),
    value: 'tags',
    icon: 'pi pi-tags',
    command: () => onUpdateActionClick('tags')
  },
  {
    label: t('knowledge.actionsMenu.share_with_orgs'),
    value: 'share',
    icon: 'pi pi-share-alt',
    command: () => onUpdateActionClick('share')
  },
  {
    label: t('knowledge.actionsMenu.unshare_from_orgs'),
    value: 'unshare',
    icon: 'pi pi-share-alt',
    command: () => onUpdateActionClick('unshare')
  }]);
}

if (can.publishArticle()) {
  menuItems.push(...[{
    label: t('knowledge.actionsMenu.publish'),
    value: 'publish',
    icon: 'pi pi-check-square',
    command: () => onUpdateActionClick('publish')
  },
  {
    label: t('knowledge.actionsMenu.unpublish'),
    value: 'unpublish',
    icon: 'pi pi-times-circle',
    command: () => onUpdateActionClick('unpublish')
  }]);
}

if (can.deleteArticle()) {
  menuItems.push(...[{
    label: t('knowledge.actionsMenu.archive'),
    value: 'archive',
    icon: 'pi pi-inbox',
    command: () => onUpdateActionClick('archive')
  }]);
}

const actionsMenuItems = computed(() => {
  return menuItems;
});

// Dialog visibility states
const showLabelsDialog = ref(false);
const showAccessControlsDialog = ref(false);
const showProductsDialog = ref(false);
const showTagsDialog = ref(false);
const showShareDialog = ref(false);
const showUnshareDialog = ref(false);
const showPublishDialog = ref(false);
const showUnpublishDialog = ref(false);
const showArchiveDialog = ref(false);

// Handle label update action
const handleLabelUpdate = (labels: string[]) => {
  setTimeout(() => {
    performSearch();
  }, 1000);
};

// Handle access controls update
const handleAccessControlsUpdate = (data: { accessLevel: string; teamAccessTo: string[] }) => {
  performSearch();
};

// Handle products update
const handleProductsUpdate = (products: string[]) => {
  performSearch();
};

// Handle tags update
const handleTagsUpdate = (tags: string[]) => {
  performSearch();
};

// Handle share articles
const handleShareArticles = (organizations: string[]) => {
  performSearch();
};

// Handle unshare articles
const handleUnshareArticles = (organizations: string[]) => {
  performSearch();
};

// Handle publish articles
const handlePublishArticles = () => {
  performSearch();
};

// Handle unpublish articles
const handleUnpublishArticles = () => {
  performSearch();
};

// Handle archive articles
const handleArchiveArticles = () => {
  performSearch();
};

// Update onUpdateActionClick function
const onUpdateActionClick = (action: string) => {
  switch (action) {
    case 'label':
      showLabelsDialog.value = true;
      break;
    case 'access':
      showAccessControlsDialog.value = true;
      break;
    case 'products':
      showProductsDialog.value = true;
      break;
    case 'tags':
      showTagsDialog.value = true;
      break;
    case 'share':
      showShareDialog.value = true;
      break;
    case 'unshare':
      showUnshareDialog.value = true;
      break;
    case 'publish':
      showPublishDialog.value = true;
      break;
    case 'unpublish':
      showUnpublishDialog.value = true;
      break;
    case 'archive':
      showArchiveDialog.value = true;
      break;
  }
};

// Function to show filter dropdown menu
const showFilterMenu = (event: Event) => {
  filterMenu.value.toggle(event);
};

// Function to show actions menu
const showActionsMenu = (event: Event) => {
  if (actionsMenuItems.value.length) {
    actionsMenu.value.toggle(event);
  }
};

// Function to show specific filter
const addFilter = (filterType: string) => {
  if (!activeFilters.value.includes(filterType)) {
    activeFilters.value.push(filterType);
  }
  filterMenu.value.toggle();
};

// Function to hide specific filter
const removeFilter = (filterType: string) => {
  // Remove the filter type from active filters
  activeFilters.value = activeFilters.value.filter(f => f !== filterType);
  
  // Trigger search with updated filters
  performSearch();
};

// Function to remove all filters and update URL
const clearAllFilters = () => {
  activeFilters.value = [];
  statusValues.value = [];
  accessValues.value = [];
  libraryValues.value = null;
  labelValues.value = null;
  productValues.value = [];
  tagValues.value = [];
  hasDraftValue.value = null;
  searchInputValue.value = '';
  
  // After clearing all filters, refresh the results
  emit('reset');
};

// Update URL params
const updateUrlParams = (params: any) => {
  // Skip URL update if we're currently updating from the URL
  if (isUpdatingFromUrl.value) return;
  
  const query: Record<string, any> = { ...route.query };
  
  // Update search query param
  if (searchInputValue.value) {
    query.q = searchInputValue.value;
  } else {
    delete query.q;
  }
  
  // Update status filter param
  if (statusValues.value.length > 0) {
    query.status = statusValues.value.length === 1 ? statusValues.value[0] : statusValues.value;
  } else {
    delete query.status;
  }
  
  // Update access filter param
  if (accessValues.value.length > 0) {
    query.access = accessValues.value.length === 1 ? accessValues.value[0] : accessValues.value;
  } else {
    delete query.access;
  }
  
  // Update library filter param
  if (libraryValues.value) {
    query.library = libraryValues.value;
  } else {
    delete query.library;
  }
  
  // Update label filter param
  if (labelValues.value) {
    query.label = labelValues.value;
  } else {
    delete query.label;
  }
  
  // Update products filter param
  if (productValues.value.length > 0) {
    query.products = productValues.value.length === 1 ? productValues.value[0] : productValues.value;
  } else {
    delete query.products;
  }
  
  // Update tags filter param
  if (tagValues.value.length > 0) {
    query.tags = tagValues.value.length === 1 ? tagValues.value[0] : tagValues.value;
  } else {
    delete query.tags;
  }
  

  
  // Update has draft filter param
  if (hasDraftValue.value) {
    query.hasDraft = hasDraftValue.value;
  } else {
    delete query.hasDraft;
  }
  

  
  // Update pagination params - always include page parameter
  query.page = String(props.currentPage || 1);
  
  if (props.rowsPerPage && props.rowsPerPage !== 25) { // Only include if not default
    query.limit = props.rowsPerPage.toString();
  } else {
    delete query.limit;
  }
  
  // Update sorting params - only if a sort field is specified
  if (props.sortField && props.sortField !== '') {
    query.sortField = props.sortField;
    query.sortOrder = props.sortOrder?.toString() || '1';
    
    // For debugging - this shows the exact sort format used in the API call
    console.log('Sort in URL:', { 
      field: props.sortField, 
      order: props.sortOrder, 
      apiFormat: JSON.stringify([{
        property: props.sortField,
        direction: props.sortOrder === 1 ? 'ASC' : 'DESC'
      }])
    });
  } else {
    delete query.sortField;
    delete query.sortOrder;
  }
  
  // Replace the current URL with the new params
  router.replace({ query });
};

// Handle search input with debounce
const handleSearchInput = (event: Event) => {
  // Clear any existing timeouts
  if (searchDelayTimeout.value !== null) {
    clearTimeout(searchDelayTimeout.value);
  }
  
  // Set a new timeout for debounce (500ms)
  searchDelayTimeout.value = window.setTimeout(() => {
    performSearch();
  }, 500);
};

// Handle search on enter key
const handleSearchKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    if (searchDelayTimeout.value !== null) {
      clearTimeout(searchDelayTimeout.value);
      searchDelayTimeout.value = null;
    }
    performSearch();
  }
};

// Clear search function
const clearSearch = () => {
  searchInputValue.value = '';
  performSearch();
};

// Perform search with all filters
const performSearch = () => {
  const apiParams: any = {
    filter: [
      { property: "type", value: store.selectedContentType === 'templates' ? 1 : 0 },
      { property: "id", value: "_no_filter_" },
      { property: "parent_id", value: null },
      { property: "root_parent_id", value: null },
      { property: "partner_ids", operator: "intersect_set", value: null },
      { property: "visibility", operator: "=", value: null },
      { property: "status", operator: "=", value: null },
      { property: "dict_id", value: null },
      { property: "tag_ids", value: null }
    ]
  };
  
  // Add query parameter if there is a search term
  if (searchInputValue.value.trim()) {
    apiParams.query = searchInputValue.value.trim();
  }
  
  // Add status filter if values are selected
  if (statusValues.value && statusValues.value.length > 0) {
    // Find and update the status filter
    const statusIndex = apiParams.filter.findIndex((f: any) => f.property === "status");
    if (statusIndex >= 0) {
      if (statusValues.value.length === 1) {
        // For single value, use "=" operator
        apiParams.filter[statusIndex].operator = "=";
        // Keep status value as string
        apiParams.filter[statusIndex].value = statusValues.value[0];
      } else {
        // For multiple values, use "in" operator
        apiParams.filter[statusIndex].operator = "in";
        // Keep status values as strings for the API
        apiParams.filter[statusIndex].value = statusValues.value;
      }
    }
  }
  
  // Add access/visibility filter if values are selected
  if (accessValues.value && accessValues.value.length > 0) {
    // Find and update the visibility filter
    const visibilityIndex = apiParams.filter.findIndex((f: any) => f.property === "visibility");
    if (visibilityIndex >= 0) {
      if (accessValues.value.length === 1) {
        // For single value, use "=" operator
        apiParams.filter[visibilityIndex].operator = "=";
        apiParams.filter[visibilityIndex].value = accessValues.value[0];
      } else {
        // For multiple values, use "in" operator
        apiParams.filter[visibilityIndex].operator = "in";
        apiParams.filter[visibilityIndex].value = accessValues.value;
      }
    }
  }
  
  // Add library filter if values are selected
  if (libraryValues.value) {
    // Find and update the root_parent_id filter (which represents library)
    const rootParentIndex = apiParams.filter.findIndex((f: any) => f.property === "root_parent_id");
    if (rootParentIndex >= 0) {
      apiParams.filter[rootParentIndex].operator = "=";
      apiParams.filter[rootParentIndex].value = libraryValues.value;
    }
  }
  
  // Add label filter if values are selected
  if (labelValues.value) {
    // Find and update the parent_id filter (which represents label)
    const parentIndex = apiParams.filter.findIndex((f: any) => f.property === "parent_id");
    if (parentIndex >= 0) {
      apiParams.filter[parentIndex].operator = "=";
      apiParams.filter[parentIndex].value = labelValues.value;
    }
  }
  
  // Add products filter if values are selected
  if (productValues.value && productValues.value.length > 0) {
    // Find and update the dict_id filter (which represents products)
    const dictIndex = apiParams.filter.findIndex((f: any) => f.property === "dict_id");
    if (dictIndex >= 0) {
      if (productValues.value.length === 1) {
        apiParams.filter[dictIndex].operator = "=";
        apiParams.filter[dictIndex].value = productValues.value[0];
      } else {
        apiParams.filter[dictIndex].operator = "in";
        apiParams.filter[dictIndex].value = productValues.value;
      }
    }
  }
  
  // Add tags filter if values are selected
  if (tagValues.value && tagValues.value.length > 0) {
    // Find and update the tag_ids filter
    const tagIndex = apiParams.filter.findIndex((f: any) => f.property === "tag_ids");
    if (tagIndex >= 0) {
      if (tagValues.value.length === 1) {
        apiParams.filter[tagIndex].operator = "=";
        apiParams.filter[tagIndex].value = tagValues.value[0];
      } else {
        apiParams.filter[tagIndex].operator = "in";
        apiParams.filter[tagIndex].value = tagValues.value;
      }
    }
  }
  
  // Add has draft filter if value is selected
  if (hasDraftValue.value) {
    // Add a new filter for has_draft
    apiParams.filter.push({
      property: "has_draft",
      operator: "=",
      value: hasDraftValue.value === 'true'
    });
  }
  

  
  // Add pagination parameters - ensure these are numbers
  const page = props.currentPage || 1;
  const limit = props.rowsPerPage || 25;
  
  apiParams.page = page;
  apiParams.start = (page - 1) * limit;
  apiParams.limit = limit;
  
  // Add sorting parameters if available
  if (props.sortField && props.sortField !== '') {
    // Format sort parameter exactly as shown in the example
    apiParams.sort = JSON.stringify([{
      property: props.sortField,
      direction: props.sortOrder === 1 ? 'ASC' : 'DESC'
    }]);
  }
  
  // Update URL params
  updateUrlParams(apiParams);
  
  // Emit search event with parameters
  emit('search', apiParams);
};

// Load params from URL
const loadParamsFromUrl = () => {
  // Set the flag to indicate we're updating from URL
  isUpdatingFromUrl.value = true;
  
  try {
    // Get search query from URL
    if (route.query.q) {
      searchInputValue.value = route.query.q as string;
    } else {
      searchInputValue.value = '';
    }
    
    // Get status filter from URL
    if (route.query.status) {
      const statusParam = route.query.status;
      if (Array.isArray(statusParam)) {
        statusValues.value = statusParam.map(s => String(s));
      } else {
        statusValues.value = [String(statusParam)];
      }
      
      if (!activeFilters.value.includes('status')) {
        activeFilters.value.push('status');
      }
    } else {
      statusValues.value = [];
      activeFilters.value = activeFilters.value.filter(f => f !== 'status');
    }
    
    // Get access filter from URL
    if (route.query.access) {
      const accessParam = route.query.access;
      if (Array.isArray(accessParam)) {
        accessValues.value = accessParam.map(a => String(a));
      } else {
        accessValues.value = [String(accessParam)];
      }
      
      if (!activeFilters.value.includes('access')) {
        activeFilters.value.push('access');
      }
    } else {
      accessValues.value = [];
      activeFilters.value = activeFilters.value.filter(f => f !== 'access');
    }
    
         // Get library filter from URL
     if (route.query.library) {
       const libraryParam = route.query.library;
       libraryValues.value = String(libraryParam);
       
       if (!activeFilters.value.includes('library')) {
         activeFilters.value.push('library');
       }
       
       // Update store state for sidebar synchronization
       store.setSelectedLibraryId(String(libraryParam));
     } else {
       libraryValues.value = null;
       activeFilters.value = activeFilters.value.filter(f => f !== 'library');
       
       // Clear library selection in store if no URL param
       if (!route.query.label) {
         store.setSelectedLibraryId(null);
       }
     }
     
     // Get label filter from URL
     if (route.query.label) {
       const labelParam = route.query.label;
       labelValues.value = String(labelParam);
       
       if (!activeFilters.value.includes('label')) {
         activeFilters.value.push('label');
       }
       
       // Update store state for sidebar synchronization
       store.setSelectedLabelId(String(labelParam));
       
       // If we have a label but no library, we need to find the library for this label
       if (!route.query.library) {
         // Find the library ID for this label from the labelMap or treeNodes
         const findLibraryForLabel = (labelId: string) => {
           for (const node of store.treeNodes) {
             if (node.isLibrary && node.children) {
               const hasLabel = (children: any[]): boolean => {
                 return children.some(child => {
                   if (child.id === labelId) return true;
                   if (child.children) return hasLabel(child.children);
                   return false;
                 });
               };
               
               if (hasLabel(node.children)) {
                 return node.id;
               }
             }
           }
           return null;
         };
         
         const parentLibraryId = findLibraryForLabel(String(labelParam));
         if (parentLibraryId) {
           libraryValues.value = parentLibraryId;
           if (!activeFilters.value.includes('library')) {
             activeFilters.value.push('library');
           }
           store.setSelectedLibraryId(parentLibraryId);
         }
       }
     } else {
       labelValues.value = null;
       activeFilters.value = activeFilters.value.filter(f => f !== 'label');
       
       // Clear label selection in store
       store.setSelectedLabelId(null);
     }
     
     // Get products filter from URL
     if (route.query.products) {
       const productsParam = route.query.products;
       if (Array.isArray(productsParam)) {
         productValues.value = productsParam.map(p => String(p));
       } else {
         productValues.value = [String(productsParam)];
       }
       
       if (!activeFilters.value.includes('products')) {
         activeFilters.value.push('products');
       }
     } else {
       productValues.value = [];
       activeFilters.value = activeFilters.value.filter(f => f !== 'products');
     }
     
     // Get tags filter from URL
     if (route.query.tags) {
       const tagsParam = route.query.tags;
       if (Array.isArray(tagsParam)) {
         tagValues.value = tagsParam.map(t => String(t));
       } else {
         tagValues.value = [String(tagsParam)];
       }
       
       if (!activeFilters.value.includes('tags')) {
         activeFilters.value.push('tags');
       }
     } else {
       tagValues.value = [];
       activeFilters.value = activeFilters.value.filter(f => f !== 'tags');
     }
     
     // Get has draft filter from URL
     if (route.query.hasDraft) {
       hasDraftValue.value = String(route.query.hasDraft);
       
       if (!activeFilters.value.includes('hasDraft')) {
         activeFilters.value.push('hasDraft');
       }
     } else {
       hasDraftValue.value = null;
       activeFilters.value = activeFilters.value.filter(f => f !== 'hasDraft');
     }
     


  } finally {
    // Reset the flag when we're done
    isUpdatingFromUrl.value = false;
  }
};

// Watch for filter changes
watch(() => statusValues.value, () => {
  if (statusValues.value.length === 0 && activeFilters.value.includes('status')) {
    // Don't remove status from active filters when deselecting all values
    // We still want to show the empty filter
  } else if (statusValues.value.length > 0 && !activeFilters.value.includes('status')) {
    // Add status to active filters when selecting values
    activeFilters.value.push('status');
  }
}, { deep: true });

watch(() => accessValues.value, () => {
  if (accessValues.value.length === 0 && activeFilters.value.includes('access')) {
    // Don't remove access from active filters when deselecting all values
    // We still want to show the empty filter
  } else if (accessValues.value.length > 0 && !activeFilters.value.includes('access')) {
    // Add access to active filters when selecting values
    activeFilters.value.push('access');
  }
}, { deep: true });

watch(() => libraryValues.value, (newValue, oldValue) => {
  if (!newValue && activeFilters.value.includes('library')) {
    // Remove library from active filters when value is cleared (including via clear icon)
    activeFilters.value = activeFilters.value.filter(f => f !== 'library');
    store.setSelectedLibraryId(null);
    
    // Clear label selection when library is cleared
    if (labelValues.value) {
      labelValues.value = null;
      activeFilters.value = activeFilters.value.filter(f => f !== 'label');
      store.setSelectedLabelId(null);
    }
    
    performSearch(); // Trigger search when cleared
  } else if (newValue && !activeFilters.value.includes('library')) {
    // Add library to active filters when selecting a value
    activeFilters.value.push('library');
    store.setSelectedLibraryId(newValue);
  } else if (newValue) {
    // Update store even if filter was already active
    store.setSelectedLibraryId(newValue);
    
    // Clear label selection when library changes (unless we're loading from URL)
    if (oldValue && oldValue !== newValue && !isUpdatingFromUrl.value && labelValues.value) {
      labelValues.value = null;
      activeFilters.value = activeFilters.value.filter(f => f !== 'label');
      store.setSelectedLabelId(null);
    }
  }
});

watch(() => labelValues.value, (newValue) => {
  if (!newValue && activeFilters.value.includes('label')) {
    // Remove label from active filters when value is cleared (including via clear icon)
    activeFilters.value = activeFilters.value.filter(f => f !== 'label');
    store.setSelectedLabelId(null);
    performSearch(); // Trigger search when cleared
  } else if (newValue && !activeFilters.value.includes('label')) {
    // Add label to active filters when selecting a value
    activeFilters.value.push('label');
    store.setSelectedLabelId(newValue);
  } else if (newValue) {
    // Update store even if filter was already active
    store.setSelectedLabelId(newValue);
  }
});

// Watch for new filter changes
watch(() => productValues.value, () => {
  if (productValues.value.length === 0 && activeFilters.value.includes('products')) {
    // Don't remove products from active filters when deselecting all values
  } else if (productValues.value.length > 0 && !activeFilters.value.includes('products')) {
    activeFilters.value.push('products');
  }
}, { deep: true });

watch(() => tagValues.value, () => {
  if (tagValues.value.length === 0 && activeFilters.value.includes('tags')) {
    // Don't remove tags from active filters when deselecting all values
  } else if (tagValues.value.length > 0 && !activeFilters.value.includes('tags')) {
    activeFilters.value.push('tags');
  }
}, { deep: true });

watch(() => hasDraftValue.value, (newValue) => {
  if (!newValue && activeFilters.value.includes('hasDraft')) {
    activeFilters.value = activeFilters.value.filter(f => f !== 'hasDraft');
    performSearch();
  } else if (newValue && !activeFilters.value.includes('hasDraft')) {
    activeFilters.value.push('hasDraft');
  }
});



// Watch for store changes to update sidebar selection
watch(() => [store.selectedLibraryId, store.selectedLabelId], ([libraryId, labelId]) => {
  // Update selectedNodeKey to reflect the current selection
  if (labelId) {
    // If a label is selected, highlight the label in the sidebar
    store.selectedNodeKey = { [labelId]: true };
  } else if (libraryId) {
    // If only a library is selected, highlight the library in the sidebar
    store.selectedNodeKey = { [libraryId]: true };
  } else {
    // Clear selection if neither is selected
    store.selectedNodeKey = {};
  }
});

// Watch for props changes to update search
const propsChangeTimeout = ref<number | null>(null);

watch(() => [props.currentPage, props.rowsPerPage, props.sortField, props.sortOrder], () => {
  // Only trigger a search if we're not currently loading from URL
  if (!isUpdatingFromUrl.value) {
    // Clear any existing timeout to prevent duplicate calls
    if (propsChangeTimeout.value !== null) {
      clearTimeout(propsChangeTimeout.value);
    }
    
    // Set a new timeout for a very small debounce (50ms)
    // This helps prevent duplicate calls when multiple props change at once
    propsChangeTimeout.value = window.setTimeout(() => {
      performSearch();
      propsChangeTimeout.value = null;
    }, 50);
  }
}, { deep: true });

// Load filter options data
const loadProductsOptions = async () => {
  try {
    const response = await knowledgeAPI.loadProducts({
      sAction: 'listingTemplate',
      page: '1',
      start: '0',
      limit: '1000'
    });
    
    if (response?.partners_product_list?.results) {
      productOptions.value = response.partners_product_list.results.map((item: any) => ({
        label: item.c__lbl || item.name || 'Unnamed Product',
        value: item.dict_id || item.id
      }));
    }
  } catch (error) {
    console.error('Error loading products:', error);
  }
};

const loadTagsOptions = async () => {
  try {
    // Note: This is a placeholder - you may need to implement a specific API call for tags
    // For now, we'll leave this empty and you can implement based on your API structure
    console.log('Tags loading not yet implemented - please implement based on your API');
  } catch (error) {
    console.error('Error loading tags:', error);
  }
};

const loadUsersOptions = async () => {
  try {
    await usersStore.fetchUsers();
  } catch (error) {
    console.error('Error loading users:', error);
  }
};

// Initialize component
onMounted(async () => {
  // Load filter options
  await Promise.all([
    loadProductsOptions(),
    loadTagsOptions(),
    loadUsersOptions()
  ]);
  
  // Load URL parameters after data is loaded
  loadParamsFromUrl();
});

// Watch for tree data to be available and update sidebar selection on initial load
watch(() => store.treeNodes.length, (newLength) => {
  if (newLength > 0 && (libraryValues.value || labelValues.value)) {
    // Tree data is now available, update the sidebar selection
    const nodeIdToSelect = labelValues.value || libraryValues.value;
    if (nodeIdToSelect) {
      store.selectedNodeKey = { [nodeIdToSelect]: true };
    }
  }
}, { immediate: true });

// Methods to update filters programmatically (for sidebar integration)
const updateLibraryFilter = (libraryId: string | null) => {
  libraryValues.value = libraryId;
  if (libraryId && !activeFilters.value.includes('library')) {
    activeFilters.value.push('library');
  } else if (!libraryId) {
    activeFilters.value = activeFilters.value.filter(f => f !== 'library');
  }
  performSearch();
};

const updateLabelFilter = (labelId: string | null) => {
  labelValues.value = labelId;
  if (labelId && !activeFilters.value.includes('label')) {
    activeFilters.value.push('label');
  } else if (!labelId) {
    activeFilters.value = activeFilters.value.filter(f => f !== 'label');
  }
  performSearch();
};

// Combined method to update both library and label filters with a single search
const updateLibraryAndLabelFilters = (libraryId: string | null, labelId: string | null) => {
  // Update library filter
  libraryValues.value = libraryId;
  if (libraryId && !activeFilters.value.includes('library')) {
    activeFilters.value.push('library');
  } else if (!libraryId) {
    activeFilters.value = activeFilters.value.filter(f => f !== 'library');
  }
  
  // Update label filter
  labelValues.value = labelId;
  if (labelId && !activeFilters.value.includes('label')) {
    activeFilters.value.push('label');
  } else if (!labelId) {
    activeFilters.value = activeFilters.value.filter(f => f !== 'label');
  }
  
  // Only call performSearch once
  performSearch();
};

const clearLibraryAndLabelFilters = () => {
  libraryValues.value = null;
  labelValues.value = null;
  activeFilters.value = activeFilters.value.filter(f => f !== 'library' && f !== 'label');
  performSearch();
};

// Expose methods for parent components
defineExpose({
  performSearch,
  loadParamsFromUrl,
  clearAllFilters,
  updateLibraryFilter,
  updateLabelFilter,
  updateLibraryAndLabelFilters,
  clearLibraryAndLabelFilters,
  getSearchInputValue: () => searchInputValue.value,
  getStatusValues: () => statusValues.value,
  getAccessValues: () => accessValues.value,
  getLibraryValues: () => libraryValues.value ? [libraryValues.value] : [],
  getLabelValues: () => labelValues.value ? [labelValues.value] : [],
  getActiveFilters: () => activeFilters.value
});

const searchPlaceholder = computed(() =>
  store.selectedContentType === 'templates'
    ? t('knowledge.search_templates')
    : t('knowledge.search_articles')
);
</script>

<template>
  <div class="search-row">
    <div class="search-filters">
      <BravoIconField>
        <BravoInputIcon class="pi pi-search" />
        <BravoInputText 
          v-model="searchInputValue" 
          @input="handleSearchInput" 
          @keydown="handleSearchKeydown" 
          :placeholder="searchPlaceholder" 
          :disabled="isLoading"
        />
      </BravoIconField>
      
      <!-- Add Filter dropdown -->
      <BravoButton 
        v-if="activeFilters.length < 7"
        :label="t('knowledge.add_filter')" 
        icon="pi pi-filter"
        severity="secondary"
        @click="showFilterMenu" 
        class="filter-button"
        :disabled="isLoading"
      />

      <!-- Actions Button -->
      <BravoButton 
        v-if="props.selectedArticlesCount && props.selectedArticlesCount > 0"
        :label="t('knowledge.actions')" 
        icon="pi pi-check-square"
        severity="secondary"
        @click="showActionsMenu" 
        class="actions-button"
        :disabled="isLoading"
      />
      
      <!-- Filter Menu -->
      <BravoMenu ref="filterMenu" :model="filterTypes.filter(f => !activeFilters.includes(f.value)).map(f => ({
        label: f.label,
        icon: f.icon,
        command: () => addFilter(f.value)
      }))" :popup="true" />

      <!-- Actions Menu -->
      <BravoMenu v-if="actionsMenuItems.length" ref="actionsMenu" :model="actionsMenuItems" :popup="true" />
      
      <!-- Active Filters -->
      <div v-if="activeFilters.length > 0" class="active-filters-row">
        <!-- Status Filter -->
        <div v-if="activeFilters.includes('status')" class="active-filter">
          <FilterDropdown
            label="Status"
            :options="statusOptions"
            v-model="statusValues"
            @remove="removeFilter('status')"
            @change="performSearch"
            :disabled="isLoading"
          />
        </div>
        
        <!-- Access Filter -->
        <div v-if="activeFilters.includes('access')" class="active-filter">
          <FilterDropdown
            label="Access"
            :options="accessOptions"
            v-model="accessValues"
            @remove="removeFilter('access')"
            @change="performSearch"
            :disabled="isLoading"
          />
        </div>
        
        <!-- Library Filter -->
        <div v-if="activeFilters.includes('library')" class="active-filter">
          <BravoSelect
            id="library-filter"
            v-model="libraryValues"
            :options="libraryOptions"
            placeholder="Select Library"
            showClear
            :disabled="isLoading"
            @update:modelValue="performSearch"
            dataTestId="library-filter"
            class="w-48"
          />
        </div>
        
        <!-- Label Filter -->
        <div v-if="activeFilters.includes('label')" class="active-filter">
          <BravoSelect
            id="label-filter"
            v-model="labelValues"
            :options="labelOptions"
            placeholder="Select Label"
            showClear
            :disabled="isLoading"
            @update:modelValue="performSearch"
            dataTestId="label-filter"
            class="w-48"
          />
        </div>
        
        <!-- Products Filter -->
        <div v-if="activeFilters.includes('products')" class="active-filter">
          <FilterDropdown
            label="Products"
            :options="productOptions"
            v-model="productValues"
            @remove="removeFilter('products')"
            @change="performSearch"
            :disabled="isLoading"
          />
        </div>
        
        <!-- Tags Filter -->
        <div v-if="activeFilters.includes('tags')" class="active-filter">
          <FilterDropdown
            label="Tags"
            :options="tagOptions"
            v-model="tagValues"
            @remove="removeFilter('tags')"
            @change="performSearch"
            :disabled="isLoading"
          />
        </div>
        
        <!-- Has Draft Filter -->
        <div v-if="activeFilters.includes('hasDraft')" class="active-filter">
          <BravoSelect
            id="has-draft-filter"
            v-model="hasDraftValue"
            :options="hasDraftOptions"
            placeholder="Has Draft?"
            showClear
            :disabled="isLoading"
            @update:modelValue="performSearch"
            dataTestId="has-draft-filter"
            class="w-48"
          />
        </div>
        

        
        <!-- Clear all filters button -->
        <BravoButton 
          v-if="activeFilters.length > 0"
          icon="pi pi-filter-slash"
          text
          size="small"
          @click="clearAllFilters"
          class="clear-filters-btn"
          label="Clear All"
          :disabled="isLoading"
        />
      </div>
    </div>
    <!-- Add UpdateLabelsDialog -->
    <UpdateLabelsDialog
      v-model:visible="showLabelsDialog"
      :selectedArticles="props.selectedArticles"
      @save="handleLabelUpdate"
    />

    <!-- Add UpdateAccessControlsDialog -->
    <UpdateAccessControlsDialog
      v-model:visible="showAccessControlsDialog"
      :selectedArticles="props.selectedArticles"
      @save="handleAccessControlsUpdate"
    />

    <!-- Add UpdateProductsDialog -->
    <UpdateProductsDialog
      v-model:visible="showProductsDialog"
      :selectedArticles="props.selectedArticles"
      @save="handleProductsUpdate"
    />

    <!-- Add UpdateTagsDialog -->
    <UpdateArticlesTagDialog
      v-model:visible="showTagsDialog"
      :selectedArticles="props.selectedArticles"
      @save="handleTagsUpdate"
    />

    <!-- Add ShareArticlesDialog -->
    <ShareArticlesDialog
      v-model:visible="showShareDialog"
      :selectedArticlesCount="props.selectedArticlesCount"
      :selectedArticles="props.selectedArticles"
      @share="handleShareArticles"
    />

    <!-- Add UnshareArticlesDialog -->
    <UnshareArticlesDialog
      v-model:visible="showUnshareDialog"
      :selectedArticlesCount="props.selectedArticlesCount"
      :selectedArticles="props.selectedArticles"
      @unshare="handleUnshareArticles"
    />

    <!-- Add PublishArticlesDialog -->
    <PublishArticlesDialog
      v-model:visible="showPublishDialog"
      :selectedArticlesCount="props.selectedArticlesCount"
      :selectedArticles="props.selectedArticles"
      @publish="handlePublishArticles"
    />

    <!-- Add UnpublishArticlesDialog -->
    <UnpublishArticlesDialog
      v-model:visible="showUnpublishDialog"
      :selectedArticlesCount="props.selectedArticlesCount"
      :selectedArticles="props.selectedArticles"
      @unpublish="handleUnpublishArticles"
    />

    <!-- Add ArchiveArticlesDialog -->
    <ArchiveArticlesDialog
      v-model:visible="showArchiveDialog"
      :selectedArticlesCount="props.selectedArticlesCount"
      :selectedArticles="props.selectedArticles"
      @archive="handleArchiveArticles"
    />
  </div>
</template>

<style scoped>
.search-row {
  margin-bottom: 1.5rem;
}

.search-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  width: 100%;
  align-items: center;
}

.active-filters-row {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
}

.active-filter {
  display: flex;
  align-items: center;
}

.active-filter .w-48 {
  min-width: 12rem;
}

.clear-filters-btn {
  margin-left: auto;
}

/* Make the filter button indicate state */
.filter-button.p-button {
  position: relative;
}

.filter-button.p-button:has(.p-badge) {
  background-color: var(--primary-color);
  color: var(--primary-color-text);
}
.actions-button.p-button {
  position: relative;
  margin-left: auto;
}

.actions-button.p-button:has(.p-badge) {
  background-color: var(--primary-color);
  color: var(--primary-color-text);
}

.date-filter-wrapper {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.remove-filter-btn {
  color: var(--text-color-secondary);
}

.remove-filter-btn:hover {
  color: var(--primary-color);
}
</style> 