<script setup lang="ts">
import { ref, watch, onBeforeUnmount, onMounted, nextTick, useSlots, computed } from 'vue';
import Button from 'primevue/button';
import ProgressSpinner from 'primevue/progressspinner';
import Dropdown from 'primevue/dropdown';
import Checkbox from 'primevue/checkbox';
import MultiSelect from 'primevue/multiselect';
import BravoTag from '@services/ui-component-library/components/BravoTag.vue';
import BravoLabel from '@services/ui-component-library/components/BravoLabel.vue';
import BravoInputText from '@services/ui-component-library/components/BravoInputText.vue';
import BravoInputNumber from '@services/ui-component-library/components/BravoInputNumber.vue';
import BravoTextarea from '@services/ui-component-library/components/BravoTextarea.vue';
import BravoSelectField from '@services/ui-component-library/components/BravoSelectField.vue';
import BravoMultiSelect from '@services/ui-component-library/components/BravoMultiSelect.vue';
import BravoCheckbox from '@services/ui-component-library/components/BravoCheckbox.vue';
import BravoDatePicker from '@services/ui-component-library/components/BravoDatePicker.vue';
import BravoInputGroup from '@services/ui-component-library/components/BravoInputGroup.vue';
import BravoInputMask from '@services/ui-component-library/components/BravoInputMask.vue';
import InputGroupAddon from 'primevue/inputgroupaddon';
import Editor from 'primevue/editor';


// Define the input types we support
type InputType = 'text' | 'textarea' | 'textarea_html' | 'dropdown' | 'multiselect' | 'date' | 'datetime' | 'currency' | 'checkbox' | 'email' | 'phone' | 'url';
type DisplayType = 'text' | 'chips' | 'tag';

const props = defineProps<{
  label: string;
  fieldName: string;
  value: any;
  displayValue: string | string[];
  inputType: InputType;
  displayType: DisplayType; 
  options?: any[];
  optionLabel?: string;
  optionValue?: string;
  isLoading?: boolean;
  isHorizontal?: boolean;
  iconClass?: string;
  isSaving?: boolean;
  noValueText?: string;
  dataTestId?: string;
  filterPlaceholder?: string;
  showFilter?: boolean;
  enforceSubmitButton?: boolean;
  isEditing?: boolean;
  libraryId?: string;
  showClear?: boolean;
  dateFormat?: 'iso' | 'simple' | 'raw';
}>();

const emit = defineEmits<{
  (e: 'update', fieldName: string, value: any): void;
  (e: 'save', fieldName: string, value: any): void;
  (e: 'submit-start'): void;
  (e: 'cancel'): void;
  (e: 'filter', event: any): void;
}>();

const slots = useSlots();

const isEditingInternal = ref(false);
const editedValue = ref<any>(null);
const emailError = ref('');
const urlError = ref('');
const multiSelectRef = ref<any>(null);
const dropdownRef = ref<any>(null);
const textInputRef = ref<any>(null);
const textareaRef = ref<any>(null);
const datePickerRef = ref<any>(null);
const currencyInputRef = ref<any>(null);
const checkboxRef = ref<any>(null);
const emailInputRef = ref<any>(null);
const phoneInputRef = ref<any>(null);
const urlInputRef = ref<any>(null);
const htmlEditorRef = ref<any>(null);
const inputWidth = ref('240px'); // Default fallback width
const fieldValueRef = ref<HTMLElement | null>(null);

// Add computed property for formatted display value
const formattedDisplayValue = computed(() => {
  // If we have a custom displayValue prop, check if it needs special formatting
  if (props.displayValue !== undefined && props.displayValue !== null && props.displayValue !== '') {
    // For date fields
    if (props.inputType === 'date') {
      // If the displayValue is already formatted (not a date string), return it as-is
      if (typeof props.displayValue === 'string' && props.displayValue === '—') {
        return props.displayValue;
      }
      
      // Try to parse and format the date
      try {
        const dateValue = props.value; // Use the raw value for date parsing
        if (dateValue) {
          // Check for invalid/placeholder date values
          if (dateValue === '0000-00-00' || dateValue === '0000-00-00 00:00:00') {
            return '—';
          }
          
          const date = new Date(dateValue);
          if (!isNaN(date.getTime())) {
            // For date-only fields, we want to avoid timezone issues
            // Parse the date as if it's in local timezone
            if (typeof dateValue === 'string' && dateValue.includes('-')) {
              // If it's a date string like "2024-01-15", parse it as local date
              const [year, month, day] = dateValue.split('T')[0].split('-').map(Number);
              const localDate = new Date(year, month - 1, day); // month is 0-indexed
              return localDate.toLocaleDateString();
            } else {
              // Fallback to regular parsing
              return date.toLocaleDateString();
            }
          } else {
            return '—'; // Return empty state for invalid dates
          }
        }
      } catch (e) {
        console.warn('Failed to format date:', e);
      }
      return '—';
    }
    
    // For datetime fields
    if (props.inputType === 'datetime') {
      // If the displayValue is already formatted (not a date string), return it as-is
      if (typeof props.displayValue === 'string' && props.displayValue === '—') {
        return props.displayValue;
      }
      
      // Try to parse and format the datetime
      try {
        const dateValue = props.value; // Use the raw value for date parsing
        if (dateValue) {
          // Check for invalid/placeholder date values
          if (dateValue === '0000-00-00' || dateValue === '0000-00-00 00:00:00') {
            return '—';
          }
          
          // Handle timezone-adjusted datetime values
          const date = new Date(dateValue);
          if (!isNaN(date.getTime())) {
            // If the value was saved with timezone adjustment, we need to display it as-is
            // without further timezone conversion
            return date.toLocaleString(undefined, {
              year: 'numeric',
              month: 'numeric',
              day: 'numeric',
              hour: 'numeric',
              minute: '2-digit',
              hour12: true
            });
          } else {
            return '—'; // Return empty state for invalid dates
          }
        }
      } catch (e) {
        console.warn('Failed to format datetime:', e);
      }
      return '—';
    }
    
    // For currency fields
    if (props.inputType === 'currency') {
      const value = props.value;
      if (typeof value === 'number') {
        // Support field-specific decimal places if available, otherwise default to 2
        const decimalPlaces = (props as any).decimalPlaces || 2;
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
          minimumFractionDigits: decimalPlaces,
          maximumFractionDigits: decimalPlaces
        }).format(value);
      }
      // If value is already formatted or is a string, return displayValue as-is
      return props.displayValue;
    }
  }
  
  // For all other cases, return the original displayValue
  return props.displayValue;
});

// Computed property for checkbox value to force reactivity
const checkboxValue = computed({
  get: () => {
    if (props.inputType === 'checkbox') {
      // When editing, use editedValue; when not editing, use props.value
      return isEditingInternal.value ? Boolean(editedValue.value) : Boolean(props.value);
    }
    return editedValue.value;
  },
  set: (newValue) => {
    if (props.inputType === 'checkbox') {
      editedValue.value = newValue;
    } else {
      editedValue.value = newValue;
    }
  }
});

// Check if this is a textarea_html field
const isTextareaHtml = computed(() => {
  return props.inputType === 'textarea_html';
});

// Email validation function
const validateEmail = (email: string): boolean => {
  if (!email) {
    emailError.value = 'Please enter an email address';
    return false;
  }

  // Basic email format validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    emailError.value = 'Please enter a valid email address';
    return false;
  }

  emailError.value = '';
  return true;
};

// Clear email error when value changes
const clearEmailError = () => {
  emailError.value = '';
};

// URL validation function
const validateUrl = (url: string): boolean => {
  if (!url) {
    urlError.value = 'Please enter a URL';
    return false;
  }

  // Basic URL format validation - allow URLs with or without protocol
  try {
    // If URL doesn't start with protocol, add https://
    const urlToTest = url.startsWith('http://') || url.startsWith('https://') ? url : `https://${url}`;
    new URL(urlToTest);
    urlError.value = '';
    return true;
  } catch {
    urlError.value = 'Please enter a valid URL';
    return false;
  }
};

// Clear URL error when value changes
const clearUrlError = () => {
  urlError.value = '';
};

// Helper function to ensure URL has protocol for display
const formatUrlForDisplay = (url: string): string => {
  if (!url) return '';
  return url.startsWith('http://') || url.startsWith('https://') ? url : `https://${url}`;
};

// Helper function to validate and convert date values
const getValidDateOrNull = (value: any): Date | null => {
  if (!value || value === '' || value === '0000-00-00' || value === '0000-00-00 00:00:00') {
    return null;
  }
  
  try {
    const date = new Date(value);
    return !isNaN(date.getTime()) ? date : null;
  } catch {
    return null;
  }
};

// Initialize the edited value when the props value changes
watch(() => props.value, (newValue) => {
  // Always update editedValue when props.value changes, but convert to proper types
  if (props.inputType === 'checkbox') {
    editedValue.value = Boolean(newValue);
  } else if (props.inputType === 'date' || props.inputType === 'datetime') {
    // Ensure date/datetime values are proper Date objects or null for invalid values
    editedValue.value = getValidDateOrNull(newValue);
  } else {
    // Make a deep copy of array values to avoid reference issues
    editedValue.value = Array.isArray(newValue) ? [...newValue] : newValue;
  }
}, { immediate: true });

// Watch editedValue changes to emit updates
watch(() => editedValue.value, (newValue) => {
  if (props.inputType === 'checkbox') {
    // Emit update when checkbox value changes during editing
    if (isEditingInternal.value) {
      emit('update', props.fieldName, newValue);
    }
  }
});

// Start editing the field
const startEditing = () => {
  // Only allow editing if isEditing prop is true (not undefined or false)
  if (props.isEditing !== true) return;
  

  
  // Make a fresh copy of the value to edit, ensuring proper type for checkboxes and dates
  if (props.inputType === 'checkbox') {
    editedValue.value = Boolean(props.value);
  } else if (props.inputType === 'date' || props.inputType === 'datetime') {
    // Ensure date/datetime values are proper Date objects or null for invalid values
    editedValue.value = getValidDateOrNull(props.value);
  } else {
    editedValue.value = Array.isArray(props.value) ? [...props.value] : props.value;
  }
  isEditingInternal.value = true;
  emit('update', props.fieldName, editedValue.value);
  
  // For dropdown inputs, automatically click to open after a short delay
  if (props.inputType === 'dropdown') {
    setTimeout(() => {
      if (dropdownRef.value && dropdownRef.value.$el) {
        // Try to find the clickable element within the dropdown component
        const clickableElement = dropdownRef.value.$el.querySelector('.p-dropdown, .p-select') || dropdownRef.value.$el;
        if (clickableElement) {
          clickableElement.click();
        }
      }
    }, 100);
  }
  
  // For multiselect inputs, automatically click to open after a short delay
  if (props.inputType === 'multiselect') {
    setTimeout(() => {
      if (multiSelectRef.value && multiSelectRef.value.$el) {
        // Try to find the clickable element within the multiselect component
        const clickableElement = multiSelectRef.value.$el.querySelector('.p-multiselect') || multiSelectRef.value.$el;
        if (clickableElement) {
          clickableElement.click();
        }
      }
    }, 100);
  }
  

  
  // For text inputs, focus after a short delay
  if (props.inputType === 'text') {
    setTimeout(() => {
      if (textInputRef.value && textInputRef.value.$el) {
        // Try to find the input element within the text component
        const inputElement = textInputRef.value.$el.querySelector('input') || textInputRef.value.$el;
        if (inputElement && inputElement.focus) {
          inputElement.focus();
        }
      }
    }, 100);
  }
  
  // For textarea inputs, focus after a short delay
  if (props.inputType === 'textarea') {
    setTimeout(() => {
      if (textareaRef.value) {
        // Try different ways to find and focus the textarea element
        let textareaElement = null;
        
        if (textareaRef.value.$el) {
          textareaElement = textareaRef.value.$el.querySelector('textarea') || 
                           textareaRef.value.$el.querySelector('.p-inputtextarea') ||
                           textareaRef.value.$el;
        } else if (textareaRef.value.focus) {
          // Direct focus if the ref has a focus method
          textareaRef.value.focus();
          return;
        }
        
        if (textareaElement && textareaElement.focus) {
          textareaElement.focus();
        }
      }
    }, 100);
  }
  
  // For date inputs, focus after a short delay
  if (props.inputType === 'date' || props.inputType === 'datetime') {
    setTimeout(() => {
      if (datePickerRef.value && datePickerRef.value.$el) {
        // Try to find the input element within the date picker component
        const inputElement = datePickerRef.value.$el.querySelector('input') || datePickerRef.value.$el;
        if (inputElement && inputElement.focus) {
          inputElement.focus();
        }
      }
    }, 100);
  }
  
  // For currency inputs, focus after a short delay
  if (props.inputType === 'currency') {
    setTimeout(() => {
      if (currencyInputRef.value && currencyInputRef.value.$el) {
        // Try to find the input element within the currency component
        const inputElement = currencyInputRef.value.$el.querySelector('input') || currencyInputRef.value.$el;
        if (inputElement && inputElement.focus) {
          inputElement.focus();
        }
      }
    }, 100);
  }
  
  // For checkbox inputs, focus after a short delay
  if (props.inputType === 'checkbox') {
    setTimeout(() => {
      if (checkboxRef.value && checkboxRef.value.$el) {
        // Try to find the input element within the checkbox component
        const inputElement = checkboxRef.value.$el.querySelector('input') || checkboxRef.value.$el;
        if (inputElement && inputElement.focus) {
          inputElement.focus();
        }
      }
    }, 100);
  }
  
  // For email inputs, focus after a short delay
  if (props.inputType === 'email') {
    setTimeout(() => {
      if (emailInputRef.value && emailInputRef.value.$el) {
        // Try to find the input element within the email input group
        const inputElement = emailInputRef.value.$el.querySelector('input') || emailInputRef.value.$el;
        if (inputElement && inputElement.focus) {
          inputElement.focus();
        }
      }
    }, 100);
  }
  
  // For phone inputs, focus after a short delay
  if (props.inputType === 'phone') {
    setTimeout(() => {
      if (phoneInputRef.value && phoneInputRef.value.$el) {
        // Try to find the input element within the phone input mask
        const inputElement = phoneInputRef.value.$el.querySelector('input') || phoneInputRef.value.$el;
        if (inputElement && inputElement.focus) {
          inputElement.focus();
        }
      }
    }, 100);
  }
  
  // For URL inputs, focus after a short delay
  if (props.inputType === 'url') {
    setTimeout(() => {
      if (urlInputRef.value && urlInputRef.value.$el) {
        // Try to find the input element within the URL input
        const inputElement = urlInputRef.value.$el.querySelector('input') || urlInputRef.value.$el;
        if (inputElement && inputElement.focus) {
          inputElement.focus();
        }
      }
    }, 100);
  }
  
  // For HTML editor inputs, focus after a short delay
  if (isTextareaHtml.value) {
    setTimeout(() => {
      if (htmlEditorRef.value && htmlEditorRef.value.$el) {
        // Try to find the Quill editor element within the HTML editor
        const editorElement = htmlEditorRef.value.$el.querySelector('.ql-editor') || htmlEditorRef.value.$el;
        if (editorElement && editorElement.focus) {
          editorElement.focus();
        }
      }
    }, 100);
  }
};

// Save the edited value
const saveEdit = () => {
  // Validate email fields before saving
  if (props.inputType === 'email') {
    if (!validateEmail(editedValue.value || '')) {
      return; // Don't save if validation fails
    }
  }
  
  // Validate URL fields before saving
  if (props.inputType === 'url') {
    if (!validateUrl(editedValue.value || '')) {
      return; // Don't save if validation fails
    }
  }
  
  // Ensure we pass the correct data type for arrays and handle datetime timezone issues
  let valueToSave = editedValue.value;
  
  // Handle date/datetime formatting based on the dateFormat prop
  const dateFormat = props.dateFormat || 'iso'; // Default to 'iso' for backward compatibility
  
  // Special handling for date fields
  if (props.inputType === 'date' && valueToSave instanceof Date) {
    if (dateFormat === 'raw') {
      // Keep the Date object as-is - let the API handle it
      // This is useful for APIs that can handle Date objects directly
    } else if (dateFormat === 'simple') {
      // For date-only fields, save as YYYY-MM-DD format to avoid timezone issues
      const year = valueToSave.getFullYear();
      const month = String(valueToSave.getMonth() + 1).padStart(2, '0');
      const day = String(valueToSave.getDate()).padStart(2, '0');
      
      // Save as simple date string without time component
      valueToSave = `${year}-${month}-${day}`;
    } else {
      // Default 'iso' format - preserve local date without timezone conversion
      const year = valueToSave.getFullYear();
      const month = String(valueToSave.getMonth() + 1).padStart(2, '0');
      const day = String(valueToSave.getDate()).padStart(2, '0');
      
      // Save as simple date string without time component (same as simple for dates)
      valueToSave = `${year}-${month}-${day}`;
    }
  }
  
  // Special handling for datetime fields
  if (props.inputType === 'datetime' && valueToSave instanceof Date) {
    if (dateFormat === 'raw') {
      // Keep the Date object as-is - let the API handle it
      // This is useful for APIs that can handle Date objects directly
    } else if (dateFormat === 'simple') {
      // Simple format - use the date's natural ISO string without timezone manipulation
      valueToSave = valueToSave.toISOString();
    } else {
      // Default 'iso' format - create a timezone-safe ISO string (existing logic)
      // Create a new date string that represents the local time as if it were UTC
      // This prevents timezone conversion issues for the cases API
      const year = valueToSave.getFullYear();
      const month = String(valueToSave.getMonth() + 1).padStart(2, '0');
      const day = String(valueToSave.getDate()).padStart(2, '0');
      const hours = String(valueToSave.getHours()).padStart(2, '0');
      const minutes = String(valueToSave.getMinutes()).padStart(2, '0');
      const seconds = String(valueToSave.getSeconds()).padStart(2, '0');
      
      // Format as ISO string but treat the local time as UTC
      valueToSave = `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.000Z`;
    }
  }
  
  // Emit that submission is starting
  emit('submit-start');
  
  // Emit the save event with the value
  emit('save', props.fieldName, valueToSave);
  
  // NOTE: We don't exit edit mode here anymore
  // isEditing.value = false;
  // This will be handled by the parent component when it calls our "handleSaveComplete" method
};

// Handle dropdown change - automatically save when user selects an option
const handleDropdownChange = () => {
  // Add a small delay to ensure the value is updated
  setTimeout(() => {
    saveEdit();
  }, 50);
};

// Handle date change - automatically save when user selects a date
const handleDateChange = () => {
  // Add a small delay to ensure the value is updated
  setTimeout(() => {
    saveEdit();
  }, 50);
};

// Track recent multiselect changes to prevent immediate auto-save
const recentMultiselectChange = ref(false);

// Track if the last interaction was a keyboard event to prevent auto-save on tab navigation
const lastInteractionWasKeyboard = ref(false);

// Track mouse selection state to prevent auto-save during text selection
const isMouseSelecting = ref(false);
const mouseDownInsideField = ref(false);

// Handle multiselect change - do NOT auto-save to allow multiple selections
const handleMultiselectChange = () => {
  // Just update the value, don't auto-save
  // Users can select multiple values before manually saving
  
  // Set flag to prevent immediate auto-save
  recentMultiselectChange.value = true;
  
  // Clear the flag after a delay to allow normal click-outside behavior later
  setTimeout(() => {
    recentMultiselectChange.value = false;
  }, 200);
};

// Handle checkbox change - not needed since v-model handles the updates
const handleCheckboxChange = () => {
  // This function is kept for compatibility but not used
  // v-model automatically handles checkbox state updates
};

// New method to handle save completion
const handleSaveComplete = (success: boolean = true) => {
  if (success) {
    isEditingInternal.value = false;
  }
  // If not successful, stay in edit mode
};

// Cancel editing
const cancelEdit = () => {
  // Reset the edited value to the original value, ensuring proper type for checkboxes
  if (props.inputType === 'checkbox') {
    editedValue.value = Boolean(props.value);
  } else {
    editedValue.value = Array.isArray(props.value) ? [...props.value] : props.value;
  }
  isEditingInternal.value = false;
  emit('cancel');
};

// Helper function to check if an element is part of a multiselect dropdown
const isInsideMultiSelect = (element: HTMLElement | null): boolean => {
  if (!element) return false;
  
  // First check if element is inside a multiselect panel (most reliable)
  if (element.closest('.p-multiselect-panel')) {
    return true;
  }
  
  // Check for specific MultiSelect class names
  const isMultiSelectSpecific = element.classList.contains('p-multiselect-item') || 
                                element.classList.contains('p-multiselect-option') ||
                                element.classList.contains('p-multiselect-header') ||
                                element.classList.contains('p-multiselect-filter-container') ||
                                element.classList.contains('p-multiselect-filter') ||
                                element.classList.contains('p-multiselect-panel') ||
                                element.classList.contains('p-multiselect-items') ||
                                element.classList.contains('p-multiselect-item-group') ||
                                element.classList.contains('p-multiselect-close') ||
                                element.classList.contains('p-multiselect-trigger') ||
                                element.classList.contains('p-multiselect-footer');
  
  // Also check for checkboxes, but only if they're inside a multiselect panel
  const isCheckboxInMultiSelect = element.classList.contains('p-checkbox') && 
                                  element.closest('.p-multiselect-panel');
  
  // Check for input elements inside multiselect (like filter inputs and custom footer inputs)
  const isInputInMultiSelect = element.tagName === 'INPUT' && 
                               (element.closest('.p-multiselect-panel') || 
                                element.classList.contains('new-term-input'));
  
  // Check for any span or div that might be inside a multiselect option or custom footer
  const isInsideMultiSelectOption = element.closest('.p-multiselect-item') || 
                                   element.closest('.p-multiselect-option') ||
                                   element.closest('.p-multiselect-filter-container') ||
                                   element.closest('.p-multiselect-footer') ||
                                   element.closest('.add-term-section') ||
                                   element.closest('.add-term-input-container') ||
                                   (element.tagName === 'SPAN' && element.closest('.p-multiselect-panel'));
  
  if (isMultiSelectSpecific || isCheckboxInMultiSelect || isInputInMultiSelect || isInsideMultiSelectOption) {
    return true;
  }
  
  // Check parent elements more thoroughly
  let currentElement = element.parentElement;
  while (currentElement) {
    if (currentElement.classList.contains('p-multiselect-panel') ||
        currentElement.classList.contains('p-multiselect-item') ||
        currentElement.classList.contains('p-multiselect-option') ||
        currentElement.closest('.p-multiselect-panel')) {
      return true;
    }
    currentElement = currentElement.parentElement;
  }
  
  return false;
};

// Handle blur events for auto-save
const handleBlur = () => {
  if (isEditingInternal.value && !props.enforceSubmitButton) {
    // Auto-save on blur for text-based field types (exclude multiselect due to complex dropdown behavior)
    const shouldAutoSave = props.inputType === 'text' || 
                         props.inputType === 'textarea' ||
                         props.inputType === 'textarea_html' ||
                         props.inputType === 'currency' ||
                         props.inputType === 'email' ||
                         props.inputType === 'phone' ||
                         props.inputType === 'url';
    
    if (shouldAutoSave) {
      // Only auto-save if the blur was caused by a mouse click, not keyboard navigation
      if (!lastInteractionWasKeyboard.value) {
        // Add a small delay to allow for potential focus changes within the same field
        setTimeout(() => {
          if (isEditingInternal.value) {
            saveEdit();
          }
        }, 100);
      }
    }
  }
};

// Handle mouse down to track selection state
const handleMouseDown = (event: MouseEvent) => {
  if (isEditingInternal.value) {
    const target = event.target as HTMLElement;
    const editField = document.querySelector(`.${props.fieldName}-edit-field`);
    
    if (editField && editField.contains(target)) {
      mouseDownInsideField.value = true;
      isMouseSelecting.value = true;
    }
  }
};

// Handle mouse up to reset selection state
const handleMouseUp = (event: MouseEvent) => {
  if (isEditingInternal.value) {
    // Reset selection tracking after a short delay to allow click events to process
    setTimeout(() => {
      isMouseSelecting.value = false;
      mouseDownInsideField.value = false;
    }, 10);
  }
};

// Handle click outside - simplified for cases not covered by blur
const handleClickOutside = (event: MouseEvent) => {
  if (isEditingInternal.value) {
    const target = event.target as HTMLElement;
    const editField = document.querySelector(`.${props.fieldName}-edit-field`);
    
    // Don't auto-save if we're in the middle of a text selection operation
    if (isMouseSelecting.value) {
      return;
    }
    
    // If no target or no editField, something went wrong - but we should still try to save
    if (!target || !editField) {
      if (!props.enforceSubmitButton) {
        if (props.inputType === 'multiselect') {
          setTimeout(() => {
            if (isEditingInternal.value) {
              saveEdit();
            }
          }, 300);
        } else {
          saveEdit();
        }
      }
      return;
    }
    
    // Check for various dropdown panel classes (PrimeVue and UI library)
    const dropdownPanels = document.querySelectorAll('.p-dropdown-panel, .p-multiselect-panel, .p-overlay-panel, [data-pc-section="panel"]');
    const isInsideDropdownPanel = Array.from(dropdownPanels).some(panel => panel.contains(target));
    
    // Check if clicked element is inside the multiselect items
    const isMultiSelectItem = isInsideMultiSelect(target);
    

    
    // Check if target is inside any overlay or portal element
    const isInsideOverlay = target.closest && target.closest('.p-component-overlay, .p-overlaypanel, [data-pc-name="overlaypanel"], [data-pc-name="dropdown"], [data-pc-name="multiselect"]');
    
    // Don't close if we're clicking inside dropdowns, overlays, or our edit field
    const isClickingInside = editField.contains(target) || 
                            isInsideDropdownPanel ||
                            isMultiSelectItem ||
                            isInsideOverlay;
    
    if (!isClickingInside) {
      // Auto-save for dropdown and multiselect (with delay for multiselect)
      const shouldAutoSave = props.inputType === 'dropdown' || props.inputType === 'multiselect';
      
      if (shouldAutoSave && !props.enforceSubmitButton) {
        if (props.inputType === 'multiselect') {
          // For multiselect, add a longer delay to allow for multiple selections
          // But don't save if there was a recent multiselect change
          if (!recentMultiselectChange.value) {
            setTimeout(() => {
              if (isEditingInternal.value && !recentMultiselectChange.value) {
                saveEdit();
              }
            }, 300); // Longer delay for multiselect
          }
        } else {
          saveEdit(); // Immediate save for dropdown
        }
      } else if (!props.enforceSubmitButton) {
        // For other types, auto-save if not enforcing submit button
        saveEdit();
      } else {
        // For enforceSubmitButton mode, just exit edit mode without reverting changes
        isEditingInternal.value = false;
      }
    }
  }
};

// Handle keyboard events
const handleKeyDown = (event: KeyboardEvent) => {
  if (isEditingInternal.value && event.key === 'Escape') {
    // Escape key should always cancel and revert changes
    cancelEdit();
  } else if (isEditingInternal.value && event.key === 'Enter' && (props.inputType === 'text' || props.inputType === 'currency' || props.inputType === 'email' || props.inputType === 'phone' || props.inputType === 'url')) {
    // Enter key should save for text, currency, email, phone, and URL inputs only (not textarea)
    event.preventDefault();
    saveEdit();
  }
};

// Track interaction type for blur auto-save logic
const handleGlobalKeyDown = (event: KeyboardEvent) => {
  // Mark that the last interaction was keyboard (especially for Tab key)
  if (event.key === 'Tab' || event.key === 'Enter' || event.key === 'Escape' || event.key === 'ArrowUp' || event.key === 'ArrowDown') {
    lastInteractionWasKeyboard.value = true;
    // Reset after a short delay to allow mouse interactions to be detected
    setTimeout(() => {
      lastInteractionWasKeyboard.value = false;
    }, 200);
  }
};

const handleGlobalMouseDown = () => {
  // Mark that the last interaction was mouse
  lastInteractionWasKeyboard.value = false;
};

// Add click and keyboard event listeners when editing starts
watch(isEditingInternal, (newValue) => {
  if (newValue) {
    // Use a slight delay to add the click listener to avoid immediate triggering
    setTimeout(() => {
      document.addEventListener('click', handleClickOutside);
      document.addEventListener('keydown', handleKeyDown);
      document.addEventListener('keydown', handleGlobalKeyDown);
      document.addEventListener('mousedown', handleGlobalMouseDown);
      document.addEventListener('mousedown', handleMouseDown);
      document.addEventListener('mouseup', handleMouseUp);
    }, 100);
  } else {
    document.removeEventListener('click', handleClickOutside);
    document.removeEventListener('keydown', handleKeyDown);
    document.removeEventListener('keydown', handleGlobalKeyDown);
    document.removeEventListener('mousedown', handleGlobalMouseDown);
    document.removeEventListener('mousedown', handleMouseDown);
    document.removeEventListener('mouseup', handleMouseUp);
    
    // Reset selection state when exiting edit mode
    isMouseSelecting.value = false;
    mouseDownInsideField.value = false;
  }
});

// Clean up event listeners when component is unmounted
onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside);
  document.removeEventListener('keydown', handleKeyDown);
  document.removeEventListener('keydown', handleGlobalKeyDown);
  document.removeEventListener('mousedown', handleGlobalMouseDown);
  document.removeEventListener('mousedown', handleMouseDown);
  document.removeEventListener('mouseup', handleMouseUp);
});



const clearFilter = () => {
  if (multiSelectRef.value && typeof multiSelectRef.value.filterValue !== 'undefined') {
    multiSelectRef.value.filterValue = '';
  }
};

// Calculate input width based on field-value container width minus 60px
const calculateInputWidth = () => {
  if (fieldValueRef.value) {
    const fieldWidth = fieldValueRef.value.offsetWidth;
    
    // Detect scrollbar width by comparing clientWidth vs offsetWidth on the document
    const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;
    
    // Adjust field width if scrollbar is present
    const adjustedFieldWidth = scrollbarWidth > 0 ? fieldWidth - Math.min(scrollbarWidth, 20) : fieldWidth;
    
    let calculatedWidth;
    
    // For textarea, HTML editor, and URL fields, use a larger minimum and different calculation
    if (props.inputType === 'textarea' || isTextareaHtml.value || props.inputType === 'url') {
      // In horizontal mode, use smaller minimum to prevent pushing labels off-screen
      const minWidth = props.isHorizontal ? 180 : 300;
      calculatedWidth = Math.max(adjustedFieldWidth - 60, minWidth);
    } else {
      calculatedWidth = Math.max(adjustedFieldWidth - 60, 180); // Minimum 180px for other inputs
    }
    
    // In horizontal mode, ensure we leave space for buttons (all field types)
    if (props.isHorizontal) {
      // Reserve extra space for buttons in horizontal mode (44px includes 4px buffer for scrollbar)
      calculatedWidth = Math.min(calculatedWidth, adjustedFieldWidth - 44);
    }
    
    inputWidth.value = `${calculatedWidth}px`;
  }
};

// Set up ResizeObserver to watch for field width changes
let resizeObserver: ResizeObserver | null = null;

onMounted(async () => {
  await nextTick();
  calculateInputWidth();
  
  // Set up ResizeObserver to reactively update width
  if (fieldValueRef.value && window.ResizeObserver) {
    resizeObserver = new ResizeObserver(() => {
      calculateInputWidth();
    });
    resizeObserver.observe(fieldValueRef.value);
  }
});

onBeforeUnmount(() => {
  if (resizeObserver) {
    resizeObserver.disconnect();
  }
});

defineExpose({
  handleSaveComplete,
  clearFilter,
  startEditing
});
</script>

<template>
  <div 
    class="data-field" 
    :class="{ 'horizontal': isHorizontal }" 
    :data-testid="dataTestId || `article-${fieldName}-field`"
  >
    <BravoLabel :text="label" class="article-field-label" />
    <div 
      ref="fieldValueRef"
      :class="[
        'field-value', 
        'editable-field', 
        `${fieldName}-edit-field`,
        { 'field-section-disabled': isEditing !== true },
        { 'editing': isEditingInternal }
      ]" 
      @click="startEditing"
      :data-testid="`article-${fieldName}-value`"
    >
      <!-- Display mode -->
      <div 
        v-if="!isEditingInternal" 
        class="display-mode" 
        :class="[
          `${fieldName}-display-mode`,
          { 'field-disabled': isEditing !== true }
        ]" 
      >
        <i v-if="iconClass" :class="iconClass" />
        
        <!-- Custom display content via slot -->
        <div v-if="slots.display" class="custom-display-content">
          <slot name="display" :displayValue="formattedDisplayValue" :value="value" />
        </div>
        
        <!-- Checkbox display -->
        <Checkbox 
          v-else-if="inputType === 'checkbox' && displayType === 'text'"
          :model-value="Boolean(value)"
          :binary="true"
          :disabled="true"
          class="display-checkbox"
          :data-testid="`${dataTestId || fieldName}-display-checkbox`"
          @click.stop="startEditing"
        />
        
        <!-- URL display -->
        <div v-else-if="inputType === 'url' && displayType === 'text'" class="url-display-container">
          <a 
            v-if="formattedDisplayValue && formattedDisplayValue !== '—'"
            :href="formatUrlForDisplay(formattedDisplayValue.toString())"
            target="_blank"
            rel="noopener noreferrer"
            class="url-link"
            @click.stop
          >
            {{ formattedDisplayValue }}
          </a>
          <span 
            v-else
            class="clickable-value empty-value"
            @click.stop="startEditing"
          >
            {{ noValueText || 'No URL' }}
          </span>
          <!-- Edit button for URL fields when editable and not disabled -->
          <Button
            v-if="formattedDisplayValue && formattedDisplayValue !== '—' && isEditing === true"
            icon="pi pi-pencil"
            severity="secondary"
            text
            class="edit-url-button"
            @click.stop="startEditing"
            v-tooltip.top="{ value: 'Edit URL', showDelay: 300 }"
            :data-testid="`${dataTestId || fieldName}-edit-url-button`"
          />
        </div>
        
        <!-- HTML Text display -->
        <div 
          v-else-if="isTextareaHtml && displayType === 'text'"
          class="clickable-value html-display" 
          :class="{ 
            'empty-value': (typeof formattedDisplayValue === 'string' && formattedDisplayValue === '—') || formattedDisplayValue === (noValueText || 'No value')
          }"
          @click.stop="startEditing"
        >
          <div 
            v-if="typeof formattedDisplayValue === 'string' && formattedDisplayValue !== '—' && formattedDisplayValue !== (noValueText || 'No value')"
            v-html="formattedDisplayValue"
            class="html-content"
          ></div>
          <span v-else class="empty-text">
            {{ noValueText || 'No content' }}
          </span>
        </div>
        
        <!-- Text display -->
        <span 
          v-else-if="displayType === 'text'" 
          class="clickable-value" 
          :class="{ 
            'textarea-display': inputType === 'textarea',
            'empty-value': (typeof formattedDisplayValue === 'string' && formattedDisplayValue === '—') || formattedDisplayValue === (noValueText || 'No value')
          }"
          @click.stop="startEditing"
        >
          {{ typeof formattedDisplayValue === 'string' ? formattedDisplayValue : Array.isArray(formattedDisplayValue) ? formattedDisplayValue.join(', ') : (noValueText || 'No value') }}
        </span>
        
        <!-- Chips display -->
        <div v-else-if="displayType === 'chips'" class="chips-content" @click.stop="startEditing">
          <span 
            v-if="!Array.isArray(formattedDisplayValue) || formattedDisplayValue.length === 0"
            class="empty-value"
          >
            {{ noValueText || 'No items' }}
          </span>
          <div v-else class="chip-items">
            <BravoTag
              v-for="(item, index) in formattedDisplayValue"
              :key="index"
              :value="item"
              severity="info"
              class="chip-item"
              @click.stop="startEditing"
              :data-testid="`${fieldName}-chip-${index}`"
            />
          </div>
        </div>
        
        <!-- Tag display (single value with icon) -->
        <div v-else-if="displayType === 'tag'" class="tag-content" @click.stop="startEditing">
          <span>{{ formattedDisplayValue }}</span>
        </div>
      </div>
      
      <!-- Edit mode -->
      <div v-else class="edit-container-compact">
        <div class="input-wrapper" :style="{ width: inputWidth }">
          <!-- Text input -->
          <BravoInputText 
            v-if="inputType === 'text'"
            ref="textInputRef"
            v-model="editedValue"
            class="edit-input"
            @click.stop
            @blur="handleBlur"
            @keydown.enter.prevent="saveEdit"
            :data-testid="`${dataTestId || fieldName}-input`"
          />
          
          <!-- Currency input -->
          <BravoInputNumber 
            v-else-if="inputType === 'currency'"
            ref="currencyInputRef"
            v-model="editedValue"
            currency="USD"
            locale="en-US"
            mode="currency"
            class="edit-currency"
            @click.stop
            @blur="handleBlur"
            @keydown.enter.prevent="saveEdit"
            :data-testid="`${dataTestId || fieldName}-currency`"
          />
          
          <!-- Email input -->
          <div v-else-if="inputType === 'email'" class="email-input-container">
            <BravoInputGroup ref="emailInputRef" class="edit-email">
              <InputGroupAddon>
                <i class="pi pi-envelope"></i>
              </InputGroupAddon>
              <BravoInputText 
                v-model="editedValue"
                type="email"
                placeholder="Enter email address"
                class="email-field"
                :class="{'error-input': emailError}"
                @click.stop
                @blur="handleBlur"
                @input="clearEmailError"
                @keydown.enter.prevent="saveEdit"
                :data-testid="`${dataTestId || fieldName}-email`"
              />
            </BravoInputGroup>
            <div v-if="emailError" class="email-error-message">
              {{ emailError }}
            </div>
          </div>
          
          <!-- Phone input -->
          <BravoInputGroup v-else-if="inputType === 'phone'" ref="phoneInputRef" class="edit-phone">
            <InputGroupAddon>
              <i class="pi pi-phone"></i>
            </InputGroupAddon>
            <BravoInputMask 
              v-model="editedValue"
              mask="(*************"
              placeholder="(*************"
              class="phone-field"
              @click.stop
              @blur="handleBlur"
              @keydown.enter.prevent="saveEdit"
              :data-testid="`${dataTestId || fieldName}-phone`"
            />
          </BravoInputGroup>
          
          <!-- URL input -->
          <div v-else-if="inputType === 'url'" class="url-input-container">
            <BravoInputText 
              ref="urlInputRef"
              v-model="editedValue"
              type="url"
              placeholder="Enter URL (e.g., www.example.com)"
              class="url-field"
              :class="{'error-input': urlError}"
              @click.stop
              @blur="handleBlur"
              @input="clearUrlError"
              @keydown.enter.prevent="saveEdit"
              :data-testid="`${dataTestId || fieldName}-url`"
            />
            <div v-if="urlError" class="url-error-message">
              {{ urlError }}
            </div>
          </div>
          
          <!-- Checkbox input -->
          <Checkbox 
            v-else-if="inputType === 'checkbox'"
            ref="checkboxRef"
            :model-value="Boolean(editedValue)"
            :binary="true"
            class="edit-checkbox"
            :data-testid="`${dataTestId || fieldName}-checkbox`"
            @update:model-value="(newValue) => { editedValue = newValue; }"
          />
          
          <!-- Textarea input -->
          <BravoTextarea
            v-else-if="inputType === 'textarea'"
            ref="textareaRef"
            v-model="editedValue"
            class="edit-textarea"
            autoResize
            rows="3"
            @click.stop
            @blur="handleBlur"
            :data-testid="`${dataTestId || fieldName}-textarea`"
          />
          
          <!-- HTML Editor input -->
          <Editor
            v-else-if="isTextareaHtml"
            ref="htmlEditorRef"
            v-model="editedValue"
            class="edit-html-editor"
            :editorStyle="{ minHeight: '120px' }"
            @click.stop
            @blur="handleBlur"
            :data-testid="`${dataTestId || fieldName}-html-editor`"
          />
          
          <!-- Dropdown input with custom templates - use native PrimeVue Dropdown -->
          <Dropdown
            v-if="inputType === 'dropdown' && (slots.option || slots.footer)"
            ref="dropdownRef"
            v-model="editedValue"
            :options="options"
            :optionLabel="optionLabel"
            :optionValue="optionValue"
            :showClear="showClear"
            class="edit-dropdown"
            :data-testid="`${dataTestId || fieldName}-dropdown`"
            @click.stop
            @change="handleDropdownChange"
          >
            <!-- Pass through custom option template if provided -->
            <template v-if="slots.option" #option="slotProps">
              <slot name="option" :option="slotProps.option" />
            </template>
            <!-- Pass through footer template if provided -->
            <template v-if="slots.footer" #footer>
              <slot name="footer" />
            </template>
          </Dropdown>
          
          <!-- Dropdown input without custom templates - use BravoSelectField -->
          <BravoSelectField 
            v-else-if="inputType === 'dropdown'"
            ref="dropdownRef"
            :id="`${dataTestId || fieldName}-dropdown`"
            v-model="editedValue" 
            :options="options"
            :optionLabel="optionLabel"
            :optionValue="optionValue"
            class="edit-dropdown"
            :dataTestId="`${dataTestId || fieldName}-dropdown`"
            :showClear="showClear"
            @click.stop
            @change="handleDropdownChange"
          />
          
          <!-- MultiSelect input with footer - use native PrimeVue MultiSelect -->
          <MultiSelect
            v-if="inputType === 'multiselect' && slots.footer"
            ref="multiSelectRef"
            v-model="editedValue" 
            :options="options"
            :optionLabel="optionLabel"
            :optionValue="optionValue"
            display="chip"
            :maxSelectedLabels="3"
            class="edit-multiselect"
            :loading="isLoading"
            :filter="showFilter !== false"
            :filterPlaceholder="filterPlaceholder || `Search ${label}`"
            :data-testid="`${dataTestId || fieldName}-multiselect`"
            @filter="$emit('filter', $event)"
            @click.stop
            @change="handleMultiselectChange"
          >
            <!-- Pass through custom option template if provided -->
            <template v-if="slots.option" #option="slotProps">
              <slot name="option" :option="slotProps.option" />
            </template>
            <!-- Pass through footer template if provided -->
            <template v-if="slots.footer" #footer>
              <slot name="footer" />
            </template>
          </MultiSelect>
          
          <!-- MultiSelect input without footer - use BravoMultiSelect -->
          <BravoMultiSelect 
            v-else-if="inputType === 'multiselect'"
            ref="multiSelectRef"
            v-model="editedValue" 
            :options="options"
            :optionLabel="optionLabel"
            :optionValue="optionValue"
            display="chip"
            :maxSelectedLabels="3"
            class="edit-multiselect"
            :loading="isLoading"
            :filter="showFilter !== false"
            :filterPlaceholder="filterPlaceholder || `Search ${label}`"
            :data-testid="`${dataTestId || fieldName}-multiselect`"
            @filter="$emit('filter', $event)"
            @click.stop
            @change="handleMultiselectChange"
          >
            <!-- Pass through custom option template if provided -->
            <template v-if="slots.option" #option="slotProps">
              <slot name="option" :option="slotProps.option" />
            </template>
          </BravoMultiSelect>
          

          
          <!-- Date input -->
          <BravoDatePicker
            v-else-if="inputType === 'date'"
            ref="datePickerRef"
            v-model="editedValue"
            dateFormat="mm/dd/yy"
            class="edit-date"
            :data-testid="`${dataTestId || fieldName}-date`"
            @click.stop
            @date-select="handleDateChange"
          />
          
          <!-- DateTime input -->
          <BravoDatePicker
            v-else-if="inputType === 'datetime'"
            ref="datePickerRef"
            v-model="editedValue"
            dateFormat="mm/dd/yy"
            :showTime="true"
            timeFormat="12"
            hourFormat="12"
            class="edit-datetime"
            :data-testid="`${dataTestId || fieldName}-datetime`"
            @click.stop
          />

        </div>
        
        <div class="edit-actions-vertical">
          <Button
            v-if="!isSaving"
            icon="pi pi-check"
            class="p-button-text"
            @mousedown.stop
            @click.stop="saveEdit"
            :data-testid="`save-${dataTestId}-btn`"
          />
          <Button
            v-if="!isSaving"
            icon="pi pi-times"
            class="p-button-text p-button-danger"
            @mousedown.stop
            @click.stop="cancelEdit"
            :data-testid="`cancel-${dataTestId}-edit-btn`"
          />
          <ProgressSpinner
            v-if="isSaving"
            style="width: 1.5rem; height: 1.5rem"
            strokeWidth="4"
            :data-testid="`${dataTestId}-saving-spinner`"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.data-field {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.data-field.horizontal {
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 0.5rem;
}

.data-field.horizontal .field-value {
  flex: 0 0 70%;
}

.data-field .field-value {
  font-size: 1rem;
  color: var(--text-color-primary);
  display: flex;
  align-items: flex-start;
  gap: .5rem;
  flex-wrap: wrap;
  width: 100%;
}

.data-field .field-value i {
  color: #64748b;
  font-size: 1rem;
}

.field-value.editable-field {
  position: relative;
  cursor: pointer;
  transition: background-color 0.2s;
  margin: -6px -8px;
  padding: 6px 8px;
  border-radius: 8px;
  width: 100%;
}

.data-field.horizontal .field-value.editable-field {
  margin: 0;
  padding: 0 .5rem; /* Remove top/bottom padding, keep left/right */
}

/* Remove hover from the editable field container */
  
/* Prevent hover effect when field contains disabled content */
.field-value.editable-field:hover:has(.field-disabled) {
  background-color: transparent !important;
}

.edit-container-compact {
  display: flex;
  align-items: flex-start;
  width: 100%;
  position: relative;
  box-sizing: border-box;
  gap: 4px; /* Space between input and buttons */
}

/* Align edit content with label in vertical layout */
.data-field:not(.horizontal) .edit-container-compact {
  margin-left: -22px; /* Pull left to align input text with label */
  padding-left: 11px; /* Restore spacing for proper input positioning */
  width: calc(100% + 22px); /* Ensure full width accounting for negative margin */
  max-width: calc(100% + 22px); /* Prevent overflow issues */
}

.input-wrapper {
  flex: 1; /* Allow input to shrink and grow as needed */
  display: block;
  position: relative;
  overflow: hidden;
  min-width: 0; /* Allow shrinking below content size */
}

.input-wrapper:has(.edit-textarea),
.input-wrapper:has(.edit-html-editor) {
  overflow: visible;
  vertical-align: top;
}

.edit-input {
  width: 100%;
  box-sizing: border-box;
}

.edit-textarea {
  width: 100%;
  box-sizing: border-box;
  min-height: 3rem;
  resize: vertical;
}

.edit-textarea :deep(.p-inputtextarea),
.edit-textarea :deep(textarea) {
  width: 100% !important;
  max-width: 100%;
  word-wrap: break-word;
  white-space: pre-wrap;
  min-height: 3rem !important;
  resize: vertical !important;
  line-height: 1.5; /* Match display mode line-height */
}

.edit-html-editor {
  width: 100%;
  box-sizing: border-box;
  min-height: 120px;
}

.edit-html-editor :deep(.p-editor-container) {
  width: 100% !important;
  max-width: 100%;
}

.edit-html-editor :deep(.ql-editor) {
  min-height: 80px !important;
  font-family: inherit;
}

.edit-html-editor :deep(.ql-toolbar) {
  border-top: 1px solid var(--surface-300);
  border-left: 1px solid var(--surface-300);
  border-right: 1px solid var(--surface-300);
}

.edit-html-editor :deep(.ql-container) {
  border-bottom: 1px solid var(--surface-300);
  border-left: 1px solid var(--surface-300);
  border-right: 1px solid var(--surface-300);
}

.edit-dropdown, .edit-multiselect, .edit-textarea, .edit-date, .edit-datetime, .edit-currency, .edit-checkbox, .edit-email, .edit-phone, .edit-url, .url-input-container {
  width: 100%;
}

/* Fix PrimeVue components width */
.edit-dropdown :deep(.p-dropdown),
.edit-dropdown :deep(.p-select),
.edit-multiselect :deep(.p-multiselect),
.edit-date :deep(.p-calendar),
.edit-datetime :deep(.p-calendar),
.edit-currency :deep(.p-inputnumber),
.edit-currency :deep(.p-inputtext),
.edit-checkbox :deep(.p-checkbox),
.edit-email :deep(.p-inputgroup),
.edit-email :deep(.p-inputtext),
.edit-phone :deep(.p-inputmask),
.edit-phone :deep(.p-inputtext),
.edit-url :deep(.p-inputtext),
.edit-html-editor :deep(.p-editor) {
  width: 100% !important;
  max-width: 100%;
}

/* Remove margins from inputs to prevent layout shifts */
.edit-dropdown,
.edit-multiselect,
.edit-input,
.edit-textarea,
.edit-html-editor,
.edit-articlelabels,
.edit-date,
.edit-datetime,
.edit-currency,
.edit-checkbox,
.edit-email,
.edit-phone,
.edit-url,
.url-input-container {
  margin: 0;
}

/* Ensure overflow is visible to prevent cutoff */
.input-wrapper,
.edit-container-compact {
  overflow: visible !important;
}

.edit-dropdown :deep(.p-dropdown),
.edit-dropdown :deep(.p-select),
.edit-multiselect :deep(.p-multiselect),
.edit-date :deep(.p-calendar),
.edit-datetime :deep(.p-calendar),
.edit-currency :deep(.p-inputnumber) {
  overflow: visible !important;
}

/* Email input styling */
.email-input-container {
  position: relative;
  width: 100%;
}

.edit-email :deep(.p-inputgroup) {
  width: 100%;
}

.edit-email :deep(.p-inputgroup-addon) {
  background-color: var(--surface-100, #f8f9fa);
  border-color: var(--surface-300, #dee2e6);
  color: var(--text-color-secondary, #6c757d);
}

.email-field.error-input :deep(.p-inputtext) {
  border-color: var(--red-500, #dc2626) !important;
}

.email-input-container:has(.error-input) :deep(.p-inputgroup-addon) {
  border-color: var(--red-500, #dc2626) !important;
}

.email-error-message {
  position: absolute;
  top: calc(100% + 2px);
  left: 0;
  color: var(--red-500, #dc2626);
  font-size: 0.875rem;
  margin-top: 0.25rem;
  line-height: 1;
}

/* Phone input styling */
.edit-phone :deep(.p-inputgroup) {
  width: 100%;
}

.edit-phone :deep(.p-inputgroup-addon) {
  background-color: var(--surface-100, #f8f9fa);
  border-color: var(--surface-300, #dee2e6);
  color: var(--text-color-secondary, #6c757d);
}

/* URL input and display styling */
.url-input-container {
  position: relative;
  width: 100%;
}

.url-input-container .url-field {
  width: 100%;
}

.url-field.error-input :deep(.p-inputtext) {
  border-color: var(--red-500, #dc2626) !important;
}

.url-error-message {
  position: absolute;
  top: calc(100% + 2px);
  left: 0;
  color: var(--red-500, #dc2626);
  font-size: 0.875rem;
  margin-top: 0.25rem;
  line-height: 1;
}

.url-display-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
}

.url-link {
  color: var(--primary-650) !important;
  text-decoration: none !important;
  cursor: pointer !important;
  word-break: break-all;
  line-height: 1.4;
  transition: color 0.2s ease, text-decoration 0.2s ease;
  display: inline-block;
  max-width: 100%;
}

.url-link:hover {
  color: var(--parimary-600) !important;
  text-decoration: underline !important;
}

.url-link:visited {
  color: var(--primary-650) !important; /* Keep same as normal state */
}

/* Edit button styling for URL fields */
.edit-url-button {
  opacity: 0;
  transition: opacity 0.2s ease;
  flex-shrink: 0;
  padding: 0.25rem;
  min-width: auto;
  width: 2rem;
  height: 2rem;
}

.url-display-container:hover .edit-url-button {
  opacity: 1;
}

.edit-url-button:hover {
  background-color: var(--surface-100);
}



/* Control multiselect chips container */
.edit-multiselect :deep(.p-multiselect-label) {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Prevent multiselect chips from wrapping */
.edit-multiselect :deep(.p-multiselect-token-label) {
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
}



.edit-actions-vertical {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0; /* Prevent buttons from shrinking */
  width: auto;
  height: 36px; /* Fixed height to match typical input height */
  min-height: 36px;
}

.edit-container-compact:has(.edit-textarea) .edit-actions-vertical,
.edit-container-compact:has(.edit-html-editor) .edit-actions-vertical {
  height: auto;
  min-height: 3rem;
  justify-content: flex-start;
  padding-top: 0.25rem;
}

/* Ensure textarea edit container has proper min-height */
.edit-container-compact:has(.edit-textarea) {
  min-height: 3rem;
  align-items: flex-start;
}

/* Fix button overflow in horizontal mode for all field types */
.data-field.horizontal .edit-container-compact .input-wrapper {
  max-width: calc(100% - 44px); /* Reserve space for buttons in horizontal mode (extra 4px for scrollbar) */
}

/* Ensure all input fields respect width constraints in horizontal mode */
.data-field.horizontal .edit-textarea,
.data-field.horizontal .edit-input,
.data-field.horizontal .edit-dropdown,
.data-field.horizontal .edit-multiselect,
.data-field.horizontal .edit-currency,
.data-field.horizontal .edit-email,
.data-field.horizontal .edit-phone,
.data-field.horizontal .url-input-container {
  max-width: 100% !important;
}

.data-field.horizontal .edit-textarea :deep(.p-inputtextarea),
.data-field.horizontal .edit-textarea :deep(textarea),
.data-field.horizontal .edit-input :deep(.p-inputtext),
.data-field.horizontal .edit-dropdown :deep(.p-dropdown),
.data-field.horizontal .edit-dropdown :deep(.p-select),
.data-field.horizontal .edit-multiselect :deep(.p-multiselect),
.data-field.horizontal .edit-currency :deep(.p-inputnumber),
.data-field.horizontal .edit-currency :deep(.p-inputtext),
.data-field.horizontal .edit-email :deep(.p-inputgroup),
.data-field.horizontal .edit-email :deep(.p-inputtext),
.data-field.horizontal .edit-phone :deep(.p-inputmask),
.data-field.horizontal .edit-phone :deep(.p-inputtext),
.data-field.horizontal .url-input-container .url-field :deep(.p-inputtext) {
  max-width: 100% !important;
  box-sizing: border-box !important;
}

.edit-actions-vertical button {
  flex: 0 0 32px;
  height: 64px;
  width: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.display-mode {
  display: flex;
  align-items: center;
  width: 100%;
  cursor: pointer;
  border-radius: 8px;
  padding: 0px 11px;
  gap: 0.5rem;
  min-height: 37px;
  box-sizing: border-box;
  transition: background-color 0.2s;
}

/* Special styling for textarea display mode */
.display-mode:has(.textarea-display) {
  align-items: flex-start;
  padding: 0.5rem 0.75rem; /* Match textarea internal padding exactly */
  min-height: 37px; /* Use same min-height as other fields for short content */
}

/* Align text content with label in vertical layout */
.data-field:not(.horizontal) .display-mode {
  margin-left: -11px; /* Pull left to align text with label */
}

.display-mode:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.display-mode i {
  margin-top: 4px;
}

.clickable-value {
  cursor: pointer;
}

.textarea-display {
  white-space: pre-wrap;
  word-wrap: break-word;
  line-height: 1.5;
  width: 100%;
  /* Remove padding and margin since parent container now handles it */
}

.html-display {
  line-height: 1.4;
  word-wrap: break-word;
}

.html-display .html-content {
  /* Reset styles for rendered HTML content */
  font-family: inherit;
  font-size: inherit;
  color: inherit;
  line-height: 1.4;
}

.html-display .html-content p {
  margin: 0 0 0.5rem 0;
}

.html-display .html-content p:last-child {
  margin-bottom: 0;
}

.html-display .html-content ul,
.html-display .html-content ol {
  margin: 0 0 0.5rem 0;
  padding-left: 1.5rem;
}

.html-display .html-content h1,
.html-display .html-content h2,
.html-display .html-content h3,
.html-display .html-content h4,
.html-display .html-content h5,
.html-display .html-content h6 {
  margin: 0 0 0.5rem 0;
  font-weight: 600;
}

.html-display .html-content blockquote {
  margin: 0 0 0.5rem 0;
  padding-left: 1rem;
  border-left: 3px solid var(--surface-300);
  font-style: italic;
}

.html-display .html-content strong {
  font-weight: 600;
}

.html-display .html-content em {
  font-style: italic;
}

.html-display .empty-text {
  color: var(--icon-color-primary);
  font-style: italic;
}

.empty-value {
  color: var(--icon-color-primary);
  margin-left: 1px; /* Add 1px left margin for better spacing of em dash */
}

.chips-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}



.chip-items {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.25rem;
}

.chip-item {
  cursor: pointer;
  transition: transform 0.1s ease;
}

.chip-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* PrimeVue Multiselect Filter Styling */
:deep(.p-multiselect-filter-container) {
  margin-bottom: 0.5rem;
}

:deep(.p-multiselect-filter) {
  width: 100%;
  padding: 0.5rem;
}

:deep(.p-multiselect-panel) {
  max-height: 400px;
  overflow-y: auto;
}



// Add these new styles for BravoLabel
.article-field-label {
  flex: 0 0 30%;
  display: block;
}

.data-field.horizontal .article-field-label {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0;
  margin-right: 1rem;
  padding-top: 4px;
}

/* Disabled field styles */
.field-disabled {
    cursor: default !important;
    opacity: 1;
    pointer-events: none;
}

.field-disabled:hover {
    background-color: transparent !important;
}

/* Also prevent hover on chip items when disabled */
.field-disabled .chip-item:hover {
    transform: none !important;
}

/* Prevent pointer cursor on disabled fields and all their children */
.field-disabled,
.field-disabled *,
.field-disabled .clickable-value,
.field-disabled .chip-item,
.field-disabled .display-mode {
    cursor: default !important;
}

/* But allow custom display content to be interactive even when field is disabled */
.field-disabled .custom-display-content {
    pointer-events: auto !important;
}

.field-disabled .custom-display-content * {
    pointer-events: auto !important;
    cursor: auto !important;
}

.field-disabled .custom-display-content a {
    cursor: pointer !important;
    color: var(--primary-color, #3b82f6) !important;
    text-decoration: underline !important;
}

/* Disable entire field section when not editable */
.field-section-disabled {
    pointer-events: none !important;
    cursor: default !important;
    opacity: 1;
}

.field-section-disabled:hover {
    background-color: transparent !important;
}

/* But allow custom display content to be interactive even when field section is disabled */
.field-section-disabled .custom-display-content {
    pointer-events: auto !important;
}

.field-section-disabled .custom-display-content * {
    pointer-events: auto !important;
    cursor: auto !important;
}

.field-section-disabled .custom-display-content a {
    cursor: pointer !important;
    color: var(--primary-color, #3b82f6) !important;
    text-decoration: underline !important;
}

/* Allow URL links and edit buttons to be interactive even when field is disabled */
.field-section-disabled .url-link,
.field-section-disabled .edit-url-button {
    pointer-events: auto !important;
    cursor: pointer !important;
}

.field-disabled .url-link,
.field-disabled .edit-url-button {
    pointer-events: auto !important;
    cursor: pointer !important;
}

/* Display checkbox styling */
.display-checkbox {
  cursor: pointer;
}

.display-checkbox :deep(.p-checkbox) {
  cursor: pointer;
}

.display-checkbox :deep(.p-checkbox-box) {
  cursor: pointer;
}

/* Edit checkbox positioning - align with display mode */
.edit-checkbox {
  /* Position to match display-mode container positioning */
  padding: 0px 11px; /* Match display-mode padding exactly */
  min-height: 37px; /* Match display-mode min-height */
  display: flex;
  align-items: center; /* Center vertically like display-mode */
  margin: 0; /* Reset any default margins */
}

/* In vertical layout, align checkbox with display-mode positioning */
.data-field:not(.horizontal) .edit-checkbox {
  margin-left: -11px; /* Match display-mode margin-left */
  padding-left: 22px; /* Add extra left padding in vertical layout to align with label text */
}

/* Custom display content styling */
.custom-display-content {
  /* Allow custom content to handle its own click events */
  pointer-events: auto;
}

/* Link styling within custom display content */
.custom-display-content a {
  color: var(--primary-color, #3b82f6) !important;
  text-decoration: underline !important;
  cursor: pointer !important;
}

.custom-display-content a:hover {
  color: var(--primary-color-dark, #2563eb) !important;
  text-decoration: underline !important;
}

.custom-display-content a:visited {
  color: var(--primary-color-dark, #2563eb) !important;
}
</style> 