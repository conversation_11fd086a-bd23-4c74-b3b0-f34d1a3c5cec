<script setup lang="ts">
import { useKnowledgeAPI } from '@/composables/services/useKnowledgeAPI';
import BravoAccordionContent from '@services/ui-component-library/components/BravoAccordionContent.vue';
import BravoAccordionHeader from '@services/ui-component-library/components/BravoAccordionHeader.vue';
import BravoAccordionPanel from '@services/ui-component-library/components/BravoAccordionPanel.vue';
import BravoLabel from '@services/ui-component-library/components/BravoLabel.vue';
import BravoBody from '@services/ui-component-library/components/BravoTypography/BravoBody.vue';
import { storeToRefs } from 'pinia';
import Button from 'primevue/button';
import Dropdown from 'primevue/dropdown';
import ProgressSpinner from 'primevue/progressspinner';
import Skeleton from 'primevue/skeleton';
import ToggleSwitch from 'primevue/toggleswitch';
import { useToast } from 'primevue/usetoast';
import { computed, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import draggable from 'vuedraggable';
import { useKnowledgeStore } from '../stores/knowledge';

// Define interface for related article objects
interface IRelatedArticle {
  val: string;
  id: string;
  lbl: string;
  sub_title?: string | null;
  short_name?: string;
  root_parent_id?: string;
  owner_partner_id?: string;
  partner_ids?: string[];
  internal_team_ids?: string[];
  visibility?: number;
  c__d_visibility?: string;
  c__d_status?: string;
  keywords?: string | null;
  updated?: string;
  url?: string;
}

const { t } = useI18n();

// Define props
const props = defineProps<{
  article: any;
  isLoading?: boolean;
  isWideMode?: boolean;
  isEditing?: boolean;
}>();

// Define emits
const emit = defineEmits<{
  (e: 'update', article: any): void;
  (e: 'update:field', field: string, value: any): void;
}>();

// Get knowledge store and API
const knowledgeStore = useKnowledgeStore();
const { availableKnowledgeBases, loadingKnowledgeBases } = storeToRefs(knowledgeStore);
const knowledgeAPI = useKnowledgeAPI();
const toast = useToast();

// Local state
const isSaving = ref(false);
const selectedDropdownItem = ref<any>(null);
const justSaved = ref(false);

// Edited values
const editedAutoGenArticles = ref(false);
const editedRelatedArticles = ref<string[]>([]);

// Initialize values from article
const initializeValues = () => {
  editedAutoGenArticles.value = props.article.auto_gen_articles || false;
  editedRelatedArticles.value = props.article.bc__tags_object_kb || [];
};

// Watch for article changes
watch(() => [props.article.auto_gen_articles, props.article.bc__tags_object_kb], () => {
  // Don't reset values if we just saved (to preserve the order we just saved)
  if (!justSaved.value) {
    initializeValues();
  } else {
    // Reset the flag after the save-triggered update
    justSaved.value = false;
  }
}, { immediate: true });

// Computed properties
const displayValue = computed(() => {
  return props.article.auto_gen_articles ? 'Auto Generated' : 'Manual Selection';
});

const availableOptions = computed(() => {
  return availableKnowledgeBases.value.filter(kb => 
    !editedRelatedArticles.value.includes(kb.id)
  );
});

const selectedArticlesList = computed((): IRelatedArticle[] => {
  const articles = editedRelatedArticles.value.map(id => {
    const article = availableKnowledgeBases.value.find(kb => kb.id === id);
    return article as IRelatedArticle || { 
      val: id,
      id, 
      lbl: `Unknown Article (${id})`,
      c__d_status: 'Unknown'
    } as IRelatedArticle;
  }).filter(Boolean);
  console.log('selectedArticlesList', articles);
  return articles;
});

const isReady = computed(() => {
  return !loadingKnowledgeBases.value && availableKnowledgeBases.value.length > 0;
});

// Check if there are any changes
const hasChanges = computed(() => {
  const originalAutoGen = props.article.auto_gen_articles || false;
  const originalRelatedArticles = props.article.bc__tags_object_kb || [];
  
  // Check if auto-generate setting changed
  if (editedAutoGenArticles.value !== originalAutoGen) {
    return true;
  }
  
  // Check if related articles changed (only relevant when auto-gen is off)
  if (!editedAutoGenArticles.value) {
    // Compare arrays - check length first
    if (editedRelatedArticles.value.length !== originalRelatedArticles.length) {
      return true;
    }
    
    // Check if all items are the same and in the same order
    return !editedRelatedArticles.value.every((id, index) => id === originalRelatedArticles[index]);
  }
  
  return false;
});

// Functions
const cancelEdit = () => {
  initializeValues();
  selectedDropdownItem.value = null;
};

const saveEdit = async () => {
  isSaving.value = true;
  try {
    const revisionData: any = {
      auto_gen_articles: editedAutoGenArticles.value
    };
    
    // Only include related articles if auto-generate is off
    if (!editedAutoGenArticles.value) {
      revisionData.bc__tags_object_kb = editedRelatedArticles.value;
    }
    
    const success = await submitRelatedArticlesUpdate(revisionData);
    
    if (success) {
      // Set flag to prevent watch from resetting our values
      justSaved.value = true;
      
      // Update the article object and emit events
      const updatedArticle = { 
        ...props.article, 
        auto_gen_articles: editedAutoGenArticles.value,
        bc__tags_object_kb: editedAutoGenArticles.value ? [] : editedRelatedArticles.value
      };
      
      emit('update', updatedArticle);
      emit('update:field', 'auto_gen_articles', editedAutoGenArticles.value.toString());
      if (!editedAutoGenArticles.value) {
        emit('update:field', 'bc__tags_object_kb', editedRelatedArticles.value);
      }
      
    }
  } catch (error: any) {
    console.error('RelatedArticlesBlock: Error saving related articles:', error);
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: error?.message || 'Failed to update related articles',
      life: 3000,
    });
  } finally {
    isSaving.value = false;
  }
};

const submitRelatedArticlesUpdate = async (revisionData: any): Promise<boolean> => {
  try {
    const revisionId = props.article.id;
    const revision = { 
      id: revisionId,
      ...revisionData
    };
    
    const response = await knowledgeAPI.putRevisions([revision]);
    
    if (response && response.success) {
      toast.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Related articles updated successfully',
        life: 3000,
      });
      
      await new Promise((resolve) => setTimeout(resolve, 300));
      return true;
    } else {
      toast.add({
        severity: 'error',
        summary: 'Error',
        detail: response?.message || 'Failed to update related articles',
        life: 3000,
      });
      return false;
    }
  } catch (error: any) {
    console.error('RelatedArticlesBlock: API error:', error);
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: error?.message || 'Failed to update related articles',
      life: 3000,
    });
    return false;
  }
};

const handleDropdownSelect = () => {
  if (selectedDropdownItem.value) {
    const newId = selectedDropdownItem.value.id;
    if (!editedRelatedArticles.value.includes(newId)) {
      editedRelatedArticles.value = [...editedRelatedArticles.value, newId];
    }
    selectedDropdownItem.value = null;
  }
};

const removeArticle = (articleId: string) => {
  editedRelatedArticles.value = editedRelatedArticles.value.filter(id => id !== articleId);
};

const handleDragEnd = () => {
  // Ensure the change is detected
  editedRelatedArticles.value = [...editedRelatedArticles.value];
};
</script>

<template>
  <BravoAccordionPanel value="related-articles" data-testid="related-articles-panel">
    <BravoAccordionHeader data-testid="related-articles-header">
      Related Articles
    </BravoAccordionHeader>
    <BravoAccordionContent data-testid="related-articles-content">
      <div v-if="isLoading" class="skeleton-content">
        <Skeleton height="1.5rem" class="mb-2" />
        <Skeleton height="1.5rem" class="mb-2" />
        <Skeleton height="1.5rem" class="mb-2" />
      </div>
      
            <div v-else class="related-articles-content">
        <!-- Always show toggle and content -->
        <div class="main-section">
          <div class="edit-header">
            <div class="auto-gen-toggle">
                             <ToggleSwitch
                 v-model="editedAutoGenArticles"
                 :disabled="!props.isEditing"
                 @click.stop
                 data-testid="auto-gen-articles-toggle"
               />
              <BravoLabel text="Auto Generate" class="toggle-label" />
            </div>
            
                        <div class="edit-actions" v-if="props.isEditing && (hasChanges || isSaving)">
              <Button
                v-if="!isSaving"
                icon="pi pi-check"
                class="p-button-text p-button-success"
                @click="saveEdit"
                data-testid="save-related-articles-btn"
              />
              <Button
                v-if="!isSaving"
                icon="pi pi-times"
                class="p-button-text p-button-danger"
                @click="cancelEdit"
                data-testid="cancel-related-articles-btn"
              />
              <ProgressSpinner
                v-if="isSaving"
                style="width: 1.5rem; height: 1.5rem"
                strokeWidth="4"
                data-testid="related-articles-saving-spinner"
              />
            </div>
          </div>
          
                    <!-- Content Section -->
          <div class="content-section">
            <!-- Show current selection in display mode -->
            <div v-if="!props.isEditing" class="display-content">
              <div v-if="!props.article.auto_gen_articles && props.article.bc__tags_object_kb?.length" class="selected-preview">
                <BravoLabel text="Selected Articles" class="section-label" />
                <div class="preview-list">
                  <div 
                    v-for="articleId in props.article.bc__tags_object_kb.slice(0, 5)" 
                    :key="articleId"
                    class="preview-item"
                  >
                    <span class="preview-title">
                      {{ availableKnowledgeBases.find(kb => kb.id === articleId)?.lbl || 'Unknown Article' }}
                    </span>
                  </div>
                  <div v-if="props.article.bc__tags_object_kb.length > 5" class="preview-more">
                    +{{ props.article.bc__tags_object_kb.length - 5 }} more articles
                  </div>
                </div>
              </div>
              <div v-else-if="!props.article.auto_gen_articles" class="no-selection">
                <BravoBody class="no-selection-text">No articles selected</BravoBody>
              </div>
              <div v-else class="auto-gen-info">
                <BravoBody class="auto-gen-text">Articles are automatically generated based on content</BravoBody>
              </div>
            </div>
            
            <!-- Edit mode content -->
            <template v-else-if="props.isEditing && !editedAutoGenArticles">
              <!-- Add Article Dropdown -->
              <div class="add-article-section">
                <BravoLabel text="Add Articles" class="section-label" />
                <Dropdown
                  v-model="selectedDropdownItem"
                  :options="availableOptions"
                  optionLabel="lbl"
                  :loading="loadingKnowledgeBases"
                  placeholder="Search for Articles"
                  class="add-dropdown"
                  :disabled="!props.isEditing || !isReady"
                  filter
                  filterPlaceholder="Search articles..."
                  @change="handleDropdownSelect"
                  @click.stop
                  data-testid="add-article-dropdown"
                />
              </div>
              
              <!-- Selected Articles List (Draggable) -->
              <div v-if="selectedArticlesList.length > 0" class="selected-articles-section">
                <BravoLabel text="Selected Articles" class="section-label" />
                <draggable
                  v-model="editedRelatedArticles"
                  item-key="id"
                  @end="handleDragEnd"
                  class="draggable-list"
                  ghost-class="ghost-item"
                  chosen-class="chosen-item"
                  drag-class="drag-item"
                >
                  <template #item="{ element: articleId }">
                    <div class="selected-article-item">
                      <div class="drag-handle">
                        <i class="pi pi-bars" />
                      </div>
                      <div class="article-content">
                        <span class="article-title">
                          {{ selectedArticlesList.find(a => a.id === articleId)?.lbl || 'Unknown Article' }}
                        </span>
                        <span class="article-status">
                          {{ selectedArticlesList.find(a => a.id === articleId)?.c__d_status || '' }} 
                        </span>
                      </div>
                      <Button
                        icon="pi pi-times"
                        class="p-button-text p-button-sm remove-btn"
                        @click="removeArticle(articleId)"
                        :disabled="!props.isEditing"
                        data-testid="remove-article-btn"
                      />
                    </div>
                  </template>
                </draggable>
              </div>
            </template>
          </div>
        </div>
      </div>
    </BravoAccordionContent>
  </BravoAccordionPanel>
</template>

<style scoped lang="scss">
.skeleton-content {
  padding: 1rem 0;
}

.skeleton-content .p-skeleton {
  margin-bottom: 0.75rem;
}

.related-articles-content {
  padding: 0.5rem 0;
}

// Main Section Styles
.main-section {
  width: 100%;
}

.edit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 0.75rem;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.auto-gen-toggle {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.toggle-label {
  font-size: 0.875rem;
  color: #374151;
  font-weight: 500;
}

.edit-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

// Content Section Styles
.content-section {
  margin-top: 1rem;
}

.display-content {
  padding: 0.75rem;
  border-radius: 8px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
}

.selected-preview {
  .preview-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-top: 0.75rem;
  }
  
  .preview-item {
    padding: 0.75rem;
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    
    &:hover {
      background: #f1f5f9;
      border-color: #cbd5e1;
    }
  }
  
  .preview-title {
    color: #374151;
    font-weight: 500;
    line-height: 1.4;
  }
  
  .preview-more {
    padding: 0.75rem;
    background: #f1f5f9;
    border: 1px dashed #cbd5e1;
    border-radius: 6px;
    font-size: 0.875rem;
    color: #64748b;
    text-align: center;
    font-style: italic;
  }
}

.no-selection {
  text-align: center;
  padding: 2rem 1rem;
  
  .no-selection-text {
    color: #64748b;
    font-style: italic;
  }
}

.auto-gen-info {
  text-align: center;
  padding: 2rem 1rem;
  
  .auto-gen-text {
    color: #64748b;
    font-style: italic;
  }
}

.section-label {
  font-size: 0.875rem;
  color: #374151;
  font-weight: 600;
  margin-bottom: 0.75rem;
  display: block;
}

// Selected Articles Section
.selected-articles-section {
  margin-bottom: 1.5rem;
}

.draggable-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.selected-article-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  
  &:hover {
    background: #f8fafc;
    border-color: #cbd5e1;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  }
}

.drag-handle {
  display: flex;
  align-items: center;
  color: #64748b;
  cursor: grab;
  padding: 0.25rem;
  
  &:active {
    cursor: grabbing;
  }
  
  i {
    font-size: 1rem;
  }
}

.article-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.article-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: #1e293b;
  line-height: 1.4;
}

.article-status {
  font-size: 0.75rem;
  color: #64748b;
}

.remove-btn {
  color: #ef4444 !important;
  
  &:hover {
    background: rgba(239, 68, 68, 0.1) !important;
  }
}

// Drag and Drop States
.ghost-item {
  opacity: 0.5;
  background: #e2e8f0;
}

.chosen-item {
  background: #dbeafe;
  border-color: #3b82f6;
}

.drag-item {
  transform: rotate(2deg);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

// Add Article Section
.add-article-section {
  margin-bottom: 1rem;
}

.add-dropdown {
  width: 100%;
  
  :deep(.p-dropdown) {
    width: 100%;
    font-size: 0.875rem;
    border-radius: 8px;
  }
  
  :deep(.p-dropdown:not(.p-disabled).p-focus) {
    outline: 0 none;
    box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.2);
    border-color: #3b82f6;
  }
  
  :deep(.p-dropdown-label) {
    color: #64748b;
  }
  
  :deep(.p-dropdown-panel) {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    max-height: 300px;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .edit-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .edit-actions {
    justify-content: center;
  }
  
  .selected-article-item {
    padding: 0.75rem;
  }
  
  .article-title {
    font-size: 0.8rem;
  }
}
</style> 