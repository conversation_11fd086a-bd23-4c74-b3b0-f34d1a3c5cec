<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import BravoFormField from './BravoFormField.vue';
import { useKnowledgeStore } from '../stores/knowledge';
import { useKnowledgeAPI } from '@/composables/services/useKnowledgeAPI';
import { useToast } from 'primevue/usetoast';

// Define props
const props = defineProps<{
  article: any;
  isLoading?: boolean;
  isSaving?: boolean;
  isHorizontal?: boolean;
  isEditing?: boolean;
  onSubmit?: (field: string, value: any) => Promise<boolean>;
}>();

// Define emits
const emit = defineEmits<{
  (e: 'update', article: any): void;
}>();

// Get knowledge store and API for labels data
const knowledgeStore = useKnowledgeStore();
const knowledgeAPI = useKnowledgeAPI();
const toast = useToast();

// Track loading state for labels
const loadingLabels = ref(false);
const labelOptions = ref<any[]>([]);

// Track loading state locally
const isLocalSaving = ref(false);

// Reference to the BravoFormField component
const formInputRef = ref<InstanceType<typeof BravoFormField> | null>(null);

// Computed to get current library ID
const currentLibraryId = computed(() => {
  return props.article.root_parent_id || props.article.library_id || 'default';
});

// Get current labels value
const currentLabels = computed(() => {
  return props.article.bc__tags_object_kb_labels || [];
});

// Check if labels are still loading (showing as IDs)
const isLabelsDisplayLoading = computed(() => {
  if (!props.article.bc__tags_object_kb_labels) return false;
  
  // If we're explicitly loading, show loading state
  if (loadingLabels.value) return true;
  
  // Check if any labels are still missing (would show as IDs)
  const missingLabels = props.article.bc__tags_object_kb_labels.filter((id: string) => !knowledgeStore.labelMap[id]);
  return missingLabels.length > 0;
});

// For chip display - get label names from IDs
const selectedLabelNames = computed(() => {
  if (!currentLabels.value || currentLabels.value.length === 0) return [];
  
  // Force reactivity by accessing the labelMap through the store's reactive state
  const labelMap = knowledgeStore.labelMap;
  
  const names = currentLabels.value.map((id: string) => {
    const name = labelMap[id];
    return name || id;
  });
  
  return names;
});

// Get labels display value
const labelsDisplayValue = computed(() => {
  if (selectedLabelNames.value.length === 0) return 'No labels';
  if (isLabelsDisplayLoading.value) return 'Loading labels...';
  return selectedLabelNames.value;
});

// Interface for label API response
interface LabelResponse {
  success: boolean;
  pl__kb_labels: Array<{
    id: string;
    val: string;
    lbl: string;
    path: string;
    actual_path: string;
    indent_lbl: string;
    root_kb_id: string;
    iconCls: string;
    icon: string | null;
    icon_url: string;
    icon_alt_text: string | null;
    icon_visible: boolean;
    url: string;
  }>;
  current_server_time: string;
}

// Interface for label options
interface LabelOption {
  label: string;
  value: string;
  fullPath: string;
}

// Load labels for the selected library and create flattened options
const loadLabelsForLibrary = async (libraryId: string) => {
  if (!libraryId || libraryId === 'default') {
    return;
  }

  loadingLabels.value = true;
  labelOptions.value = [];

  try {
    const query = {
      sAction: 'metaKBLabels',
      query: JSON.stringify([{
        property: 'root_kb_id',
        value: libraryId,
      }, {
        property: 'excludeId',
        value: 'kb.KBTreeModel-2',
      }, {
        property: 'excludeChildrenId',
        value: true,
      }]),
    };
    
    const labels = await knowledgeAPI.loadLabels(query) as LabelResponse;
    
    if (labels && labels?.pl__kb_labels?.length > 0) {
      // Filter labels to only include those belonging to the selected library
      const filteredLabels = labels?.pl__kb_labels?.filter(label => label.root_kb_id === libraryId);

      // Create flattened options with full path display
      labelOptions.value = createFlattenedOptions(filteredLabels);

      // Update the knowledge store's labelMap with the loaded labels
      const labelUpdates: Record<string, string> = {};
      filteredLabels.forEach(label => {
        labelUpdates[label.id] = label.lbl;
      });
      knowledgeStore.updateLabelMap(labelUpdates);
    } else {
      labelOptions.value = [];
    }
  } catch (error) {
    console.error('Error loading labels:', error);
    labelOptions.value = [];
  } finally {
    loadingLabels.value = false;
  }
};

// Function to create flattened options from labels with full path display
const createFlattenedOptions = (labels: any[]): LabelOption[] => {
  // Sort labels by path to ensure proper hierarchy
  const sortedLabels = [...labels].sort((a, b) => a.path.localeCompare(b.path));
  
  return sortedLabels.map(label => {
    // Create display path by replacing "/" with " / " for better readability
    const displayPath = label.path.replace(/\//g, ' / ');
    
    return {
      label: displayPath,
      value: label.id,
      fullPath: label.path
    };
  });
};

// Watch for library changes to reload labels - watch the article properties directly
watch(() => props.article.root_parent_id || props.article.library_id, async (newLibraryId, oldLibraryId) => {
  if (newLibraryId && newLibraryId !== oldLibraryId && oldLibraryId !== undefined) {
    // Only clear selected labels if the library actually changed (not on initial load)
    labelOptions.value = [];
    await loadLabelsForLibrary(newLibraryId);
  } else if (newLibraryId && oldLibraryId === undefined) {
    // Initial load - don't clear selected labels, just load options
    await loadLabelsForLibrary(newLibraryId);
  } else if (!newLibraryId) {
    labelOptions.value = [];
  }
}, { immediate: true });



// Handle form field updates
const handleUpdate = (fieldName: string, value: any) => {
  // Update the article object and emit events
  const updatedArticle = { ...props.article, [fieldName]: value };
  emit('update', updatedArticle);
};

// Handle submission start
const handleSubmitStart = () => {
  isLocalSaving.value = true;
};

// Handle form field save
const handleSave = async (fieldName: string, value: any) => {
  try {
    // Check if the arrays are different
    const currentLabelsArray = props.article.bc__tags_object_kb_labels || [];
    const isChanged = 
      value.length !== currentLabelsArray.length ||
      value.some((id: string) => !currentLabelsArray.includes(id)) ||
      currentLabelsArray.some((id: string) => !value.includes(id));
      
    // Only submit if the values have changed
    if (isChanged && props.onSubmit) {
      const success = await props.onSubmit(fieldName, value);
      formInputRef.value?.handleSaveComplete(success);
      if (success) {
        // Update the article object and emit events
        const updatedArticle = { ...props.article, [fieldName]: value };
        emit('update', updatedArticle);
      }
    } else {
      // No change needed, just complete
      formInputRef.value?.handleSaveComplete(true);
    }
  } catch (error) {
    console.error('Error saving labels:', error);
    formInputRef.value?.handleSaveComplete(false);
  } finally {
    isLocalSaving.value = false;
  }
};

// Handle cancel event
const handleCancel = () => {
  // Reset handled by BravoFormField
};
</script>

<template>
  <BravoFormField
    ref="formInputRef"
    label="Labels"
    field-name="bc__tags_object_kb_labels"
    :value="currentLabels"
    :display-value="labelsDisplayValue"
    input-type="multiselect"
    display-type="chips"
    :options="labelOptions"
    option-label="label"
    option-value="value"
    :is-loading="loadingLabels"
    :is-horizontal="isHorizontal"
    icon-class="pi pi-tag"
    :is-saving="isLocalSaving || isSaving"
    no-value-text="No labels"
    data-test-id="article-labels-field"
    filter-placeholder="Search labels"
    :show-filter="true"
    :is-editing="isEditing"
    :show-clear="true"
    @update="handleUpdate"
    @save="handleSave"
    @submit-start="handleSubmitStart"
    @cancel="handleCancel"
  />
</template>

<style scoped>
/* No additional styles needed - BravoFormField handles everything */
</style> 