<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import BravoForm<PERSON>ield from './BravoFormField.vue';
import { useKnowledgeStore } from '../stores/knowledge';

// Define props
const props = defineProps<{
  article: any;
  isLoading?: boolean;
  isSaving?: boolean;
  isHorizontal?: boolean;
  isEditing?: boolean;
  onSubmit?: (field: string, value: any) => Promise<boolean>;
}>();

// Define emits
const emit = defineEmits<{
  (e: 'update', article: any): void;
}>();

// Get knowledge store for teams data
const knowledgeStore = useKnowledgeStore();

// Helper to get team name from ID
const getTeamNameFromId = (id: string) => {
  const team = knowledgeStore.teams.find(t => t.val === id || t.id === id);
  return team ? team.lbl || team.name : id;
};

// Compute the display values for the teams
const teamDisplayValues = computed(() => {
  const teamIds = props.article.internal_team_ids || [];
  if (teamIds.length === 0) {
    return 'No team restrictions';
  }
  return teamIds.map((id: string) => getTeamNameFromId(id));
});

// Compute if there are any team restrictions
const hasTeamRestrictions = computed(() => {
  return props.article.internal_team_ids && props.article.internal_team_ids.length > 0;
});

// Track the edited value locally
const editedTeamAccess = ref<string[]>([]);

// Initialize the edited team access when the article changes
watch(() => props.article.internal_team_ids, (newTeamIds) => {
  editedTeamAccess.value = newTeamIds ? [...newTeamIds] : [];
}, { immediate: true });

// Track loading state locally
const isLocalSaving = ref(false);

// Reference to the BravoFormField component
const formInputRef = ref<InstanceType<typeof BravoFormField> | null>(null);

// Handle the update event from BravoFormField
const handleUpdate = (fieldName: string, value: any) => {
  editedTeamAccess.value = value;
};

// Handle submission start
const handleSubmitStart = () => {
  isLocalSaving.value = true;
};

// Handle the save event from BravoFormField
const handleSave = async (fieldName: string, value: any) => {
  try {
    // Check if the arrays are different
    const currentTeamIds = props.article.internal_team_ids || [];
    const isChanged = 
      value.length !== currentTeamIds.length ||
      value.some((id: string) => !currentTeamIds.includes(id)) ||
      currentTeamIds.some((id: string) => !value.includes(id));
      
    // Only submit if the values have changed
    if (isChanged && props.onSubmit) {
      const success = await props.onSubmit('internal_team_ids', value);
      formInputRef.value?.handleSaveComplete(success);
    } else {
      // No change needed, just complete
      formInputRef.value?.handleSaveComplete(true);
    }
  } catch (error) {
    console.error('Error saving team access:', error);
    formInputRef.value?.handleSaveComplete(false);
  } finally {
    isLocalSaving.value = false;
  }
};

// Handle cancel event
const handleCancel = () => {
  // Reset the edited value back to the original
  editedTeamAccess.value = props.article.internal_team_ids ? [...props.article.internal_team_ids] : [];
};

// Only show the component if team access is relevant (based on visibility)
const showComponent = computed(() => {
  // Use the numeric visibility field if present, otherwise fallback to mapping
  const getVisibilityValueFromArticle = (article: any) => {
    if (article.c__d_visibility === 'Internal' || article.isPrivate) {
      return 1;
    } else if (article.c__d_visibility === 'Partner Ecosystem') {
      return 2;
    }
    return 0; // Public by default
  };

  const vis = typeof props.article.visibility === 'number' 
    ? props.article.visibility 
    : getVisibilityValueFromArticle(props.article);
  return vis === 1 || vis === 2;
});
</script>

<template>
  <BravoFormField
    ref="formInputRef"
    v-if="showComponent"
    label="Team Access"
    fieldName="team-access"
    :value="editedTeamAccess"
    :displayValue="teamDisplayValues"
    inputType="multiselect"
    displayType="chips"
    :options="knowledgeStore.teams"
    optionLabel="lbl"
    optionValue="val"
    :isLoading="isLoading || knowledgeStore.loading"
    :isHorizontal="isHorizontal"
    :isSaving="isLocalSaving || isSaving"
    :isEditing="isEditing"
    iconClass="pi pi-users"
    noValueText="No team restrictions"
    dataTestId="article-team-access"
    showFilter
    filterPlaceholder="Search teams..."
    @update="handleUpdate"
    @save="handleSave"
    @submit-start="handleSubmitStart"
    @cancel="handleCancel"
  />
</template> 