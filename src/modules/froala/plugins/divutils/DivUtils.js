/**
 * Div Utilities Plugin for Froala Editor
 * Provides utilities for managing divs like tabs and accordions
 * No Ext dependency
 */
import FroalaEditor from "froala-editor";

const divutils = function (editor) {
  // Initialize the plugin
  function _init() {
    // Add popup template for deletion confirmation
    FroalaEditor.POPUP_TEMPLATES["tabs.deletion"] = "[_BUTTONS_]";

    // Setup click handler for deleting divs
    editor.events.on("click", function (e) {
      const clickedEl = editor.selection.element();
      if (!clickedEl) return;
    
      // Ensure clicked element is exactly <p class="tabs-title"> or <p class="accordion-header">
      if (clickedEl.classList.contains("tab-title") || clickedEl.classList.contains("accordion-header"))
       {
        const $popup = editor.popups.get("tabs.deletion");
    
      // Toggle popup state
      // editor.deletePopupShown = !editor.deletePopupShown;
    
      const rect = clickedEl.getBoundingClientRect();
      const left = rect.right;
      const top = rect.top + 20;
    
      $popup.css({
        top: `${top}px`,
        left: `${left}px`,
        height: "55px",
        width: "45px",
        padding: "5px",
        transform: "translate(-50%, -50%)",
      });
    
      editor.selection.save();
    
      // if (!editor.deletePopupShown) {
      //   editor.popups.hide("tabs.deletion");
      // } else {
        editor.popups.show("tabs.deletion");
      // }
    
      editor.selection.restore();
      } else {
        return
      }
    
     
    });

    // Register the remove command if not already defined
    if (!FroalaEditor.COMMANDS.removeDiv) {
      // Define the icon if not already defined
      if (!FroalaEditor.ICONS.removeDiv) {
        FroalaEditor.DefineIcon("removeDiv", {
            template: 'relay_glyphs',
            NAME: Glyphs.getIconName('editor-paragraph-more-16'),
        });
      }

      FroalaEditor.RegisterCommand("removeDiv", {
        title: "Delete",
        icon: "removeDiv",
        callback: function () {
          const targetElement = this.selection.get().anchorNode.parentElement;
          const getDiv = targetElement.closest(
            ".tabs-panel, .accordion-panel"
          );
          const target = this.selection.element();
          let tabChild = false;

          if(getDiv.className === "tabs-panel"){
            const parent = target.parentElement;
            if(parent.childNodes.length >1){
              const classlist = target.classList;
              const numericClass = Array.from(classlist).find(cls => /^\d+$/.test(cls));
              const panelId = `panel${numericClass}`;
              const contentEl = getDiv.querySelector(`#${panelId}`);
              tabChild = true;
              this.events.trigger("trigger.dialog", [getDiv,target,contentEl,tabChild]);

            } else {
              this.events.trigger("trigger.dialog", [getDiv,'','',tabChild]);
            }
          }
          
          if(getDiv.className === "accordion-panel"){
            const parent = target.closest('.accordion-navigation');
            if(parent.childNodes.length >2){
              const panelId = target.getAttribute('href');
              const contentEl = parent.querySelector(`${panelId}`);
              tabChild = true;
              this.events.trigger("trigger.dialog", [getDiv,target,contentEl,tabChild]);

            } else {
              this.events.trigger("trigger.dialog", [getDiv,'','',tabChild]);
            }
          }
          this.popups.hide("tabs.deletion");
        },
      });
    }

    // Create deletion popup if it doesn't exist yet
    if (!editor.popups.get("tabs.deletion")) {
      editor.popups.create("tabs.deletion", {
        buttons:
          '<div class="fr-buttons"><button class="fr-command" data-cmd="removeDiv" title="Delete" type="button">&#xe020;</button></div>',
      });
    }
  }

  function deleteDiv (target, tabHeaderRef, tabContentRef, deletePara, editor){
    editor.undo.saveStep();
    if(deletePara.value){
      tabHeaderRef.value.remove();
      tabContentRef.value.remove();
    }else{
      target.value.remove();
    }
  }

  // Return public API
  return {
    _init: _init,
    deleteDiv: deleteDiv,
  };
};

export default divutils;
