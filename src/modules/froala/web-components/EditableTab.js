class EditableTab extends HTMLElement {
    constructor() {
        super();
        this.tabTitle = 'Title';
        this.tabContent = 'Tab Body';
        this.isEditingTitle = false;
        this.isEditingContent = false;
        this.initialized = false;
    }

    // This is called when the element is added to the DOM
    connectedCallback() {
        if (!this.initialized) {
            this.init();
            this.initialized = true;
        }
    }

    // This is called when the element is removed from the DOM
    disconnectedCallback() {
        // Clean up event listeners if needed
    }

    init() {
        // Add styles to document head if not already added
        if (!document.querySelector('#editable-tab-styles')) {
            const style = document.createElement('style');
            style.id = 'editable-tab-styles';
            style.textContent = `
        editable-tab {
          display: block;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
          position: relative;
          background: #f0f0f0;
          border-radius: 12px;
          padding: 8px;
          margin-bottom: 8px;
        }

        editable-tab .tab-container {
          position: relative;
          background: transparent;
          border-radius: 10px;
          width: 100%;
          overflow: visible;
          transition: all 0.2s ease;
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        editable-tab .tab-container:focus-within {
          box-shadow: 0 0 0 2px #ffd700;
        }

        /* Delete button */
        editable-tab .delete-btn {
          position: absolute;
          top: -6px;
          right: -6px;
          width: 24px;
          height: 24px;
          background: #ffd700;
          border: 2px solid white;
          border-radius: 50%;
          color: #666;
          font-size: 14px;
          font-weight: bold;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 10;
          transition: all 0.2s ease;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        editable-tab .delete-btn:hover {
          background: #ffed4e;
          transform: scale(1.1);
        }

        /* Tab title */
        editable-tab .tab-title {
          padding: 16px 20px;
          background: white;
          border: none;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.2s ease;
          position: relative;
        }

        editable-tab .tab-title:hover:not(.editing) {
          background: #fafafa;
        }

        editable-tab .tab-title.editing {
          cursor: text;
        }

        editable-tab .tab-title input {
          border: none;
          outline: none;
          font-size: 16px;
          font-weight: 500;
          font-family: inherit;
          background: transparent;
          width: 100%;
          color: #333;
          padding: 0;
        }

        editable-tab .tab-title span {
          font-size: 16px;
          font-weight: 500;
          color: #333;
          display: block;
          width: 100%;
        }

        /* Tab body */
        editable-tab .tab-body {
          padding: 20px;
          min-height: 120px;
          cursor: pointer;
          position: relative;
          background: white;
          border-radius: 8px;
          transition: all 0.2s ease;
          color: #666;
          font-size: 14px;
          line-height: 1.5;
        }

        editable-tab .tab-body:hover:not(.editing) {
          background: #fafafa;
        }

        editable-tab .tab-body.editing {
          cursor: text;
          background: white;
          outline: 2px solid #ffd700;
          outline-offset: -2px;
        }

        editable-tab .tab-body[contenteditable="true"] {
          cursor: text;
        }

        editable-tab .tab-body:focus {
          outline: none;
          outline-offset: unset;
        }

        /* Placeholder text for empty contenteditable */
        editable-tab .tab-body:empty:before {
          content: attr(data-placeholder);
          color: #999;
          pointer-events: none;
        }
      `;
            document.head.appendChild(style);
        }

        this.render();
        this.attachEventListeners();
    }

    render() {
        this.innerHTML = `
      <div class="tab-container">
        <!-- Delete button -->
        <button class="delete-btn" title="Delete tab">
          ×
        </button>

        <!-- Tab title -->
        <div class="fr-editable tab-title ${this.isEditingTitle ? 'editing' : ''}">
          ${
              this.isEditingTitle
                  ? `<input type="text" value="${this.tabTitle}" autofocus>`
                  : `<span>${this.tabTitle}</span>`
          }
        </div>

        <!-- Tab body -->
        <div class="fr-editable tab-body ${this.isEditingContent ? 'editing' : ''}" 
             ${this.isEditingContent ? 'contenteditable="true"' : ''}
             data-placeholder="Click to edit content...">
          ${this.tabContent}
        </div>
      </div>
    `;
    }

    attachEventListeners() {
        const deleteBtn = this.querySelector('.delete-btn');
        const tabTitle = this.querySelector('.tab-title');
        const tabBody = this.querySelector('.tab-body');

        deleteBtn.addEventListener('click', () => this._handleDelete());
        tabTitle.addEventListener('click', (e) => this._handleTitleClick(e));
        tabBody.addEventListener('click', (e) => this._handleContentClick(e));

        // Add event listeners for input elements if they exist
        const titleInput = this.querySelector('.tab-title input');

        if (titleInput) {
            titleInput.addEventListener('blur', () => this._handleTitleBlur());
            titleInput.addEventListener('keydown', (e) => this._handleTitleKeydown(e));
            titleInput.addEventListener('input', (e) => this._handleTitleInput(e));
        }

        // Content editable event listeners
        if (this.isEditingContent) {
            tabBody.addEventListener('blur', () => this._handleContentBlur());
            tabBody.addEventListener('keydown', (e) => this._handleContentKeydown(e));
            tabBody.addEventListener('input', () => this._handleContentInput());

            // Focus the contenteditable element
            setTimeout(() => {
                tabBody.focus();
                // Place cursor at the end
                const range = document.createRange();
                const sel = window.getSelection();
                range.selectNodeContents(tabBody);
                range.collapse(false);
                sel.removeAllRanges();
                sel.addRange(range);
            }, 0);
        }
    }

    _handleDelete() {
        this.dispatchEvent(
            new CustomEvent('tab-delete', {
                bubbles: true,
                composed: true,
                detail: { element: this },
            })
        );
        this.remove();
    }

    _handleTitleClick(e) {
        if (!this.isEditingTitle) {
            e.stopPropagation();
            this.isEditingTitle = true;
            this.render();
            this.attachEventListeners();
        }
    }

    _handleTitleBlur() {
        this.isEditingTitle = false;
        this.render();
        this.attachEventListeners();
    }

    _handleTitleKeydown(e) {
        if (e.key === 'Enter') {
            this.isEditingTitle = false;
            this.render();
            this.attachEventListeners();
        } else if (e.key === 'Escape') {
            this.isEditingTitle = false;
            this.tabTitle = 'Title'; // Reset to default
            this.render();
            this.attachEventListeners();
        }
    }

    _handleTitleInput(e) {
        this.tabTitle = e.target.value || 'Title';
    }

    _handleContentClick(e) {
        if (!this.isEditingContent) {
            e.stopPropagation();
            this.isEditingContent = true;
            this.render();
            this.attachEventListeners();
        }
    }

    _handleContentBlur() {
        this.isEditingContent = false;
        this.render();
        this.attachEventListeners();
    }

    _handleContentKeydown(e) {
        if (e.key === 'Escape') {
            this.isEditingContent = false;
            this.render();
            this.attachEventListeners();
        }
    }

    _handleContentInput() {
        const tabBody = this.querySelector('.tab-body');
        this.tabContent = tabBody.innerHTML || 'Tab Body';
    }
}

export default EditableTab;