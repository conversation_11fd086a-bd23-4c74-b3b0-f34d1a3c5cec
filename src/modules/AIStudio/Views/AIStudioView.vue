<script setup lang="ts">
import { onMounted, ref } from 'vue'
import BravoTextarea from '@services/ui-component-library/components/BravoTextarea.vue'
import BravoLabel from '@services/ui-component-library/components/BravoLabel.vue'

const brandVoice = ref('')

onMounted(() => {
  console.log('AI Studio view mounted')
})
</script>

<template>
  <div class="ai-studio-view">
    <div class="ai-studio-header">
      <h1>AI Studio</h1>
      <p>Welcome to AI Studio - Your creative AI workspace</p>
    </div>
    
    <div class="ai-studio-content">
      <div class="ai-studio-form">
        <div class="form-field">
          <BravoLabel text="Brand Voice" for-element="brand-voice" :is-required="true" />
          <BravoTextarea
            id="brand-voice"
            v-model="brandVoice"
            placeholder="Describe your brand's voice and tone..."
            :rows="4"
            class="w-full"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.ai-studio-view {
  padding: 2rem;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.ai-studio-header {
  margin-bottom: 2rem;
}

.ai-studio-header h1 {
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.ai-studio-header p {
  color: var(--text-color-secondary);
  font-size: 1.1rem;
}

.ai-studio-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.ai-studio-form {
  max-width: 600px;
  width: 100%;
}

.form-field {
  margin-bottom: 1.5rem;
}

.form-field :deep(.p-label) {
  margin-bottom: 0.5rem;
  display: block;
}
</style>
