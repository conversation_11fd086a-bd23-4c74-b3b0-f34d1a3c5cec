import { client, xml } from '@xmpp/client';
import type { AppConfig, Communication } from '../types';
import { useCommunicationsStore } from '../stores/communications';

class XMPPService {
  private client: any = null;
  private connected: boolean = false;
  private currentRoom: string | null = null;
  private messages: any[] = [];
  private static instance: XMPPService | null = null;
  private roomConnections: Map<string, boolean> = new Map();
  private initializationPromise: Promise<void> | null = null;
  private configuredResource: string | null = null;
  private joiningRooms: Map<string, Promise<void>> = new Map(); // Track ongoing join operations

  private constructor() {}

  static getInstance(): XMPPService {
    if (!XMPPService.instance) {
      XMPPService.instance = new XMPPService();
    }
    return XMPPService.instance;
  }

  private handleStanza(stanza: any) {
    console.log('Received stanza:', stanza.toString());
    // debugger

    if (stanza.is('message') && stanza.attrs.type === 'groupchat') {
      const body = stanza.getChildText('body');
      const delay = stanza.getChild('delay', 'urn:xmpp:delay');
      
      if (body) {
        const store = useCommunicationsStore();
        const fromJid = stanza.attrs.from.split('/');
        const roomJid = fromJid[0];
        const nickname = fromJid[1] || fromJid[0].split('@')[0];
        
        // Get current user prefix from XMPP config
        const currentUserPrefix = store.appConfig?.serviceConfig.resource.split(';')[0];
        const isCurrentUser = nickname.startsWith(currentUserPrefix || '');

        // Find the communication by room JID
        const comm = store.findCommByRoomJid(roomJid);
        if (!comm) {
          console.log('🚫 XMPP: No communication found for room JID:', roomJid);
          return;
        }

        // Extract the base ID (everything before the semicolon)
        const participantBaseId = nickname.split(';')[0];
        const participant = comm.participants.find(p => p.id.startsWith(participantBaseId));
        
        console.log('Participant matching:', {
          nickname,
          participantBaseId,
          foundParticipant: participant?.name,
          allParticipants: comm.participants
        });

        // Generate a more reliable and unique message ID
        const timestamp = delay ? new Date(delay.attrs.stamp) : new Date();
        
        // Create a deterministic message ID based on content, sender, timestamp, and room
        // This ensures the same message will always have the same ID
        // Use Unicode-safe base64 encoding to handle emojis and special characters
        const contentHash = btoa(encodeURIComponent(body).replace(/%[0-9A-F]{2}/g, (match) => String.fromCharCode(parseInt(match.slice(1), 16)))).slice(0, 8);
        const timestampMs = timestamp.getTime();
        const messageId = stanza.attrs.id || `${roomJid.split('@')[0]}-${nickname.split(';')[0]}-${timestampMs}-${contentHash}`;

        // Debug: Check if this message would be hidden by the EmailChannel filter
        let shouldHide = false;
        try {
          const parsed = JSON.parse(body);
          shouldHide = !!(parsed.hideMessage || parsed.secret === 'MessengerUserExit');
        } catch (e) {
          // Not JSON, won't be hidden
        }

        const messageData = {
          id: messageId,
          senderId: participant?.id || nickname,
          content: body,
          timestamp,
          type: 'text' as const,
          isMine: isCurrentUser
        };

        console.log('🔔 XMPP: Processing incoming message:', {
          commId: comm.id,
          messageId: messageData.id,
          isMine: messageData.isMine,
          isCurrentUser,
          nickname,
          currentUserPrefix,
          content: body.substring(0, 50) + (body.length > 50 ? '...' : ''),
          currentlyViewedCommId: store.currentlyViewedCommId,
          selectedCommId: store.selectedComm?.id,
          hasDelay: !!delay,
          timestamp: timestamp.toISOString(),
          isHistorical: !!delay,
          willBeHiddenByEmailChannel: shouldHide,
          totalMessagesInCommBefore: comm.messages.length
        });

        store.addMessage(comm.id, messageData);
        
        console.log('📊 XMPP: Message added, new total:', comm.messages.length);
      }
    } else if (stanza.is('presence')) {
      const from = stanza.attrs.from;
      const type = stanza.attrs.type;
      const [roomJid] = from.split('/');

      if (type === 'unavailable') {
        this.roomConnections.set(roomJid, false);
        console.log('🚪 XMPP: Left room:', roomJid);
      } else {
        const xMuc = stanza.getChild('x', 'http://jabber.org/protocol/muc#user');
        if (xMuc) {
          // Successfully joined the room
          this.roomConnections.set(roomJid, true);
          console.log('✅ XMPP: Successfully joined room:', roomJid);
        }
      }
    }
  }

  async initialize(config: AppConfig) {
    console.log('XMPP: Starting initialization...');
    if (this.initializationPromise) {
      console.log('XMPP: Initialization already in progress, returning existing promise');
      return this.initializationPromise;
    }

    this.initializationPromise = (async () => {
      // If we already have a connected client with the same resource, don't reinitialize
      if (this.client && this.connected && this.configuredResource === config.serviceConfig.resource) {
        console.log('XMPP: Already connected with same resource, skipping initialization');
        return;
      }

      // If we have an existing client but different resource or not connected, clean it up first
      if (this.client) {
        console.log('XMPP: Cleaning up existing client before reinitializing');
        try {
          // Remove all event listeners to prevent duplicate handlers
          this.client.removeAllListeners();
          await this.client.stop();
        } catch (error) {
          console.warn('XMPP: Error cleaning up existing client:', error);
        }
        this.client = null;
        this.connected = false;
        this.roomConnections.clear();
      }

      try {
        // Store the resource
        this.configuredResource = config.serviceConfig.resource;

        console.log('XMPP: Creating client with config:', {
          service: config.serviceConfig.websocket_host,
          domain: config.serviceConfig.websocket_host.split('://')[1].split(':')[0],
          username: config.serviceConfig.jid.split('@')[0],
          password: '***'
        });

        this.client = client({
          service: config.serviceConfig.websocket_host,
          domain: config.serviceConfig.websocket_host.split('://')[1].split(':')[0],
          username: config.serviceConfig.jid.split('@')[0],
          password: config.serviceConfig.password,
        });

        // Set the resource
        this.client.options.resource = this.configuredResource;

        console.log('XMPP: Client created with options:', {
          ...this.client.options,
          password: '***'
        });

        // Set up stanza handler - only once per client
        this.client.on('stanza', (stanza: any) => {
          this.handleStanza(stanza);
        });

        // Create a promise that resolves when we're connected
        const connectionPromise = new Promise<void>((resolve, reject) => {
          const timeout = setTimeout(() => {
            console.log('XMPP: Connection timeout after 20 seconds');
            reject(new Error('XMPP connection timeout'));
          }, 20000); // 20 second timeout

          this.client.on('online', async () => {
            console.log('XMPP: Connected to server and authenticated');
            this.connected = true;
            clearTimeout(timeout);

            // Send initial presence
            try {
              console.log('XMPP: Sending initial presence');
              await this.client.send(xml('presence'));
              console.log('XMPP: Initial presence sent');
            } catch (error) {
              console.error('XMPP: Error sending initial presence:', error);
            }

            resolve();
            
            // If we have a pending room to join, join it now
            if (this.currentRoom) {
              console.log('XMPP: Joining pending room:', this.currentRoom);
              await this.joinRoom({ roomJid: this.currentRoom } as Communication);
            }
          });

          this.client.on('error', (err: any) => {
            console.error('XMPP: Connection error:', err);
            this.connected = false;
            clearTimeout(timeout);
            reject(err);
          });

          this.client.on('offline', () => {
            console.log('XMPP: Connection offline');
            this.connected = false;
          });

          this.client.on('connect', () => {
            console.log('XMPP: Socket connected, waiting for authentication');
          });

          this.client.on('authenticate', () => {
            console.log('XMPP: Starting authentication');
          });
        });

        console.log('XMPP: Starting client connection');
        await this.client.start();
        console.log('XMPP: Waiting for connection and authentication');
        await connectionPromise;
        console.log('XMPP: Connection and authentication complete');

        const store = useCommunicationsStore();
        store.isXmppInitialized = true;
        console.log('XMPP: Store initialized flag set to true');
      } catch (error) {
        console.error('XMPP: Connection error:', error);
        this.connected = false;
        throw error;
      } finally {
        this.initializationPromise = null;
      }
    })();

    return this.initializationPromise;
  }

  isRoomConnected(roomJid: string): boolean {
    return this.roomConnections.get(roomJid) || false;
  }

  async joinRoom(communication: Communication) {
    if (!communication.roomJid) {
      console.log('XMPP: Cannot join room - no room JID provided');
      throw new Error('Room JID not provided');
    }

    // If we're already connected to this room, don't try to join again
    if (this.isRoomConnected(communication.roomJid)) {
      console.log('XMPP: Already connected to room:', communication.roomJid);
      return;
    }

    // If we're already in the process of joining this room, wait for that to complete
    const existingJoinPromise = this.joiningRooms.get(communication.roomJid);
    if (existingJoinPromise) {
      console.log('XMPP: Room join already in progress, waiting for completion:', communication.roomJid);
      return existingJoinPromise;
    }

    // Create a new join promise and track it
    const joinPromise = this._performRoomJoin(communication);
    this.joiningRooms.set(communication.roomJid, joinPromise);

    try {
      await joinPromise;
    } finally {
      // Clean up the tracking promise when done (success or failure)
      this.joiningRooms.delete(communication.roomJid);
    }
  }

  private async _performRoomJoin(communication: Communication) {
    try {
      // Set connecting state
      const store = useCommunicationsStore();
      store.setCommConnecting(communication.id, true);
      
      // Save the room JID in case we need to rejoin after reconnection
      this.currentRoom = communication.roomJid!;
      console.log('XMPP: Setting current room to:', this.currentRoom);

      if (!this.connected || !this.client) {
        console.log('XMPP: Not connected yet, room will be joined after connection');
        store.setCommConnecting(communication.id, false);
        return;
      }

      console.log('XMPP: Attempting to join room:', communication.roomJid);
      
      // Use the stored resource
      const resource = this.client.options.resource || this.configuredResource;
      if (!resource) {
        console.error('XMPP: No resource available');
        throw new Error('No resource available for XMPP client');
      }
      
      // Mark room as connecting to prevent duplicate join attempts
      this.roomConnections.set(communication.roomJid!, false);
      
      // Join room with limited history to prevent duplicates
      // Request more messages but from a reasonable timeframe
      const presenceWithHistory = xml(
        'presence',
        { 
          to: `${communication.roomJid}/${resource}`,
          from: this.client.jid
        },
        xml('x', { xmlns: 'http://jabber.org/protocol/muc' },
          xml('history', {
            maxstanzas: '500',  // Increased to get more history
            seconds: '2592000'  // Last 30 days
          })
        )
      );

      console.log('XMPP: Sending MUC join with limited history:', presenceWithHistory.toString());
      await this.client.send(presenceWithHistory);

      // Wait for room join confirmation with better timeout handling
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          console.log('XMPP: Room join timeout after 10 seconds');
          // Clean up connection state on timeout
          this.roomConnections.delete(communication.roomJid!);
          
          // Set error state and clear connecting state in the communications store
          const store = useCommunicationsStore();
          store.setCommConnecting(communication.id, false);
          store.setCommError(communication.id, {
            type: 'connection_timeout',
            message: 'Connection timed out while trying to join the room. Please try refreshing the page or contact support if the issue persists.'
          });
          
          reject(new Error('Room join timeout'));
        }, 10000); // Increased timeout to 10 seconds

        const checkInterval = setInterval(async () => {
          const isConnected = this.isRoomConnected(communication.roomJid!);
          console.log('XMPP: Checking room connection status:', isConnected);
          if (isConnected) {
            console.log('XMPP: Successfully connected to room');
            clearTimeout(timeout);
            clearInterval(checkInterval);

            // Clear connecting state
            const store = useCommunicationsStore();
            store.setCommConnecting(communication.id, false);

            // Now that we're in the room, try to configure it
            try {
              const configIQ = xml(
                'iq',
                { 
                  to: communication.roomJid,
                  type: 'set'
                },
                xml('query', { xmlns: 'http://jabber.org/protocol/muc#owner' },
                  xml('x', { xmlns: 'jabber:x:data', type: 'submit' },
                    xml('field', { var: 'FORM_TYPE' },
                      xml('value', {}, 'http://jabber.org/protocol/muc#roomconfig')
                    ),
                    xml('field', { var: 'muc#roomconfig_publicroom' },
                      xml('value', {}, '1')
                    ),
                    xml('field', { var: 'muc#roomconfig_persistentroom' },
                      xml('value', {}, '1')
                    )
                  )
                )
              );

              await this.client.send(configIQ);
              console.log('XMPP: Room configuration sent');
            } catch (error) {
              // Ignore configuration errors - room might already be configured
              console.log('XMPP: Room configuration error (expected):', error);
            }

            resolve();
          }
        }, 200); // Check every 200ms instead of 100ms
      });

      console.log('XMPP: Room join process completed for:', communication.roomJid);
    } catch (error) {
      console.error('XMPP: Error joining room:', error);
      // Clear the room connection status and connecting state on error
      this.roomConnections.delete(communication.roomJid!);
      const store = useCommunicationsStore();
      store.setCommConnecting(communication.id, false);
      throw error;
    }
  }

  async sendMessage(communication: Communication, content: string) {
    if (!this.connected || !communication.roomJid) {
      throw new Error('Not connected to room or room JID not available');
    }

    try {
      const resource = this.client.options.resource || this.configuredResource;
      if (!resource) {
        throw new Error('No resource available for XMPP client');
      }

      const messageStanza = xml(
        'message', 
        { 
          to: communication.roomJid, 
          type: 'groupchat',
          from: `${communication.roomJid}/${resource}`
        },
        xml('body', {}, content)
      );
      console.log('Sending message:', messageStanza.toString());
      await this.client.send(messageStanza);
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  disconnect() {
    console.log('XMPP: Disconnecting and cleaning up resources');
    
    if (this.client) {
      try {
        // Remove all event listeners to prevent memory leaks
        this.client.removeAllListeners();
        // Stop the client
        this.client.stop();
      } catch (error) {
        console.warn('XMPP: Error during disconnect:', error);
      }
      
      this.client = null;
    }
    
    // Reset all state
    this.connected = false;
    this.messages = [];
    this.currentRoom = null;
    this.roomConnections.clear();
    this.joiningRooms.clear(); // Clear ongoing join operations
    this.configuredResource = null;
    this.initializationPromise = null;
    
    console.log('XMPP: Disconnect and cleanup complete');
  }

  async exitRoom(roomJid: string) {
    if (!this.client || !this.connected) return;
    
    if (!this.isRoomConnected(roomJid)) {
      console.log('XMPP: Not connected to room:', roomJid);
      return;
    }

    try {
      const resource = this.client.options.resource || this.configuredResource;
      if (!resource) {
        throw new Error('No resource available for XMPP client');
      }

      console.log('XMPP: Exiting room:', roomJid);
      const presence = xml(
        'presence',
        { 
          to: `${roomJid}/${resource}`,
          type: 'unavailable'
        }
      );
      await this.client.send(presence);
      this.roomConnections.delete(roomJid);
      
      if (this.currentRoom === roomJid) {
        this.currentRoom = null;
      }
    } catch (error) {
      console.error('XMPP: Error exiting room:', error);
    }
  }
}

export const xmppService = XMPPService.getInstance();