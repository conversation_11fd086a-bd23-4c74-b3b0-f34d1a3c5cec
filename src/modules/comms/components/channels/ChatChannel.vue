<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue';
import type { Communication, Message, XMPPNotificationObject, MessageAction } from '../../types';
import { tryParseMessageContent, shouldHideMessage, formatFileSize } from '../../types';
import { formatDistanceToNow } from 'date-fns';
import { useCommunicationsStore } from '../../stores/communications';
import { useCommsAPI } from '@/composables/services/useCommsAPI';
import { useUserStore } from '@/stores/user';
import { useMetaStore } from '@/stores/meta';
import { useLinkPreview, type LinkPreview } from '@/composables/useLinkPreview';
import { getAvatarColor } from '../../utils/commHelpers';
import { xmppService } from '../../services/xmpp';
import { useAIAssistant } from '@/composables/useAIAssistant';
import { useCannedResponseTokens } from '@/composables/useCannedResponseTokens';
import type { Issue } from '@/composables/services/useIssuesAPI';
import FileUploadProgress from '../FileUploadProgress.vue';
import Dropdown from 'primevue/dropdown';
import Tooltip from 'primevue/tooltip';
import BravoAvatar from '@services/ui-component-library/components/BravoAvatar.vue';
import CryptoJS from 'crypto-js';
import ProgressSpinner from 'primevue/progressspinner';
import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue';
import linkifyHtml from 'linkify-html';
import BravoZeroStateScreen from '@services/ui-component-library/components/BravoZeroStateScreen.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';

import BravoSkeleton from '@services/ui-component-library/components/BravoSkeleton.vue';
import EmailZeroStateSvg from '@/assets/email-zero-state.svg';
import ChatMessage from './ChatMessage.vue';

// Register the tooltip directive
const vTooltip = Tooltip;

const props = defineProps<{
  communication: Communication;
  issue?: Issue | null;
}>();

const fileInput = ref<HTMLInputElement | null>(null);
const uploadingFiles = ref<Array<{ id: string; name: string; size: number; progress: number }>>([]);
const stagedFiles = ref<Array<{ 
  id: string; 
  name: string; 
  size: number; 
  type: string; 
  preview?: string; 
  payloadData: any; // Store the API response payload
}>>([]);
const selectedTemplate = ref(null);
const selectedAiAction = ref(null);
const showSummaryDialog = ref(false);
const summaryContent = ref('');
const isSummarizing = ref(false);
const isGeneratingReply = ref(false);
const isReformatting = ref(false);
const mentionDropdown = ref<{
  show: boolean;
  top: number;
  left: number;
}>({
  show: false,
  top: 0,
  left: 0
});
const filteredUsers = ref<Array<{ id: string; name: string }>>([]);
const mentionQuery = ref('');
const mentionStartIndex = ref<number | null>(null);
const selectedMentionIndex = ref(0);

// Add loading state
const isLoading = ref(true);

// ResizeObserver to handle dynamic content changes (like image loading)
const resizeObserver = ref<ResizeObserver | null>(null);

// Debounced scroll function for image loads
let imageLoadScrollTimeout: NodeJS.Timeout | null = null;
const debouncedScrollOnImageLoad = () => {
  if (imageLoadScrollTimeout) {
    clearTimeout(imageLoadScrollTimeout);
  }
  imageLoadScrollTimeout = setTimeout(() => {
    scrollToBottom(true);
  }, 100);
};

// Add stores
const store = useCommunicationsStore();
const commsAPI = useCommsAPI();
const userStore = useUserStore();
const metaStore = useMetaStore();
const aiAssistant = useAIAssistant();
const { processCannedResponse } = useCannedResponseTokens(computed(() => props.issue || null));

// Link preview functionality
const { extractUrls, fetchPreviewsForMessage, getPreview } = useLinkPreview();
const messagePreviews = ref<Map<string, LinkPreview[]>>(new Map());
const fetchingPreviews = ref<Set<string>>(new Set());
const previewTimeouts = ref<Map<string, NodeJS.Timeout>>(new Map());

// Constants for message parsing
const endChatMessageSecret = CryptoJS.MD5('MessengerUserExit').toString();
const emojiHeight = 20;
const emojiWidth = 20;

// Add modal states
const showImageModal = ref(false);
const modalImageUrl = ref('');
const modalImageAlt = ref('');

// Function to format message content with highlighted mentions and enhanced parsing
const formatMessageContent = (content: string, isFromBot = false) => {
  let formattedContent = content;

  // Replace @mentions with styled spans
  formattedContent = formattedContent.replace(/@(\w+)/g, '<span class="mention">@$1</span>');

  // Replace newline characters with br tags (both actual and escaped)
  formattedContent = formattedContent.replace(/(?:\\r\\n|\\r|\\n|\r\n|\r|\n)/g, '<br>');

  // Parse links in markdown format [text](url)
  const markdownLinkRegex = /\[([^\]]+)\]\(([^)]+)\)/g;
  formattedContent = formattedContent.replace(markdownLinkRegex, '<a href="$2" target="_blank">$1</a>');

  // Handle all other links using linkify
  formattedContent = linkifyHtml(formattedContent, { target: '_blank' });

  return formattedContent;
};

// Add modal functions
const openImageModal = (url: string, alt: string = 'Image') => {
  modalImageUrl.value = url;
  modalImageAlt.value = alt;
  showImageModal.value = true;
};

// Enhanced message parsing function
const parseXMPPMessage = (message: Message): {
  parsedContent: XMPPNotificationObject | null;
  displayContent: string;
  shouldHide: boolean;
} => {
  const parseResult = tryParseMessageContent(message.content);
  
  if (!parseResult.isParsed) {
    // Plain text message
    return {
      parsedContent: null,
      displayContent: formatMessageContent(message.content),
      shouldHide: false
    };
  }

  const notificationObj = parseResult.data!;
  const shouldHide = shouldHideMessage(notificationObj);

  // Handle different message types
  let displayContent = '';
  
  if (notificationObj.message) {
    displayContent = formatMessageContent(
      decodeURIComponent(String(notificationObj.message).replace(/\+/g, ' '))
    );
  }

  return {
    parsedContent: notificationObj,
    displayContent,
    shouldHide
  };
};



// Handle action clicks (for bot interactions)
const handleActionClick = async (action: MessageAction) => {
  try {
    // Mark action as selected
    action.selected = true;
    
    // Send action response
    if (action.uri.includes('bt-bot:')) {
      await store.sendMessage(props.communication.id, action.uri);
    }
  } catch (error) {
    console.error('Failed to handle action click:', error);
  }
};



const templates = [
  { 
    label: 'Account Setup Instructions',
    value: 'setup',
    content: 'Here are the steps to set up your account:\n\n1. Log in to your dashboard\n2. Complete your profile\n3. Set up two-factor authentication'
  },
  { 
    label: 'Payment Confirmation',
    value: 'payment',
    content: 'Thank you for your payment. Your transaction has been processed successfully.'
  },
  { 
    label: 'Technical Support Response',
    value: 'support',
    content: 'I understand you\'re experiencing technical difficulties. Let me help you troubleshoot the issue.'
  }
];

// Fetch canned responses from API
const cannedResponses = ref<any[]>([]);
const loadingCannedResponses = ref(false);

// Get filtered canned responses based on partner team
const filteredCannedResponses = computed(() => {
  if (!cannedResponses.value.length) return [];
  
  // Get the issue's owner partner team ID
  const issueTeamId = props.issue?.owner_partners_teams_id;
  const issuePartnerId = props.issue?.owner_partners_id;
  
  if (!issueTeamId && !issuePartnerId) {
    // If no team or partner ID, return all responses
    return cannedResponses.value;
  }
  
  // First try to filter by exact team match
  let filtered = [];
  if (issueTeamId) {
    filtered = cannedResponses.value.filter(response => 
      response.partners_teams_ids && 
      response.partners_teams_ids.includes(issueTeamId)
    );
  }
  
  // If no exact matches, try filtering by partner ID (less restrictive)
  if (filtered.length === 0 && issuePartnerId) {
    filtered = cannedResponses.value.filter(response => 
      response.partners_id === issuePartnerId
    );
  }
  
  // If still no matches, return all responses (fallback)
  if (filtered.length === 0) {
    return cannedResponses.value;
  }
  
  return filtered;
});

// Convert filtered canned responses to dropdown options
const templateOptions = computed(() => 
  filteredCannedResponses.value.map(response => ({
    label: response.name || response.lbl || response._title,
    value: response.id,
    content: response.content
  }))
);

const loadCannedResponses = async () => {
  try {
    loadingCannedResponses.value = true;
    const response = await metaStore.fetchCannedResponses({
      object: 'issues', // These params are no longer used but kept for interface compatibility
      object_id: props.communication.availableComm?.id || ''
    });
    
    cannedResponses.value = response.canned_responses?.results || response.pl__canned_responses || [];
    console.log('📋 Loaded canned responses for chat:', cannedResponses.value.length);
  } catch (error) {
    console.error('❌ Failed to load canned responses for chat:', error);
    cannedResponses.value = [];
  } finally {
    loadingCannedResponses.value = false;
  }
};

const applyTemplate = () => {
  try {
    if (selectedTemplate.value) {
      const template = templateOptions.value.find(t => t.value === selectedTemplate.value);
      if (template && template.content) {
        // Process the template content to replace tokens with actual case data
        const processedContent = processCannedResponse(template.content);
        
        // For chat, we want to use the text content, not HTML
        const textContent = processedContent.replace(/<[^>]*>/g, ''); // Strip HTML tags
        // Append to existing content instead of replacing
        const existingContent = newMessage.value || '';
        const separator = existingContent.trim() ? '\n\n' : '';
        newMessage.value = existingContent + separator + textContent;
      }
      selectedTemplate.value = null;
    }
  } catch (error) {
    console.error('Error applying template:', error);
    selectedTemplate.value = null; // Reset selection even on error
  }
};

const aiActions = [
  {
    label: 'Summarize Conversation',
    value: 'summarize',
    icon: 'pi pi-list'
  },
  {
    label: 'Generate Next Reply',
    value: 'generate',
    icon: 'pi pi-reply'
  },
  {
    label: 'Reformat Current Response',
    value: 'reformat',
    icon: 'pi pi-pencil'
  }
];

const handleAiAction = async () => {
  if (!selectedAiAction.value) return;

  try {
    switch (selectedAiAction.value) {
      case 'summarize':
        showSummaryDialog.value = true;
        isSummarizing.value = true;
        // Use the AI assistant composable
        summaryContent.value = await aiAssistant.summarizeConversation(filteredMessages.value);
        break;
      case 'generate':
        isGeneratingReply.value = true;
        // Use the AI assistant composable
        const generatedReply = await aiAssistant.generateReply(filteredMessages.value);
        // Append to existing content instead of replacing
        const existingContent = newMessage.value || '';
        const separator = existingContent.trim() ? '\n\n' : '';
        newMessage.value = existingContent + separator + generatedReply;
        break;
      case 'reformat':
        if (!newMessage.value.trim()) {
          return;
        }
        isReformatting.value = true;
        const reformattedContent = await aiAssistant.reformatChat(newMessage.value);
        newMessage.value = reformattedContent;
        break;
    }
  } catch (error) {
    console.error('AI action failed:', error);
    if (selectedAiAction.value === 'summarize') {
      summaryContent.value = 'Failed to process request. Please try again.';
    }
  } finally {
    isSummarizing.value = false;
    isGeneratingReply.value = false;
    isReformatting.value = false;
    selectedAiAction.value = null; // Reset selection
  }
};

const textareaRef = ref<HTMLTextAreaElement | null>(null);
const newMessage = ref('');
const messagesContainer = ref<HTMLElement | null>(null);

const adjustTextareaHeight = async () => {
  if (!textareaRef.value) return;
  
  // Reset height to auto to get the correct scrollHeight
  textareaRef.value.style.height = 'auto';
  
  // Calculate new height (min 80px, max 300px)
  const newHeight = Math.min(Math.max(textareaRef.value.scrollHeight, 80), 300);
  textareaRef.value.style.height = `${newHeight}px`;
  
  await nextTick();
  await scrollToBottom(true);
};

const handleInput = (e: Event) => {
  const textarea = e.target as HTMLTextAreaElement;
  adjustTextareaHeight();

  const text = textarea.value;
  if (!text) return;

  const caretPos = textarea.selectionStart;
  
  // Find the last @ before the caret
  const lastAtPos = text.lastIndexOf('@', caretPos - 1);
  
  if (lastAtPos !== -1 && (lastAtPos === 0 || text[lastAtPos - 1] === ' ')) {
    const query = text.substring(lastAtPos + 1, caretPos).toLowerCase();
    mentionQuery.value = query;
    mentionStartIndex.value = lastAtPos;
    
    // Filter users based on query
    filteredUsers.value = props.communication.participants
      .filter(p => p.name.toLowerCase().includes(query))
      .map(p => ({ id: p.id, name: p.name }));

    // Reset selected index when filtering
    selectedMentionIndex.value = 0;
    
    if (filteredUsers.value.length > 0) {
      // Position the dropdown below the @ symbol
      const coords = getCaretCoordinates(textarea, lastAtPos);
      mentionDropdown.value = {
        show: true,
        top: coords.top - 200, // Position above with space for the dropdown
        left: coords.left
      };
    } else {
      mentionDropdown.value.show = false;
    }
  } else {
    mentionDropdown.value.show = false;
    mentionStartIndex.value = null;
  }
};

const handleEnterKey = () => {
  if (mentionDropdown.value.show) {
    insertMention(filteredUsers.value[selectedMentionIndex.value]);
  } else {
    sendMessage();
  }
};

const handleShiftEnter = (e: KeyboardEvent) => {
  e.preventDefault();
  const target = e.target as HTMLTextAreaElement;
  if (!target) return;
  const start = target.selectionStart;
  const end = target.selectionEnd;
  newMessage.value = newMessage.value.substring(0, start) + '\n' + newMessage.value.substring(end);
  nextTick(() => {
    target.selectionStart = target.selectionEnd = start + 1;
    adjustTextareaHeight();
  });
};

const handleKeyDown = (e: KeyboardEvent) => {
  if (!mentionDropdown.value.show) return;

  switch (e.key) {
    case 'Tab':
      e.preventDefault();
      insertMention(filteredUsers.value[selectedMentionIndex.value]);
      break;
    case 'ArrowDown':
      e.preventDefault();
      selectedMentionIndex.value = (selectedMentionIndex.value + 1) % filteredUsers.value.length;
      break;
    case 'ArrowUp':
      e.preventDefault();
      selectedMentionIndex.value = selectedMentionIndex.value - 1;
      if (selectedMentionIndex.value < 0) selectedMentionIndex.value = filteredUsers.value.length - 1;
      break;
    case 'Enter':
      e.preventDefault();
      break;
    case 'Escape':
      mentionDropdown.value.show = false;
      break;
  }
};

const getCaretCoordinates = (element: HTMLTextAreaElement, position: number) => {
  const { offsetLeft: inputX, offsetTop: inputY } = element;
  
  // Create a temporary div to measure text
  const div = document.createElement('div');
  const styles = window.getComputedStyle(element);
  
  // Copy styles to ensure accurate measurement
  const styleProperties = [
    'fontFamily', 'fontSize', 'fontWeight', 'letterSpacing',
    'padding', 'border', 'wordSpacing', 'lineHeight'
  ] as const;
  
  styleProperties.forEach(prop => {
    (div.style as any)[prop] = styles[prop];
  });
  
  // Set content and styles
  div.style.position = 'absolute';
  div.style.visibility = 'hidden';
  div.style.whiteSpace = 'pre-wrap';
  div.style.width = `${element.offsetWidth}px`;
  
  // Add text up to the caret position
  div.textContent = element.value.substring(0, position);
  
  // Add a span at caret position to measure its position
  const span = document.createElement('span');
  span.textContent = element.value.charAt(position) || '.';
  div.appendChild(span);
  
  document.body.appendChild(div);
  const { offsetLeft: spanX, offsetTop: spanY } = span;
  document.body.removeChild(div);
  
  return {
    left: inputX + spanX,
    top: inputY + spanY,
    height: parseInt(styles.lineHeight)
  };
};

const insertMention = (user: { id: string; name: string }) => {
  if (mentionStartIndex.value === null || !textareaRef.value) return;
  
  if (!newMessage.value) {
    newMessage.value = '';
  }

  const start = mentionStartIndex.value;
  const end = textareaRef.value.selectionStart;
  const beforeMention = newMessage.value.slice(0, start);
  const afterMention = newMessage.value.slice(end);
  
  newMessage.value = `${beforeMention}@${user.name} ${afterMention}`;
  mentionDropdown.value.show = false;
  mentionStartIndex.value = null;
  
  // Set focus back to textarea
  nextTick(() => {
    if (textareaRef.value) {
      const newCursorPos = start + user.name.length + 2; // +2 for @ and space
      textareaRef.value.focus();
      textareaRef.value.setSelectionRange(newCursorPos, newCursorPos);
    }
  });
};

const getFileIcon = (type: string) => {
  if (type.startsWith('image/')) return 'pi pi-image';
  if (type.includes('pdf')) return 'pi pi-file-pdf';
  if (type.includes('word') || type.includes('doc')) return 'pi pi-file-word';
  if (type.includes('excel') || type.includes('sheet')) return 'pi pi-file-excel';
  if (type.includes('zip') || type.includes('rar')) return 'pi pi-file-archive';
  return 'pi pi-file';
};

const getFilePreviewUrl = (file: File): Promise<string | undefined> => {
  return new Promise((resolve) => {
    if (file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target?.result as string);
      reader.onerror = () => resolve(undefined);
      reader.readAsDataURL(file);
    } else {
      resolve(undefined);
    }
  });
};

const handleFileUpload = async (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (!input.files || input.files.length === 0) return;

  const files = Array.from(input.files);
  console.log('📎 ChatChannel: Starting file upload for', files.length, 'files');

  // Add files to uploading state
  const uploadingFileIds: string[] = [];
  for (const file of files) {
    const fileId = crypto.randomUUID();
    uploadingFileIds.push(fileId);
    
    uploadingFiles.value.push({
      id: fileId,
      name: file.name,
      size: file.size,
      progress: 0
    });
  }

  try {
    // Call the putFile API to upload files
    const response = await commsAPI.putFile({
      id: props.communication.id,
      file_tag: 'attachment',
      message: newMessage.value.trim() || '', // Use the user's message or empty string
      files: files
    });

    console.log('✅ ChatChannel: Files uploaded successfully:', response);

    // Update progress to 100% for all files and show briefly
    uploadingFileIds.forEach(fileId => {
      const fileIndex = uploadingFiles.value.findIndex(f => f.id === fileId);
      if (fileIndex !== -1) {
        uploadingFiles.value[fileIndex].progress = 100;
      }
    });

    // Wait a moment to show 100% completion, then transition to staged files
    setTimeout(async () => {
      // Stage the files with their payload data for later sending
      if (response.payloads && response.payloads.length > 0) {
        for (let i = 0; i < response.payloads.length; i++) {
          const payload = response.payloads[i];
          const originalFile = files[i];
          
          // Create preview for images
          let preview: string | undefined;
          if (originalFile && originalFile.type.startsWith('image/')) {
            try {
              preview = await getFilePreviewUrl(originalFile);
            } catch (error) {
              console.warn('Failed to create preview for', originalFile.name, error);
            }
          }
          
          // Add to staged files
          stagedFiles.value.push({
            id: crypto.randomUUID(),
            name: payload.attachment.name,
            size: payload.attachment.size,
            type: payload.attachment.type,
            preview: preview || undefined,
            payloadData: payload
          });
        }
      }

      // Clear uploading files after staging is complete
      uploadingFiles.value = uploadingFiles.value.filter(f => !uploadingFileIds.includes(f.id));
    }, 800);

    console.log('✅ ChatChannel: Files staged for sending:', stagedFiles.value.length);

  } catch (error) {
    console.error('❌ ChatChannel: Failed to upload files:', error);
    
    // Remove failed uploads from uploading state
    uploadingFiles.value = uploadingFiles.value.filter(f => !uploadingFileIds.includes(f.id));
    
    // TODO: Show error toast to user
  }

  input.value = ''; // Reset the input
};

const scrollToBottom = async (smooth = false, waitForImages = false) => {
  await nextTick();
  
  if (waitForImages && messagesContainer.value) {
    // Wait for all images in the messages container to load
    const images = messagesContainer.value.querySelectorAll('img');
    const imagePromises = Array.from(images).map(img => {
      if (img.complete) {
        return Promise.resolve();
      }
      return new Promise((resolve) => {
        const onLoad = () => {
          img.removeEventListener('load', onLoad);
          img.removeEventListener('error', onLoad);
          resolve(void 0);
        };
        img.addEventListener('load', onLoad);
        img.addEventListener('error', onLoad);
        
        // Fallback timeout in case image never loads
        setTimeout(onLoad, 2000);
      });
    });
    
    await Promise.all(imagePromises);
    await nextTick();
  }
  
  if (messagesContainer.value) {
    if (smooth) {
      messagesContainer.value.scrollTo({
        top: messagesContainer.value.scrollHeight,
        behavior: 'smooth'
      });
    } else {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
    }
  }
};

// Watch for new messages and scroll to bottom
watch(() => props.communication.messages.length, () => {
  scrollToBottom(true, true);
});

// Initial scroll on mount
onMounted(async () => {
  isLoading.value = true;
  try {
    // Wait for DOM to be ready but don't scroll during loading
    await nextTick();
    
    // Load canned responses
    loadCannedResponses();
  } finally {
    // Short delay to prevent flash, then set loading to false and scroll
    setTimeout(async() => {
      isLoading.value = false;
      // Wait for DOM to update after loading state changes
      await nextTick();
      // Use requestAnimationFrame to ensure DOM is fully painted
      requestAnimationFrame(() => {
        requestAnimationFrame(async () => {
          // Add smooth scrolling for initial load and wait for images
          await scrollToBottom(true, true);
          
          // Set up ResizeObserver after initial scroll
          if (messagesContainer.value && !resizeObserver.value) {
            resizeObserver.value = new ResizeObserver((entries) => {
              // Only auto-scroll if we're already near the bottom (within 100px)
              const container = messagesContainer.value;
              if (container) {
                const isNearBottom = container.scrollTop + container.clientHeight >= container.scrollHeight - 100;
                if (isNearBottom) {
                  scrollToBottom(true);
                }
              }
            });
            resizeObserver.value.observe(messagesContainer.value);
          }
        });
      });
    }, 250);
  }
});

const sendMessage = async () => {
  if (!newMessage.value.trim() && stagedFiles.value.length === 0) return;

  try {
    // If we have both text and files, send text first as a separate message
    if (newMessage.value.trim() && stagedFiles.value.length > 0) {
      console.log('📤 ChatChannel: Sending text message first, then attachments separately');
      
      if (props.communication.roomJid) {
        await xmppService.sendMessage(props.communication, newMessage.value.trim());
      }
    }

    // If we have staged files, send each as a separate XMPP message
    if (stagedFiles.value.length > 0) {
      console.log('📎 ChatChannel: Sending', stagedFiles.value.length, 'attachments as separate messages');
      
      for (const stagedFile of stagedFiles.value) {
        // Each attachment gets its own message with empty text
        const attachmentPayload = {
          ...stagedFile.payloadData,
          message: '' // Always empty for attachment-only messages
        };
        
        console.log('📤 ChatChannel: Sending individual attachment:', attachmentPayload);
        
        if (props.communication.roomJid) {
          await xmppService.sendMessage(
            props.communication,
            JSON.stringify(attachmentPayload)
          );
        }
        
        // Small delay between messages to ensure proper ordering
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Clear staged files after sending
      stagedFiles.value = [];
    } else if (newMessage.value.trim()) {
      // Send regular text message (only if no files were sent above)
      if (props.communication.roomJid) {
        await xmppService.sendMessage(props.communication, newMessage.value.trim());
      }
    }

    // Clear text message after sending
    newMessage.value = '';
    
  } catch (error) {
    console.error('❌ ChatChannel: Failed to send message:', error);
  }
};



const isConnected = computed(() => {
  return props.communication.roomJid ? xmppService.isRoomConnected(props.communication.roomJid) : false;
});

// Function to fetch link previews for a message
const fetchMessagePreviews = async (message: Message) => {
  // Prevent multiple simultaneous fetches for the same message
  if (fetchingPreviews.value.has(message.id) || messagePreviews.value.has(message.id)) {
    return;
  }
  
  const parseResult = parseXMPPMessage(message);
  
  // For messages with parsed content, only extract URLs from the actual message text
  // Skip attachment URLs and user avatar URLs
  let contentForLinkExtraction = '';
  
  if (parseResult.parsedContent) {
    // Only use the message text content, not the full JSON
    const messageText = parseResult.parsedContent.message || '';
    contentForLinkExtraction = formatMessageContent(decodeURIComponent(String(messageText).replace(/\+/g, ' ')));
  } else {
    // For plain text messages, use the full content
    contentForLinkExtraction = parseResult.displayContent || message.content;
  }
  
  // Extract URLs from the filtered content (excluding attachment and avatar URLs)
  const urls = extractUrls(contentForLinkExtraction);
  
  if (urls.length > 0) {
    fetchingPreviews.value.add(message.id);
    
    // Set a 5-second timeout to remove stuck loading previews
    const timeoutId = setTimeout(() => {
      console.warn('Link preview timeout for message:', message.id, 'removing stuck loading state');
      
      // Remove from fetching state
      fetchingPreviews.value.delete(message.id);
      
      // Remove from previews (this will hide the loading state)
      const newMap = new Map(messagePreviews.value);
      newMap.delete(message.id);
      messagePreviews.value = newMap;
      
      // Clean up timeout tracking
      previewTimeouts.value.delete(message.id);
    }, 5000);
    
    previewTimeouts.value.set(message.id, timeoutId);
    
    try {
      const previews = await fetchPreviewsForMessage(contentForLinkExtraction);
      console.log('Setting previews for message:', message.id, previews);
      
      // Clear the timeout since we completed successfully
      const timeout = previewTimeouts.value.get(message.id);
      if (timeout) {
        clearTimeout(timeout);
        previewTimeouts.value.delete(message.id);
      }
      
      // Force reactivity by creating a new Map
      const newMap = new Map(messagePreviews.value);
      newMap.set(message.id, previews);
      messagePreviews.value = newMap;
      
    } catch (error) {
      console.warn('Failed to fetch link previews for message:', message.id, error);
      
      // Clear the timeout since we completed (with error)
      const timeout = previewTimeouts.value.get(message.id);
      if (timeout) {
        clearTimeout(timeout);
        previewTimeouts.value.delete(message.id);
      }
      
      // Set empty array to prevent retrying
      const newMap = new Map(messagePreviews.value);
      newMap.set(message.id, []);
      messagePreviews.value = newMap;
    } finally {
      fetchingPreviews.value.delete(message.id);
    }
  }
};

// Filtered messages for display
const filteredMessages = computed(() => {
  const messages = props.communication.messages.filter(message => {
    const parseResult = parseXMPPMessage(message);
    return !parseResult.shouldHide && message.type !== 'system';
  });
  
  return messages;
});

// Watch for new messages and fetch link previews
watch(filteredMessages, (newMessages) => {
  newMessages.forEach(message => {
    fetchMessagePreviews(message);
  });
}, { immediate: true });

// Clean up previews when communication changes
watch(() => props.communication.id, () => {
  // Clear all timeouts
  previewTimeouts.value.forEach(timeout => clearTimeout(timeout));
  previewTimeouts.value.clear();
  
  messagePreviews.value.clear();
  fetchingPreviews.value.clear();
});

// Cleanup on unmount
onUnmounted(() => {
  if (resizeObserver.value) {
    resizeObserver.value.disconnect();
    resizeObserver.value = null;
  }
  if (imageLoadScrollTimeout) {
    clearTimeout(imageLoadScrollTimeout);
    imageLoadScrollTimeout = null;
  }
});

const openImageInNewTab = (url: string) => {
  window.open(url, '_blank', 'noopener,noreferrer');
};

const getFileTypeLabel = (type: string): string => {
  if (type.startsWith('image/')) return 'Image';
  if (type.includes('pdf')) return 'PDF';
  if (type.includes('word') || type.includes('document')) return 'Document';
  if (type.includes('sheet') || type.includes('excel')) return 'Spreadsheet';
  if (type.includes('zip') || type.includes('rar')) return 'Archive';
  if (type.startsWith('video/')) return 'Video';
  if (type.startsWith('audio/')) return 'Audio';
  return 'File';
};

// Helper function to safely extract hostname from URL
const getHostnameFromUrl = (url: string): string => {
  try {
    return new URL(url).hostname;
  } catch {
    return url;
  }
};
</script>

<template>
  <div class="flex flex-col h-full">
    <!-- Loading state -->
    <div 
      v-if="isLoading"
      class="flex-1 flex items-center justify-center bg-white"
    >
      <div class="text-center">
        <ProgressSpinner 
          style="width: 50px; height: 50px;" 
          strokeWidth="4" 
          animationDuration="1.5s"
        />
        <div class="mt-4 text-gray-600">
          Loading messages...
        </div>
      </div>
    </div>

    <!-- Content (only show when not loading) -->
    <template v-else>
      <!-- Empty state -->
      <BravoZeroStateScreen
        v-if="!communication.messages.length"
        title="Start New Chat"
        message="Start a new chat conversation by sending your first message."
        :showButton="false"
        :imageSrc="EmailZeroStateSvg"
        image-alt="Chat Messages"
        :action-handler="() => {}"
        class="flex items-center justify-center h-full"
      />

      <!-- Connection status -->
      <div
        v-if="!isConnected && !communication.messages.length"
        class="px-4 py-2 text-sm flex items-center space-x-2"
        :class="[
          isConnected ? 'bg-green-100 text-green-700' : 'bg-yellow-100 text-yellow-700'
        ]"
      >
        <div 
          class="w-2 h-2 rounded-full"
          :class="[
            isConnected ? 'bg-green-500' : 'bg-yellow-500'
          ]"
        ></div>
        <span>{{ isConnected ? 'Connected to room' : 'Connecting...' }}</span>
      </div>

      <div 
        ref="messagesContainer"
        class="flex-1 overflow-y-auto p-4 space-y-1" style="flex-basis:600px;"
      >
        <!-- Message groups -->
        <TransitionGroup name="message-slide" tag="div" class="space-y-1">
          <ChatMessage
            v-for="(message, index) in filteredMessages"
            :key="message.id"
            :message="message"
            :previous-message="filteredMessages[index - 1]"
            :participants="communication.participants"
            :message-previews="messagePreviews"
            :on-action-click="handleActionClick"
            :on-image-click="openImageModal"
            :on-image-load="debouncedScrollOnImageLoad"
          />
        </TransitionGroup>

        <!-- Typing indicator -->
        <div 
          v-if="communication.customerTyping"
          class="flex items-center space-x-2 text-gray-500 pl-12"
        >
          <div class="flex space-x-1">
            <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
            <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
            <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.4s"></div>
          </div>
          <span class="text-sm">Customer is typing...</span>
        </div>
      </div>
      
      <!-- Message composer -->
      <div class="border-t border-gray-200" style="box-shadow: 0 -2px 10px -1px rgba(0, 0, 0, 0.05);">
        <div class="px-2 py-2">
          <textarea 
            ref="textareaRef"
            v-model="newMessage"
            class="w-full resize-none focus:outline-none min-h-[80px] p-3 text-[#282A2C]"
            placeholder="Type your message..."
            @input="handleInput"
            @keydown.exact="handleKeyDown"
            @keydown.tab.prevent="mentionDropdown.show && insertMention(filteredUsers[selectedMentionIndex])"
            @keydown.enter.exact.prevent="handleEnterKey"
            @keydown.shift.enter="handleShiftEnter"
          ></textarea>
          
          <!-- Mention dropdown -->
          <div 
            v-if="mentionDropdown.show"
            class="absolute bg-white rounded-lg shadow-lg border border-gray-200 h-[200px] overflow-y-auto z-50 w-64"
            :style="{
              top: `${mentionDropdown.top}px`,
              left: `${mentionDropdown.left}px`
            }"
          >
            <div class="py-1 h-full">
              <div
                v-for="user in filteredUsers"
                :key="user.id"
                class="px-4 py-2 cursor-pointer flex items-center gap-2"
                :class="{
                  'bg-blue-50': selectedMentionIndex === filteredUsers.indexOf(user),
                  'hover:bg-gray-100': selectedMentionIndex !== filteredUsers.indexOf(user)
                }"
                @click="insertMention(user)"
              >
                <BravoAvatar
                  :firstName="user.name.split(' ')[0]"
                  :lastName="user.name.split(' ')[1] || ''"
                  :backgroundColor="getAvatarColor(user.name)"
                  :style="{ color: '#ffffff' }"
                  size="32"
                />
                <span class="truncate">{{ user.name }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Staged files -->
        <div v-if="stagedFiles.length" class="px-2 pt-4">
          <div class="text-sm font-medium text-gray-500 mb-2">
            Attached Files ({{ stagedFiles.length }})
          </div>
          <div class="space-y-2">
            <div 
              v-for="file in stagedFiles"
              :key="file.id"
              class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg"
            >
              <!-- File preview/icon -->
              <div class="w-12 h-12 flex-shrink-0 rounded overflow-hidden bg-gray-200 flex items-center justify-center">
                <img 
                  v-if="file.preview" 
                  :src="file.preview" 
                  :alt="file.name"
                  class="w-full h-full object-cover"
                />
                <i 
                  v-else
                  class="text-gray-400 text-xl"
                  :class="getFileIcon(file.type)"
                ></i>
              </div>
              
              <!-- File info -->
              <div class="flex-1 min-w-0">
                <div class="font-medium truncate" :title="file.name">{{ file.name }}</div>
                <div class="text-sm text-gray-500">
                  {{ formatFileSize(file.size) }} • {{ getFileTypeLabel(file.type) }}
                </div>
              </div>
              
              <!-- Remove button -->
              <button 
                class="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-colors"
                @click="stagedFiles = stagedFiles.filter(f => f.id !== file.id)"
                title="Remove file"
              >
                <i class="pi pi-times"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- File upload progress -->
        <div v-if="uploadingFiles.length" class="px-2 pt-4 space-y-2">
          <FileUploadProgress
            v-for="file in uploadingFiles"
            :key="file.id"
            :file-name="file.name"
            :size="file.size"
            :progress="file.progress"
          />
        </div>

        <!-- Action buttons -->
        <div class="mx-2 border-t border-gray-200">
          <div class="flex justify-between items-center py-2">
          <div class="flex items-center">
            <BravoButton
              icon="pi pi-paperclip"
              text
              severity="secondary"
              @click="fileInput?.click()"
              class="mr-2"
              v-tooltip.bottom="{ value: 'Attach File', showDelay: 300 }"
            />
            <Dropdown
              v-model="selectedTemplate"
              :options="templateOptions"
              optionLabel="label"
              optionValue="value"
              placeholder=""
              class="quick-reply-dropdown"
              @change="applyTemplate"
              :filter="false"
              :showClear="false"
              :loading="loadingCannedResponses"
              :disabled="loadingCannedResponses || !templateOptions.length"
              v-tooltip.bottom="'Quick Replies'"
            >
              <template #value>
                <div class="flex items-center">
                  <i class="pi pi-comments"></i>
                </div>
              </template>
              <template #option="slotProps">
                <div class="flex items-center">
                  <i class="pi pi-file mr-2 text-gray-400"></i>
                  <span>{{ slotProps.option.label }}</span>
                </div>
              </template>
            </Dropdown>
            <Dropdown
              v-model="selectedAiAction"
              :options="aiActions"
              optionLabel="label"
              optionValue="value"
              placeholder=""
              class="ai-assistant-dropdown"
              @change="handleAiAction"
              :filter="false"
              :showClear="false"
              v-tooltip.bottom="'AI Assistant'"
            >
              <template #value>
                <div class="flex items-center">
                  <i class="pi pi-sparkles"></i>
                </div>
              </template>
              <template #option="slotProps">
                <div class="flex items-center">
                  <i :class="slotProps.option.icon" class="mr-2 text-gray-400"></i>
                  <span>{{ slotProps.option.label }}</span>
                </div>
              </template>
            </Dropdown>
          </div>
          <div>
            <BravoButton
              label="Send"
              size="small"
              severity="primary"
              :disabled="!newMessage.trim() && stagedFiles.length === 0"
              @click="sendMessage"
            />
          </div>
          </div>
        </div>
      </div>
      <input
        ref="fileInput"
        type="file"
        multiple
        class="hidden"
        @change="handleFileUpload"
      >
    </template>

    <!-- Image Modal -->
    <BravoDialog
      v-model:visible="showImageModal"
      modal
      :closable="true"
      :style="{ width: '90vw', maxWidth: '800px' }"
      class="image-modal"
    >
      <template #header>
        <span>{{ modalImageAlt }}</span>
      </template>
      <div class="flex justify-center p-4">
        <img 
          :src="modalImageUrl" 
          :alt="modalImageAlt"
          class="max-w-full h-auto max-h-[70vh] object-contain"
        />
      </div>
    </BravoDialog>

    <!-- Summary Dialog -->
    <BravoDialog
      v-model:visible="showSummaryDialog"
      modal
      header="Conversation Summary"
      :style="{ width: '600px' }"
      :closable="true"
    >
      <div class="p-4">
        <div v-if="isSummarizing" class="flex flex-col items-center justify-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <div class="mt-4 text-gray-600">
            Generating summary...
          </div>
        </div>
        <div 
          v-else
          class="prose max-w-none"
          v-html="summaryContent"
        ></div>
      </div>
      <template #footer>
        <div class="flex justify-end">
          <BravoButton
            label="Close"
            severity="secondary"
            @click="showSummaryDialog = false"
          />
        </div>
      </template>
    </BravoDialog>
  </div>
</template>

<style scoped>


/* Animation styles */
.animate-bounce {
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-4px);
  }
}

/* Message slide-up animation */
.message-slide-enter-active {
  transition: all 0.4s ease-out;
}

.message-slide-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.message-slide-enter-to {
  opacity: 1;
  transform: translateY(0);
}

/* Dropdown styles */
:deep(.p-dropdown) {
  @apply border border-gray-200 rounded-lg shadow-lg;
  min-width: 200px;
}

:deep(.p-dropdown-item) {
  @apply px-4 py-2;
}

/* Quick Reply Dropdown Styling */
:deep(.quick-reply-dropdown) {
  border: none !important;
  background: transparent !important;
  box-shadow: none !important;
  padding: 0 !important;
  width: auto !important;
  min-width: auto !important;
}

:deep(.quick-reply-dropdown .p-select-dropdown) {
  width: 0 !important;
}

:deep(.quick-reply-dropdown .p-dropdown-trigger) {
  background: transparent !important;
  border: none !important;
  color: #6b7280 !important;
  padding: 4px !important;
  width: auto !important;
  display: none !important;
}

:deep(.quick-reply-dropdown .p-dropdown-trigger-icon) {
  display: none !important;
}

:deep(.quick-reply-dropdown .p-icon) {
  display: none !important;
}

:deep(.quick-reply-dropdown .pi-chevron-down) {
  display: none !important;
}

:deep(.quick-reply-dropdown .p-dropdown-label) {
  background: transparent !important;
  border: none !important;
  padding: 4px !important;
  color: #374151 !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  width: auto !important;
  min-width: auto !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

:deep(.quick-reply-dropdown:not(.p-disabled):hover) {
  background-color: #f9fafb !important;
  border-radius: 6px !important;
}

:deep(.quick-reply-dropdown:not(.p-disabled):hover .p-dropdown-label) {
  background-color: transparent !important;
}

:deep(.quick-reply-dropdown:not(.p-disabled):hover .p-dropdown-trigger) {
  background-color: transparent !important;
}

:deep(.quick-reply-dropdown.p-focus) {
  box-shadow: none !important;
  border: none !important;
  background-color: #f3f4f6 !important;
  border-radius: 6px !important;
}



/* Modal styles */
:deep(.image-modal .p-dialog-content) {
  padding: 0;
}

:deep(.image-modal .p-dialog-header) {
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
}



/* AI Assistant Dropdown Styling - Same as Quick Reply */
:deep(.ai-assistant-dropdown) {
  border: none !important;
  background: transparent !important;
  box-shadow: none !important;
  padding: 0 !important;
  width: auto !important;
  min-width: auto !important;
}

:deep(.ai-assistant-dropdown .p-select-dropdown) {
  width: 0 !important;
}

:deep(.ai-assistant-dropdown .p-dropdown-trigger) {
  background: transparent !important;
  border: none !important;
  color: #6b7280 !important;
  padding: 4px !important;
  width: auto !important;
  display: none !important;
}

:deep(.ai-assistant-dropdown .p-dropdown-trigger-icon) {
  display: none !important;
}

:deep(.ai-assistant-dropdown .p-icon) {
  display: none !important;
}

:deep(.ai-assistant-dropdown .pi-chevron-down) {
  display: none !important;
}

:deep(.ai-assistant-dropdown .p-dropdown-label) {
  background: transparent !important;
  border: none !important;
  padding: 4px !important;
  color: #374151 !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  width: auto !important;
  min-width: auto !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

:deep(.ai-assistant-dropdown:not(.p-disabled):hover) {
  background-color: #f9fafb !important;
  border-radius: 6px !important;
}

:deep(.ai-assistant-dropdown:not(.p-disabled):hover .p-dropdown-label) {
  background-color: transparent !important;
}

:deep(.ai-assistant-dropdown:not(.p-disabled):hover .p-dropdown-trigger) {
  background-color: transparent !important;
}

:deep(.ai-assistant-dropdown.p-focus) {
  box-shadow: none !important;
  border: none !important;
  background-color: #f3f4f6 !important;
  border-radius: 6px !important;
}
</style>