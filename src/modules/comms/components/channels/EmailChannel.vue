<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch } from 'vue';
import type { Communication, Message, XMPPNotificationObject, MessageEmail } from '../../types';
import { tryParseMessageContent, shouldHideMessage, formatFileSize } from '../../types';
import { format } from 'date-fns';
import { useCommunicationsStore } from '../../stores/communications';
import { useCommsAPI } from '@/composables/services/useCommsAPI';
import { htmlToPlainText, separateEmailContent, getAvatarColor } from '../../utils/commHelpers';
import { useUserStore } from '@/stores/user';
import { useMetaStore } from '@/stores/meta';
import { xmppService } from '../../services/xmpp';
import { nextTick } from 'vue';
import FileUploadProgress from '../FileUploadProgress.vue';
import Dropdown from 'primevue/dropdown';
import Editor from 'primevue/editor';
import Dialog from 'primevue/dialog';
import Tooltip from 'primevue/tooltip';
import { useAIAssistant } from '@/composables/useAIAssistant';
import { useCannedResponseTokens } from '@/composables/useCannedResponseTokens';
import type { Issue } from '@/composables/services/useIssuesAPI';
import BravoAvatar from '@services/ui-component-library/components/BravoAvatar.vue';
import BravoZeroStateScreen from '@services/ui-component-library/components/BravoZeroStateScreen.vue';
import BravoTimestamp from '@services/ui-component-library/components/BravoTimestamp.vue';
import BravoSkeleton from '@services/ui-component-library/components/BravoSkeleton.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import EmailZeroStateSvg from '@/assets/email-zero-state.svg';
import BravoChip from '@services/ui-component-library/components/BravoChip.vue';
import BravoLabel from '@services/ui-component-library/components/BravoLabel.vue';
import BravoBody from '@services/ui-component-library/components/BravoTypography/BravoBody.vue';
import InputText from 'primevue/inputtext';
import CommsErrorSvg from '@/assets/comms-error.svg?url';

const props = defineProps<{
  communication: Communication;
  issue?: Issue | null;
}>();

// Register the tooltip directive
const vTooltip = Tooltip;

const store = useCommunicationsStore();
const commsAPI = useCommsAPI();
const userStore = useUserStore();
const metaStore = useMetaStore();
const aiAssistant = useAIAssistant();
const { processCannedResponse } = useCannedResponseTokens(computed(() => props.issue || null));
const expandedMessages = ref<Set<string>>(new Set());
const showMetadata = ref<Set<string>>(new Set());
const fileInput = ref<HTMLInputElement | null>(null);
const uploadingFiles = ref<Array<{ id: string; name: string; size: number; progress: number }>>([]);
const attachedFiles = ref<Array<{ id: string; name: string; size: number; type: string; file: File; preview?: string }>>([]);
const showTemplates = ref(false);
const selectedTemplate = ref(null);
const selectedAiAction = ref(null);
const showSummaryDialog = ref(false);
const summaryContent = ref('');
const isSummarizing = ref(false);
const isGeneratingReply = ref(false);
const isReformatting = ref(false);
const isSendingEmail = ref(false);
const isCleaningUp = ref(false);
const showLinkDialog = ref(false);
const linkUrl = ref('');
const selectedText = ref('');
const editorRef = ref();
const showEditorToolbar = ref(false);
const textareaRef = ref<HTMLTextAreaElement | null>(null);
const selectionRange = ref<{ start: number; end: number } | null>(null);
const messagesContainer = ref<HTMLElement | null>(null);

// Simplified loading state
const isLoading = ref(true);

// Composer resizing
const composerHeight = ref('300px'); // Default height in pixels
const isResizingComposer = ref(false);
const baseComposerHeight = '250px'; // Base height without fields
const expandedComposerHeight = '350px'; // Expanded height with fields
const newEmailComposerHeight = '60%'; // Percentage of the communication panel container for new emails

// Email content separation - track which messages have expanded quoted content
const expandedQuotedContent = ref<Set<string>>(new Set());

const scrollToBottom = async () => {
  await nextTick();
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
  }
};

// Enhanced message parsing function for emails
const parseEmailMessage = (message: Message): {
  parsedContent: XMPPNotificationObject | null;
  emailData: MessageEmail | null;
  displayContent: string;
  shouldHide: boolean;
} => {
  const parseResult = tryParseMessageContent(message.content);
  
  if (!parseResult.isParsed) {
    // Plain text message
    return {
      parsedContent: null,
      emailData: null,
      displayContent: message.content,
      shouldHide: false
    };
  }

  const notificationObj = parseResult.data!;
  const shouldHide = shouldHideMessage(notificationObj);

  // Extract email data
  const emailData = notificationObj.email || null;
  
  // Handle different message types
  let displayContent = '';
  
  if (emailData?.body) {
    displayContent = emailData.body;
  } else if (notificationObj.message) {
    displayContent = decodeURIComponent(String(notificationObj.message).replace(/\+/g, ' '));
  }

  return {
    parsedContent: notificationObj,
    emailData,
    displayContent,
    shouldHide
  };
};

// Get sender details with enhanced information
const getSenderDetails = (message: Message) => {
  const participant = props.communication.participants.find(p => p.id === message.senderId);
  const parseResult = parseEmailMessage(message);
  
  return {
    name: parseResult.parsedContent?.context?.from_name || 
          parseResult.emailData?.from_name || 
          participant?.name || 
          message.senderId.split('@')[0],
    email: parseResult.parsedContent?.context?.from || 
           parseResult.emailData?.from || 
           participant?.email || 
           `${message.senderId}@example.com`,
    initials: (parseResult.parsedContent?.context?.from_name || 
               parseResult.emailData?.from_name || 
               participant?.name || 
               message.senderId).charAt(0).toUpperCase(),
    avatar: parseResult.parsedContent?.context?.from_avatar || participant?.avatar
  };
};

// Check if message is from current user
const isMessageFromCurrentUser = (message: Message): boolean => {
  return store.isCurrentUser(message.senderId);
};

// Helper function to check if attachment is valid (not empty/null Gmail artifact)
const isValidAttachment = (attachment: any): boolean => {
  if (!attachment) return false;
  
  // Check if attachment has null/empty values (Gmail artifacts)
  if (attachment.id === null && 
      attachment.name === null && 
      attachment.size === null && 
      attachment.type === null && 
      attachment.url === null) {
    return false;
  }
  
  // Additional check for size and name
  if (!attachment.name || attachment.size === 0 || attachment.size === null) {
    return false;
  }
  
  return true;
};

// Define messages computed property with enhanced parsing
// Only recompute when communication messages actually change, not on other reactive changes
const messages = computed(() => {
  // Create a stable reference based only on communication messages
  const communicationMessages = props.communication.messages;
  
  console.log('📧 EmailChannel: Processing messages for communication:', props.communication.id, 'Total raw messages:', communicationMessages.length);
  
  // Filter out system messages and hidden messages, then process
  const processedMessages = communicationMessages
    .filter(message => {
      if (message.type === 'system') {
        console.log('📧 EmailChannel: Filtering out system message:', message.id);
        return false;
      }
      const parseResult = parseEmailMessage(message);
      if (parseResult.shouldHide) {
        console.log('📧 EmailChannel: Filtering out hidden message:', message.id, 'Content preview:', message.content.substring(0, 100));
        return false;
      }
      console.log('📧 EmailChannel: Keeping message:', message.id, 'Type:', message.type, 'Content preview:', message.content.substring(0, 50));
      return true;
    })
    .map((message: Message) => {
      const parseResult = parseEmailMessage(message);
      const sender = getSenderDetails(message);
      
      // Only consider it an attachment if it's valid (not empty Gmail artifact)
      const attachment = parseResult.parsedContent?.attachment;
      const hasValidAttachment = attachment && isValidAttachment(attachment);
      
      return {
        ...message,
        sender,
        parsedContent: parseResult.parsedContent,
        emailData: parseResult.emailData,
        displayContent: parseResult.displayContent,
        hasAttachment: hasValidAttachment,
        attachmentData: hasValidAttachment ? attachment : null
      };
    });

  // Group file attachments with their parent messages
  const groupedMessages = [];
  const processedMessagesArray = [...processedMessages]; // Create a copy to avoid mutation issues
  const skippedIndices = new Set<number>(); // Track which messages to skip
  
  for (let i = 0; i < processedMessagesArray.length; i++) {
    if (skippedIndices.has(i)) continue; // Skip messages that were grouped
    
    const currentMessage = processedMessagesArray[i];
    
    // Check if this is an email message with content (not just an attachment)
    const hasEmailContent = currentMessage.emailData?.body && currentMessage.emailData.body.trim();
    
    if (hasEmailContent) {
      // This is a main email message - look for related attachments
      const attachments: any[] = [];
      const currentTimestamp = currentMessage.timestamp.getTime();
      const currentSender = currentMessage.senderId;
      
      // Look ahead for attachment messages with the same timestamp and sender
      let j = i + 1;
      while (j < processedMessagesArray.length) {
        const potentialAttachment = processedMessagesArray[j];
        const attachmentTimestamp = potentialAttachment.timestamp.getTime();
        const attachmentSender = potentialAttachment.senderId;
        
        // Check if this is an attachment from the same email (same timestamp and sender)
        const hasValidAttachment = potentialAttachment.parsedContent?.attachment && 
          isValidAttachment(potentialAttachment.parsedContent.attachment);
        const hasNoEmailContent = !potentialAttachment.emailData?.body || !potentialAttachment.emailData.body.trim();
        
        const isAttachmentFromSameEmail = 
          Math.abs(attachmentTimestamp - currentTimestamp) < 5000 && // Within 5 seconds
          attachmentSender === currentSender &&
          hasValidAttachment &&
          hasNoEmailContent;
        
        if (isAttachmentFromSameEmail && potentialAttachment.parsedContent?.attachment) {
          // This is an attachment for the current email
          attachments.push(potentialAttachment.parsedContent.attachment);
          // Mark this message to be skipped
          skippedIndices.add(j);
        } else {
          // Different email or timestamp too far apart, stop looking
          break;
        }
        j++;
      }
      
      // Add attachments to the current message
      if (attachments.length > 0) {
        (currentMessage as any).attachments = attachments;
        currentMessage.hasAttachment = true;
        // For backward compatibility, also set attachmentData to the first attachment
        currentMessage.attachmentData = attachments[0] ?? null;
        console.log('📧 EmailChannel: Grouped', attachments.length, 'attachments with email message:', currentMessage.id);
      }
      
      groupedMessages.push(currentMessage);
    } else {
      // This is a standalone message (not grouped with an email)
      // Check if it has an attachment
      if (currentMessage.parsedContent?.attachment && isValidAttachment(currentMessage.parsedContent.attachment)) {
        currentMessage.attachmentData = currentMessage.parsedContent.attachment;
        currentMessage.hasAttachment = true;
      }
      groupedMessages.push(currentMessage);
    }
  }
  
  console.log('📧 EmailChannel: Final processed messages count:', groupedMessages.length, 'for communication:', props.communication.id);
  
  return groupedMessages;
});

// Simplified mount
onMounted(() => {
  // Expand the latest message
  const latestMessage = messages.value[messages.value.length - 1];
  if (latestMessage) {
    expandedMessages.value = new Set([latestMessage.id]);
  }
  
  // For new email communications (no messages), show fields expanded
  if (messages.value.length === 0) {
    composerState.value.minimizedFields = false;
    composerHeight.value = newEmailComposerHeight;
    console.log('📧 EmailChannel: New email communication detected, showing fields expanded with larger height');
  }
  
  setTimeout(() => {
    isLoading.value = false;
    scrollToBottom();
  }, 100);
  
  // Load canned responses when component mounts
  loadCannedResponses();
});

// Watch for new messages and scroll to bottom
watch(() => messages.value.length, (newLength, oldLength) => {
  // If new messages were added, expand only the latest one
  if (newLength > oldLength) {
    const latestMessage = messages.value[messages.value.length - 1];
    if (latestMessage) {
      expandedMessages.value = new Set([latestMessage.id]);
    }
  }
  scrollToBottom();
});

// Add watcher for communication changes
watch(() => props.communication.id, () => {
  isLoading.value = true;
  
  // For new email communications (no messages), show fields expanded
  if (messages.value.length === 0) {
    composerState.value.minimizedFields = false;
    composerHeight.value = newEmailComposerHeight;
    console.log('📧 EmailChannel: New email communication detected on change, showing fields expanded with larger height');
  }
  
  setTimeout(() => {
    isLoading.value = false;
    scrollToBottom();
  }, 100);
  
  // Reload canned responses when communication changes
  loadCannedResponses();
});

// Fetch canned responses from API
const cannedResponses = ref<any[]>([]);
const loadingCannedResponses = ref(false);

// Get filtered canned responses based on partner team
const filteredCannedResponses = computed(() => {
  if (!cannedResponses.value.length) return [];
  
  // Get the issue's owner partner team ID
  const issueTeamId = props.issue?.owner_partners_teams_id;
  const issuePartnerId = props.issue?.owner_partners_id;
  
  console.log('🔍 Filtering canned responses:', {
    issueTeamId,
    issuePartnerId,
    totalResponses: cannedResponses.value.length,
    issue: props.issue
  });
  if (!issueTeamId && !issuePartnerId) {
    // If no team or partner ID, return all responses
    console.log('🔍 No issue team/partner ID found, returning all responses');
    return cannedResponses.value;
  }
  
  // First try to filter by exact team match
  let filtered = [];
  if (issueTeamId) {
    filtered = cannedResponses.value.filter(response => 
      response.partners_teams_ids && 
      response.partners_teams_ids.includes(issueTeamId)
    );
    console.log('🔍 Exact team match results:', filtered.length);
  }
  
  // If no exact matches, try filtering by partner ID (less restrictive)
  if (filtered.length === 0 && issuePartnerId) {
    filtered = cannedResponses.value.filter(response => 
      response.partners_id === issuePartnerId
    );
    console.log('🔍 Partner ID match results:', filtered.length);
  }
  
  // If still no matches, return all responses (fallback)
  if (filtered.length === 0) {
    console.log('🔍 No matches found, returning all responses as fallback');
    return cannedResponses.value;
  }
  
  console.log('🔍 Final filtered responses:', filtered.length);
  return filtered;
});

// Convert filtered canned responses to dropdown options
const templateOptions = computed(() => {
  const options = filteredCannedResponses.value.map(response => ({
    label: response.name || response.lbl || response._title,
    value: response.id,
    content: response.content
  }));
  
  console.log('📋 Template options computed:', {
    loading: loadingCannedResponses.value,
    optionsLength: options.length,
    disabled: loadingCannedResponses.value || !options.length,
    cannedResponsesLength: cannedResponses.value.length,
    filteredLength: filteredCannedResponses.value.length
  });
  
  return options;
});

const loadCannedResponses = async () => {
  try {
    loadingCannedResponses.value = true;
    const response = await metaStore.fetchCannedResponses({
      object: 'issues', // These params are no longer used but kept for interface compatibility
      object_id: props.communication.availableComm?.id || ''
    });
    cannedResponses.value = response.canned_responses?.results || response.pl__canned_responses || [];
    console.log('📋 Loaded canned responses:', cannedResponses.value.length);
  } catch (error) {
    console.error('❌ Failed to load canned responses:', error);
    cannedResponses.value = [];
  } finally {
    loadingCannedResponses.value = false;
  }
};

const selectedTemplateValue = ref('');

const applyTemplate = () => {
  try {
    if (selectedTemplateValue.value) {
      const template = templateOptions.value.find(t => t.value === selectedTemplateValue.value);
      if (template && template.content) {
        // Process the template content to replace tokens with actual case data
        const processedContent = processCannedResponse(template.content);
        
        // Append to existing content instead of replacing
        const existingContent = composerState.value.content || '';
        const separator = existingContent.trim() ? '<br><br>' : '';
        composerState.value.content = existingContent + separator + processedContent;
        
        console.log('✅ Applied template:', template.label);
      }
      selectedTemplateValue.value = ''; // Reset selection
    }
  } catch (error) {
    console.error('❌ Error applying template:', error);
  }
};

const aiActions = [
  {
    label: 'Summarize Conversation',
    value: 'summarize',
    icon: 'pi pi-list'
  },
  {
    label: 'Generate Next Reply',
    value: 'generate',
    icon: 'pi pi-reply'
  },
  {
    label: 'Reformat Current Response',
    value: 'reformat',
    icon: 'pi pi-pencil'
  },
  {
    label: 'Cleanup',
    value: 'cleanup',
    icon: 'pi pi-eraser'
  }
];

const handleAiAction = async () => {
  if (!selectedAiAction.value) return;

  try {
    switch (selectedAiAction.value) {
      case 'summarize':
        showSummaryDialog.value = true;
        isSummarizing.value = true;
        // Use the AI assistant composable
        summaryContent.value = await aiAssistant.summarizeConversation(messages.value);
        break;
      case 'generate':
        isGeneratingReply.value = true;
        // Use the AI assistant composable
        const generatedReply = await aiAssistant.generateReply(messages.value);
        // Update the editor content with the generated reply
        composerState.value.content = generatedReply;
        break;
      case 'reformat':
        if (!composerState.value.content.trim()) {
          return;
        }
        isReformatting.value = true;
        const reformattedContent = await aiAssistant.reformatEmail(composerState.value.content);
        // Update the editor content with the reformatted text
        composerState.value.content = reformattedContent;
        break;
      case 'cleanup':
        if (!composerState.value.content.trim()) {
          return;
        }
        isCleaningUp.value = true;
        const cleanedUpContent = await aiAssistant.cleanupResponse(composerState.value.content);
        // Update the editor content with the cleaned up text
        composerState.value.content = cleanedUpContent;
        break;
    }
  } catch (error) {
    console.error('AI action failed:', error);
    if (selectedAiAction.value === 'summarize') {
      summaryContent.value = 'Failed to process request. Please try again.';
    }
  } finally {
    isSummarizing.value = false;
    isGeneratingReply.value = false;
    isReformatting.value = false;
    isCleaningUp.value = false;
    selectedAiAction.value = null;
  }
};

const composerState = ref({
  to: [] as string[],
  cc: [] as string[],
  subject: '',
  content: '',
  minimizedFields: true
});

// Composer resize functionality
const startComposerResize = (event: MouseEvent) => {
  event.preventDefault();
  event.stopPropagation();

  // Add a class to the resize handle
  const resizeHandle = event.currentTarget as HTMLElement;
  resizeHandle.classList.add('resizing');

  // Add a transparent overlay that covers the whole document to capture mouse events
  const overlay = document.createElement('div');
  overlay.style.position = 'fixed';
  overlay.style.top = '0';
  overlay.style.left = '0';
  overlay.style.width = '100%';
  overlay.style.height = '100%';
  overlay.style.backgroundColor = 'transparent';
  overlay.style.zIndex = '9999';
  overlay.style.cursor = 'row-resize';
  document.body.appendChild(overlay);

  // Get the composer container to calculate relative heights
  const composerContainer = resizeHandle.closest('.composer-container') as HTMLElement;
  if (!composerContainer) {
    console.error('📏 Could not find composer container for resizing');
    return;
  }

  // Store initial position and height
  const startY = event.clientY;
  const startHeight = composerContainer.offsetHeight;
  
  document.body.classList.add('resizing-composer');
  isResizingComposer.value = true;

  const handleMouseMove = (moveEvent: MouseEvent) => {
    // Calculate the height change (negative because we're dragging up to make it taller)
    const deltaY = startY - moveEvent.clientY;
    const newHeight = Math.max(200, Math.min(800, startHeight + deltaY)); // Min 200px, max 800px
    
    // Always set as pixel value when manually resizing
    composerHeight.value = `${newHeight}px`;
  };

  const handleMouseUp = () => {
    // Clean up
    document.body.classList.remove('resizing-composer');
    document.body.removeChild(overlay);
    resizeHandle.classList.remove('resizing');
    isResizingComposer.value = false;
    
    // Remove event listeners
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
    
    // Save the new height to localStorage (only if it's a pixel value)
    if (composerHeight.value.endsWith('px')) {
      const heightValue = parseInt(composerHeight.value, 10);
      if (typeof localStorage !== 'undefined') {
        localStorage.setItem('email-composer-height', String(heightValue));
      }
      console.log('📏 EmailChannel: Saved manually resized height:', heightValue);
    }
  };

  // Add event listeners
  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);
};

// Load saved composer height from localStorage on mount
const loadSavedComposerHeight = () => {
  const isNewEmail = messages.value.length === 0;
  
  // For new emails, always use the percentage height initially
  if (isNewEmail) {
    composerHeight.value = composerState.value.minimizedFields ? baseComposerHeight : newEmailComposerHeight;
    console.log('📏 EmailChannel: Set new email composer height to', composerHeight.value);
    return;
  }
  
  // For existing emails, try to load saved height first
  if (typeof localStorage !== 'undefined') {
    const savedHeight = localStorage.getItem('email-composer-height');
    if (savedHeight) {
      const height = parseInt(savedHeight, 10);
      // Only use saved height if it's reasonable
      if (height >= 200 && height <= 800) {
        composerHeight.value = `${height}px`;
        console.log('📏 EmailChannel: Loaded saved composer height:', height);
        return;
      }
    }
  }
  
  // Fallback to default heights for existing emails
  composerHeight.value = composerState.value.minimizedFields ? baseComposerHeight : expandedComposerHeight;
  console.log('📏 EmailChannel: Set default composer height to', composerHeight.value, 'for existing email');
};

const handleInsertLink = () => {
  if (!textareaRef.value) return;
  const start = textareaRef.value.selectionStart;
  const end = textareaRef.value.selectionEnd;
  if (start === end) return;
  selectedText.value = composerState.value.content.substring(start, end);
  selectionRange.value = { start, end };
  showLinkDialog.value = true;
};

const insertLink = () => {
  if (!selectionRange.value || !linkUrl.value.trim()) return;
  const { start, end } = selectionRange.value;
  const linkHtml = `<a href="${linkUrl.value}">${selectedText.value}</a>`;
  composerState.value.content = 
    composerState.value.content.substring(0, start) +
    linkHtml +
    composerState.value.content.substring(end);
  showLinkDialog.value = false;
  linkUrl.value = '';
  selectedText.value = '';
  selectionRange.value = null;
};

const handleFileUpload = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    Array.from(input.files).forEach(async (file) => {
      const fileId = crypto.randomUUID();
      
      // Add to uploading files for progress display
      uploadingFiles.value.push({
        id: fileId,
        name: file.name,
        size: file.size,
        progress: 0
      });

      // Create preview for images
      let preview: string | undefined;
      if (file.type && file.type.startsWith('image/')) {
        try {
          preview = await createImagePreview(file);
        } catch (error) {
          console.warn('Failed to create preview for', file.name, error);
        }
      }

      // Simulate upload progress
      let progress = 0;
      const interval = setInterval(() => {
        progress += 5;
        const fileIndex = uploadingFiles.value.findIndex((f: any) => f.id === fileId);
        if (fileIndex !== -1) {
          uploadingFiles.value[fileIndex].progress = progress;
        }
        
        if (progress >= 100) {
          clearInterval(interval);
          setTimeout(() => {
            // Remove from uploading and add to attached
            uploadingFiles.value = uploadingFiles.value.filter((f: any) => f.id !== fileId);
            attachedFiles.value.push({
              id: fileId,
              name: file.name,
              size: file.size,
              type: file.type,
              file: file,
              preview: preview
            });
          }, 500);
        }
      }, 200);
    });

    // Clear the input so the same file can be selected again
    input.value = '';
  }
};

// Helper function to create image previews
const createImagePreview = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target?.result) {
        resolve(e.target.result as string);
      } else {
        reject(new Error('Failed to read file'));
      }
    };
    reader.onerror = () => reject(new Error('FileReader error'));
    reader.readAsDataURL(file);
  });
};

const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  if (!target.closest('.metadata-toggle') && !target.closest('.metadata-dropdown')) {
    showMetadata.value.clear();
  }
};

onMounted(() => {
  window.addEventListener('click', handleClickOutside);
  loadSavedComposerHeight();
});

onBeforeUnmount(() => {
  window.removeEventListener('click', handleClickOutside);
});

const toggleMessage = (messageId: string) => {
  if (expandedMessages.value.has(messageId)) {
    expandedMessages.value.delete(messageId);
  } else {
    expandedMessages.value.add(messageId);
  }
};

const isMessageExpanded = (messageId: string) => {
  return expandedMessages.value.has(messageId);
};

const toggleQuotedContent = (messageId: string) => {
  if (expandedQuotedContent.value.has(messageId)) {
    expandedQuotedContent.value.delete(messageId);
  } else {
    expandedQuotedContent.value.add(messageId);
  }
};

const isQuotedContentExpanded = (messageId: string) => {
  return expandedQuotedContent.value.has(messageId);
};

const toggleMetadata = (messageId: string) => {
  if (showMetadata.value.has(messageId)) {
    showMetadata.value.delete(messageId);
  } else {
    showMetadata.value.add(messageId);
  }
};

const formatDetailedDate = (date: Date) => {
  return `${format(date, 'MMM d, yyyy, h:mm:ss a')} (${
    Intl.DateTimeFormat().resolvedOptions().timeZone
  })`;
};

const getFileTypeLabel = (mimeType: string) => {
  if (!mimeType) return 'File';
  if (mimeType.startsWith('image/')) return 'Image';
  if (mimeType.startsWith('video/')) return 'Video';
  if (mimeType.startsWith('audio/')) return 'Audio';
  if (mimeType === 'application/pdf') return 'PDF';
  if (mimeType.includes('word') || mimeType.includes('document')) return 'Document';
  if (mimeType.includes('sheet') || mimeType.includes('excel')) return 'Spreadsheet';
  if (mimeType.includes('presentation') || mimeType.includes('powerpoint')) return 'Presentation';
  if (mimeType.includes('zip') || mimeType.includes('rar') || mimeType.includes('archive')) return 'Archive';
  if (mimeType.includes('text/')) return 'Text';
  return 'File';
};

const sendEmail = async () => {
  if (!composerState.value.content.trim() && attachedFiles.value.length === 0) {
    return;
  }

  try {
    isSendingEmail.value = true;
    console.log('📧 EmailChannel: Sending email via API...');
    
    // Check if we have a room JID and if we're connected to the XMPP room
    if (!props.communication.roomJid) {
      console.error('❌ EmailChannel: No room JID available for communication:', props.communication.id);
      throw new Error('Communication not properly initialized - no room JID');
    }
    
    console.log('🔍 EmailChannel: Checking XMPP room connection for:', props.communication.roomJid);
    
    // First check if XMPP service is initialized and connected
    const isXmppInitialized = store.isXmppInitialized;
    console.log('🔍 EmailChannel: XMPP initialization status:', isXmppInitialized);
    
    if (!isXmppInitialized) {
      console.warn('⚠️ EmailChannel: XMPP service not initialized, email will be sent via API only');
    }
    
    // Check if we're connected to the XMPP room, if not, try to join it
    const isRoomConnected = xmppService.isRoomConnected(props.communication.roomJid);
    console.log('🔍 EmailChannel: Room connection status:', isRoomConnected);
    
    if (!isRoomConnected) {
      console.log('🔄 EmailChannel: Not connected to XMPP room, attempting to join before sending email...');
      try {
        await xmppService.joinRoom(props.communication);
        console.log('✅ EmailChannel: Successfully joined XMPP room before sending email');
      } catch (joinError) {
        console.error('❌ EmailChannel: Failed to join XMPP room before sending email:', joinError);
        // Don't throw - we'll still try to send the email via API and handle XMPP errors later
      }
    } else {
      console.log('✅ EmailChannel: Already connected to XMPP room');
    }
    
    // Prepare email data for the API call
    const toEmail = composerState.value.to.length > 0 ? composerState.value.to[0].trim() : (getEmailSummary.value.to || '');
    const emailReplyData = {
      to: toEmail,
      toLabel: toEmail,
      cc: composerState.value.cc.filter(email => email.trim()),
      subject: composerState.value.subject || getEmailSummary.value.subject || '',
      replyBody: composerState.value.content,
      from: toEmail,
      from_name: getEmailSummary.value.from || '',
      from_email: toEmail,
      origParts: []
    };

    // Prepare files for upload
    const files: File[] = attachedFiles.value.map(attachedFile => attachedFile.file);

    // Call the sendEmail API
    const emailResponse = await commsAPI.sendEmail({
      id: props.communication.id,
      commId: props.communication.id,
      from_email: emailReplyData.from_email,
      replyBody: emailReplyData.replyBody,
      subject: emailReplyData.subject,
      reply: emailReplyData,
      files: files.length > 0 ? files : undefined,
      file_tag: files.length > 0 ? 'attachment' : undefined
    });

    console.log('✅ EmailChannel: Email API response:', emailResponse);

    // Handle the response payloads - send each payload to XMPP
    if (emailResponse.payloads && emailResponse.payloads.length > 0) {
      console.log('📨 EmailChannel: Processing', emailResponse.payloads.length, 'payloads');
      
      // Check if XMPP is available before attempting to send
      if (!isXmppInitialized) {
        console.warn('⚠️ EmailChannel: Skipping XMPP payload delivery - service not initialized. Email was sent successfully via API.');
        console.log('📧 EmailChannel: Email payloads that would have been sent to XMPP:', emailResponse.payloads);
        // The email was still sent successfully via API, so we don't need to fail here
      } else {
        // XMPP is initialized, attempt to send payloads
        for (const messagePayload of emailResponse.payloads) {
          console.log('📤 EmailChannel: Sending payload to XMPP:', messagePayload);
          
          // Add additional debugging for new email communications
          const isNewEmail = messages.value.length === 0;
          console.log('🔍 EmailChannel: Debugging email send:', {
            isNewEmail,
            currentMessageCount: messages.value.length,
            communicationId: props.communication.id,
            roomJid: props.communication.roomJid,
            payloadContent: messagePayload.message ? decodeURIComponent(messagePayload.message.replace(/\+/g, ' ')) : 'No message content',
            payloadChannel: messagePayload.channel,
            payloadTimestamp: messagePayload.timestamp,
            isXmppInitialized,
            isRoomConnected
          });
          
          try {
            // Send each payload as a separate XMPP message
            const stringifiedPayload = JSON.stringify(messagePayload);
            console.log('📤 EmailChannel: Stringified payload being sent to XMPP:', stringifiedPayload);
            await xmppService.sendMessage(props.communication, stringifiedPayload);
            console.log('✅ EmailChannel: Successfully sent payload to XMPP');
          } catch (xmppError: any) {
            console.error('❌ EmailChannel: XMPP send failed:', xmppError);
            
            // Check if it's a connection/room error
            if (xmppError.message?.includes('Not connected to room') || 
                xmppError.message?.includes('room JID not available') ||
                xmppError.message?.includes('Not connected')) {
              console.log('🔄 EmailChannel: Attempting to rejoin XMPP room and retry...');
              
              try {
                // Attempt to rejoin the room
                await xmppService.joinRoom(props.communication);
                console.log('✅ EmailChannel: Successfully rejoined XMPP room');
                
                // Retry sending the payload
                await xmppService.sendMessage(props.communication, JSON.stringify(messagePayload));
                console.log('✅ EmailChannel: Successfully sent payload to XMPP after rejoin');
              } catch (retryError) {
                console.error('❌ EmailChannel: Failed to rejoin room or retry XMPP send:', retryError);
                console.warn('⚠️ EmailChannel: XMPP message delivery failed, but email was sent successfully via API');
                // Don't throw - email was already sent successfully via API
              }
            } else {
              console.error('❌ EmailChannel: Non-connection XMPP error:', xmppError);
              console.warn('⚠️ EmailChannel: XMPP message delivery failed, but email was sent successfully via API');
              // Don't throw - email was already sent successfully via API
            }
          }
        }
      }
    }

    // Clear the composer after successful send
    composerState.value.content = '';
    // Don't clear subject and recipients - keep them for follow-up emails
    attachedFiles.value = [];
    
    console.log('✅ EmailChannel: Email sent successfully');
    
  } catch (error) {
    console.error('❌ EmailChannel: Failed to send email:', error);
    // You could add a toast notification here to inform the user
  } finally {
    isSendingEmail.value = false;
  }
};

// Add this computed property after other computed properties
const getEmailSummary = computed(() => {
  if (!props.communication.messages.length) return { to: 'No recipients' };

  // Get the latest message
  const latestMessage = props.communication.messages[props.communication.messages.length - 1];
  const parseResult = parseEmailMessage(latestMessage);
  
  if (!parseResult.parsedContent) {
    return {
      from: 'You',
      to: 'Recipients',
      subject: 'No subject',
      cc: null,
      bcc: null
    };
  }
  
  const context = parseResult.parsedContent.context;
  const emailData = parseResult.emailData;
  
  if (!context) return { to: 'No recipients' };

  return {
    from: context.from_name || context.from,
    to: context.to || context.to_name || 'Recipients',
    subject: emailData?.subject || props.communication.title,
    cc: context.cc,
    bcc: context.bcc
  };
});

// Add this computed property after getEmailSummary
const composerDefaults = computed(() => {
  const latestMessage = props.communication.messages.length > 0 
    ? props.communication.messages[props.communication.messages.length - 1]
    : null;
  
  const parseResult = latestMessage ? parseEmailMessage(latestMessage) : null;
  
  // Get subject from latest email message
  const subject = parseResult?.emailData?.subject || 
                  props.communication.title || 
                  '';
  

  // Get TO recipient from last_sender in communication object (only one allowed)
  const toRecipient = (props.communication.availableComm as any)?.last_sender;
  const toRecipients = toRecipient ? [toRecipient as string] : [];
  
  // Get CC recipients from cc_recipients in communication object
  const ccRecipients = (props.communication.availableComm as any)?.cc_recipients 
    ? Object.values((props.communication.availableComm as any).cc_recipients as Record<string, string>)
    : [];
  
  return {
    to: toRecipients,
    cc: ccRecipients,
    subject: subject && subject.startsWith('Re: ') ? subject : `Re: ${subject || ''}`
  };
});

// Watch for communication changes and update composer defaults
watch(() => props.communication.id, () => {
  // Update composer with new defaults when communication changes
  const defaults = composerDefaults.value;
  composerState.value.to = defaults.to;
  composerState.value.cc = defaults.cc;
  composerState.value.subject = defaults.subject;
}, { immediate: true });

// Also update when messages change (new email received) - but avoid reactive dependencies
watch(() => props.communication.messages.length, () => {
  // Snapshot the current state to avoid reactivity during comparison
  const currentMinimized = composerState.value.minimizedFields;
  const currentSubject = composerState.value.subject.trim();
  const currentToLength = composerState.value.to.length;
  
  // Only update if fields are empty or minimized
  if (currentMinimized || (!currentSubject && currentToLength === 0)) {
    const defaults = composerDefaults.value;
    composerState.value.to = defaults.to;
    composerState.value.cc = defaults.cc;
    composerState.value.subject = defaults.subject;
  }
});

// Email validation function
const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email.trim());
};

// State for adding new CC recipients
const newCcRecipient = ref('');
const ccInputError = ref('');

// State for adding new To recipients
const newToRecipient = ref('');
const toInputError = ref('');

// Function to add CC recipient
const addCcRecipient = () => {
  const email = newCcRecipient.value.trim();
  if (!email) return;
  
  if (!isValidEmail(email)) {
    ccInputError.value = 'Please enter a valid email address';
    return;
  }
  
  if (composerState.value.cc.includes(email)) {
    ccInputError.value = 'This email is already added';
    return;
  }
  
  composerState.value.cc.push(email);
  newCcRecipient.value = '';
  ccInputError.value = '';
};

// Function to remove CC recipient
const removeCcRecipient = (index: number) => {
  composerState.value.cc.splice(index, 1);
};

// Function to add To recipient (only allows one recipient)
const addToRecipient = () => {
  const email = newToRecipient.value.trim();
  if (!email) return;
  
  if (!isValidEmail(email)) {
    toInputError.value = 'Please enter a valid email address';
    return;
  }
  
  if (composerState.value.to.includes(email)) {
    toInputError.value = 'This email is already added';
    return;
  }
  
  // Replace existing To recipient (only allow one)
  composerState.value.to = [email];
  newToRecipient.value = '';
  toInputError.value = '';
};

// Function to remove To recipient
const removeToRecipient = (index: number) => {
  composerState.value.to.splice(index, 1);
};

// Handle Enter key in CC input
const handleCcInputKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    event.preventDefault();
    addCcRecipient();
  }
};

// Clear CC input error when user starts typing and trim whitespace
const handleCcInputChange = () => {
  // Trim the input value
  newCcRecipient.value = newCcRecipient.value.trim();
  // Clear any existing error
  ccInputError.value = '';
};

// Handle Enter key in To input
const handleToInputKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    event.preventDefault();
    addToRecipient();
  }
};

// Clear To input error when user starts typing and trim whitespace
const handleToInputChange = () => {
  // Trim the input value
  newToRecipient.value = newToRecipient.value.trim();
  // Clear any existing error
  toInputError.value = '';
};

// Watch for field visibility changes and adjust height
watch(() => composerState.value.minimizedFields, (isMinimized) => {
  if (isMinimized) {
    // Fields are hidden - use base height (always pixels for minimized)
    composerHeight.value = baseComposerHeight;
  } else {
    // Fields are shown - determine appropriate height
    const isNewEmail = messages.value.length === 0;
    
    if (isNewEmail) {
      // New emails use percentage height
      composerHeight.value = newEmailComposerHeight;
    } else {
      // Existing emails: try to use saved height, fallback to default
      if (typeof localStorage !== 'undefined') {
        const savedHeight = localStorage.getItem('email-composer-height');
        if (savedHeight) {
          const height = parseInt(savedHeight, 10);
          if (height >= 200 && height <= 800) {
            composerHeight.value = `${height}px`;
            console.log('📏 EmailChannel: Using saved height on field expand:', height);
            return;
          }
        }
      }
      // Fallback to default expanded height
      composerHeight.value = expandedComposerHeight;
    }
  }
  
  console.log('📏 EmailChannel: Adjusted composer height to', composerHeight.value, 'based on field visibility');
});

// Watch for message count changes to switch from percentage to pixel heights
watch(() => messages.value.length, (newLength, oldLength) => {
  // If we just sent the first message (went from 0 to 1+ messages), switch to pixel-based height
  if (oldLength === 0 && newLength > 0 && !composerState.value.minimizedFields) {
    console.log('📏 EmailChannel: Switching from percentage to pixel height after first message');
    composerHeight.value = expandedComposerHeight; // Switch to pixel-based height
  }
});

// Retry connection function for error state
const retryConnection = async () => {
  try {
    // Clear the error state first
    store.clearCommError(props.communication.id);
    
    // Attempt to rejoin the XMPP room (this will set isConnecting=true)
    if (props.communication.roomJid) {
      await xmppService.joinRoom(props.communication);
      console.log('✅ EmailChannel: Successfully retried connection for:', props.communication.id);
    }
  } catch (error) {
    console.error('❌ EmailChannel: Failed to retry connection:', error);
    // The XMPP service will set the error state again if it fails
  }
};
</script>

<template>
  <div class="flex flex-col h-full bg-white">
    <!-- Email skeleton loading state -->
    <div 
      v-if="isLoading"
      class="flex-1 overflow-y-auto"
    >
      <div class="mx-auto">
        <div class="divide-y divide-gray-100">
          <!-- Create 3 skeleton email messages -->
          <div v-for="n in 3" :key="`skeleton-${n}`" class="py-4">
            <div class="flex items-start gap-2 px-4">
                             <!-- Avatar skeleton -->
               <BravoSkeleton 
                 borderRadius="16px"
                 size="32px" 
                 class="flex-shrink-0"
               />
              
              <div class="flex-1 min-w-0">
                <div class="flex justify-between items-start">
                  <div class="flex flex-col gap-1">
                    <!-- Sender name skeleton -->
                    <BravoSkeleton 
                      width="120px" 
                      height="16px" 
                      border-radius="4px"
                    />
                  </div>
                  <div class="flex items-center gap-2">
                    <!-- Timestamp skeleton -->
                    <BravoSkeleton 
                      width="80px" 
                      height="14px" 
                      border-radius="4px"
                    />
                  </div>
                </div>
                
                <div class="mt-1">
                  <!-- "to" line skeleton -->
                  <BravoSkeleton 
                    width="160px" 
                    height="12px" 
                    border-radius="4px"
                  />
                </div>
                
                <!-- Message preview skeleton -->
                <div class="mt-2">
                  <BravoSkeleton 
                    width="85%" 
                    height="14px" 
                    border-radius="4px" 
                    class="mb-1"
                  />
                  <BravoSkeleton 
                    width="60%" 
                    height="14px" 
                    border-radius="4px"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Content -->
    <template v-else>
      <!-- Connecting state -->
      <div 
        v-if="props.communication.isConnecting"
        class="flex-1 flex items-center justify-center bg-white"
      >
        <div class="text-center">
          <BravoSkeleton 
            borderRadius="50%" 
            size="50px" 
            class="animate-pulse mx-auto mb-4"
          />
          <div class="text-gray-600 text-lg font-medium">
            Connecting...
          </div>
                     <div class="mt-2 text-sm text-gray-500">
             Getting the latest messages
           </div>
        </div>
      </div>
      
      <!-- Error state -->
      <BravoZeroStateScreen
        v-else-if="props.communication.error"
        title="Connection Error"
        :message="props.communication.error.message"
        :showButton="true"
        buttonLabel="Retry Connection"
        :action-handler="() => retryConnection()"
        :imageSrc="CommsErrorSvg"
        image-alt="Connection Error"
        class="flex items-center justify-center h-full"
      />
      
      <!-- Empty state -->
      <BravoZeroStateScreen
        v-else-if="!messages.length"
        title="Send New Email"
        message="Start a new email thread by choosing recipient(s) and creating a message."
        :showButton="false"
        :imageSrc="EmailZeroStateSvg"
        image-alt="Email Messages"
        :action-handler="() => {}"
        class="flex items-center justify-center h-full"
      />
      
      <!-- Email thread -->
      <div ref="messagesContainer" class="flex-1 overflow-y-auto">
        <div class="mx-auto">
          <!-- Message thread -->
          <div class="divide-y divide-gray-100">
            <template v-for="message in messages" :key="message.id">
              <div 
                class="py-4"
                :class="{ 'cursor-pointer hover:bg-gray-50': !isMessageExpanded(message.id) }"
                @click="!isMessageExpanded(message.id) && toggleMessage(message.id)"
              >
                <!-- Message header -->
                <div 
                  class="flex items-start gap-3 px-4 cursor-pointer"
                  @click.stop="toggleMessage(message.id)"
                >
                  <!-- Avatar -->
                  <BravoAvatar
                    :key="`avatar-${message.id}-${message.sender.email}`"
                    :firstName="message.sender.name.split(' ')[0]"
                    :lastName="message.sender.name.split(' ')[1] || ''"
                    :backgroundColor="getAvatarColor(message.sender.email)"
                    :style="{ color: '#ffffff' }"
                    size="32"
                    class="flex-shrink-0"
                  />

                  <div class="flex-1 min-w-0">
                    <div class="flex justify-between items-start">
                      <div class="flex flex-col">
                        <span class="font-medium text-gray-900 leading-none">{{ message.sender.name }}</span>

                      </div>
                      <div class="flex items-center gap-2">
                        <BravoTimestamp 
                          :datetime="message.timestamp.toISOString()"
                          class="text-sm text-gray-500 whitespace-nowrap"
                        />
                      </div>
                    </div>
                    <div class="relative w-full">
                      <!-- Gmail-style: Show recipients when expanded, preview when minimized -->
                      <template v-if="isMessageExpanded(message.id)">
                        <div class="flex items-center space-x-1 max-w-full">
                          <span class="text-sm text-gray-500 mt-0.5 truncate inline-flex items-center max-w-[calc(100%-24px)]">
                            <span class="truncate">to {{ message.parsedContent?.context?.to || message.emailData?.to || message.parsedContent?.context?.to_name || 'Recipients' }}</span>
                          </span>
                          <button 
                            class="p-1 -m-1 text-gray-500 hover:text-gray-700 metadata-toggle flex-shrink-0"
                            @click.stop="toggleMetadata(message.id)"
                          >
                            <i class="pi pi-chevron-down text-xs"></i>
                          </button>
                        </div>
                      </template>
                      
                      <!-- Message preview when minimized -->
                      <template v-else>
                        <div class="text-gray-600 line-clamp-1 mt-0 pointer-events-none pr-4">
                          {{ htmlToPlainText(message.displayContent).substring(0, 150) }}
                        </div>
                      </template>
                      
                      <!-- Email metadata dropdown -->
                      <div 
                        v-if="showMetadata.has(message.id)"
                        class="absolute left-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-20 w-[500px] metadata-dropdown"
                        @click.stop
                      >
                        <div class="p-4 space-y-2 text-sm">
                          <div class="grid grid-cols-[80px_1fr] gap-2">
                            <span class="text-gray-500">from:</span>
                            <span>{{ message.sender.name }} &lt;{{ message.sender.email }}&gt;</span>
                            
                            <span class="text-gray-500">to:</span>
                            <span>{{ message.parsedContent?.context?.to || message.emailData?.to || message.parsedContent?.context?.to_name || 'Recipients' }}</span>
                            
                            <template v-if="message.emailData?.cc || message.parsedContent?.context?.cc">
                              <span class="text-gray-500">cc:</span>
                              <span>{{ message.emailData?.cc || message.parsedContent?.context?.cc }}</span>
                            </template>
                            
                            <span class="text-gray-500">date:</span>
                            <span>{{ formatDetailedDate(message.timestamp) }}</span>
                            
                            <span class="text-gray-500">subject:</span>
                            <span>{{ message.emailData?.subject || props.communication.title }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Expanded message -->
                <div 
                  v-if="isMessageExpanded(message.id)"
                  class="mt-2 pl-[60px] cursor-default"
                  @click.stop
                >
                  <!-- Parse content to separate new from quoted -->
                  <template v-if="separateEmailContent(message.displayContent).hasQuotedContent">
                    <!-- New content (always visible) -->
                    <div 
                      class="prose text-gray-800 w-full max-w-none break-words pr-4"
                      v-html="separateEmailContent(message.displayContent).newContent"
                    ></div>
                    
                    <!-- Quoted content toggle button (three dots) -->
                    <div class="mt-3 mb-2">
                      <button 
                        @click="toggleQuotedContent(message.id)"
                        class="quoted-content-toggle inline-flex items-center justify-center w-8 h-5 hover:bg-gray-100 rounded-full transition-colors"
                        v-tooltip="isQuotedContentExpanded(message.id) ? 'Hide previous messages' : 'Show previous messages'"
                      >
                        <i class="pi pi-ellipsis-h" style="color: var(--icon-color-primary);"></i>
                      </button>
                    </div>
                    
                    <!-- Quoted content (collapsible) -->
                    <div 
                      v-if="isQuotedContentExpanded(message.id)"
                      class="prose prose-sm text-gray-600 w-full max-w-none break-words pr-4 border-l-3 border-gray-300 pl-2"
                      v-html="separateEmailContent(message.displayContent).quotedContent"
                    ></div>
                  </template>
                  
                  <!-- If no quoted content, show original content -->
                  <template v-else>
                    <div 
                      class="prose text-gray-800 w-full max-w-none break-words pr-4"
                      v-html="message.displayContent"
                    ></div>
                  </template>

                  <!-- Attachments -->
                  <div 
                    v-if="message.hasAttachment && ((message as any).attachments || message.attachmentData)"
                    class="mt-4 space-y-2"
                  >
                    <div class="text-sm font-medium text-gray-500 mb-2">
                      Attachments ({{ (message as any).attachments?.length || 1 }})
                    </div>
                    
                    <!-- Multiple attachments (new grouping logic) -->
                    <template v-if="(message as any).attachments?.length">
                      <div 
                        v-for="(attachment, index) in (message as any).attachments"
                        :key="`attachment-${index}`"
                        class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg"
                      >
                        <i 
                          class="pi text-gray-400 text-xl"
                          :class="{
                            'pi-file-pdf': attachment.type === 'application/pdf',
                            'pi-image': attachment.type && attachment.type.startsWith('image/'),
                            'pi-file': attachment.type && !attachment.type.startsWith('image/') && attachment.type !== 'application/pdf'
                          }"
                        ></i>
                        <div class="flex-1 min-w-0">
                          <div class="font-medium truncate">{{ attachment.name }}</div>
                          <div class="text-sm text-gray-500">{{ formatFileSize(attachment.size) }}</div>
                        </div>
                        <a 
                          :href="attachment.url"
                          target="_blank"
                          rel="noopener noreferrer"
                          class="p-2 hover:bg-gray-100 rounded"
                          download
                        >
                          <i class="pi pi-download"></i>
                        </a>
                      </div>
                    </template>
                    
                    <!-- Single attachment (backward compatibility) -->
                    <template v-else-if="message.attachmentData">
                      <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                        <i 
                          class="pi text-gray-400 text-xl"
                          :class="{
                            'pi-file-pdf': message.attachmentData.type === 'application/pdf',
                            'pi-image': message.attachmentData.type && message.attachmentData.type.startsWith('image/'),
                            'pi-file': message.attachmentData.type && !message.attachmentData.type.startsWith('image/') && message.attachmentData.type !== 'application/pdf'
                          }"
                        ></i>
                        <div class="flex-1 min-w-0">
                          <div class="font-medium truncate">{{ message.attachmentData.name }}</div>
                          <div class="text-sm text-gray-500">{{ formatFileSize(message.attachmentData.size) }}</div>
                        </div>
                        <a 
                          :href="message.attachmentData.url"
                          target="_blank"
                          rel="noopener noreferrer"
                          class="p-2 hover:bg-gray-100 rounded"
                          download
                        >
                          <i class="pi pi-download"></i>
                        </a>
                      </div>
                    </template>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
      
      <!-- Email composer - simplified -->
      <div 
        class="border-t bg-white px-4 pt-4 pb-2 relative composer-container"
        :style="{ height: composerHeight, boxShadow: '0 -2px 10px -1px rgba(0, 0, 0, 0.05)' }"
      >
        <!-- Composer resize handle -->
        <div
          class="composer-resize-handle"
          :class="{ resizing: isResizingComposer }"
          @mousedown="startComposerResize"
          data-testid="composer-resize-handle"
        >
          <div class="composer-resize-grabber"></div>
        </div>
        <!-- Attached files -->
        <div v-if="attachedFiles.length" class="mb-4">
          <div class="text-sm font-medium text-gray-500 mb-2">
            Attached Files ({{ attachedFiles.length }})
          </div>
          <div class="space-y-2">
            <div 
              v-for="file in attachedFiles"
              :key="file.id"
              class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg"
            >
              <!-- File preview/icon -->
              <div class="w-12 h-12 flex-shrink-0 rounded overflow-hidden bg-gray-200 flex items-center justify-center">
                <img 
                  v-if="file.preview" 
                  :src="file.preview" 
                  :alt="file.name"
                  class="w-full h-full object-cover"
                />
                <i 
                  v-else
                  class="text-gray-400 text-xl"
                  :class="{
                    'pi pi-file-pdf': file.type === 'application/pdf',
                    'pi pi-file-word': file.type && (file.type.includes('word') || file.type.includes('document')),
                    'pi pi-file-excel': file.type && (file.type.includes('sheet') || file.type.includes('excel')),
                    'pi pi-image': file.type && file.type.startsWith('image/'),
                    'pi pi-video': file.type && file.type.startsWith('video/'),
                    'pi pi-file': file.type && !file.type.startsWith('image/') && !file.type.startsWith('video/') && !file.type.includes('pdf') && !file.type.includes('word') && !file.type.includes('sheet')
                  }"
                ></i>
              </div>
              
              <!-- File info -->
              <div class="flex-1 min-w-0">
                <div class="font-medium truncate" :title="file.name">{{ file.name }}</div>
                <div class="text-sm text-gray-500">
                  {{ formatFileSize(file.size) }} • {{ getFileTypeLabel(file.type) }}
                </div>
              </div>
              
              <!-- Remove button -->
              <button 
                class="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-colors"
                @click="attachedFiles = attachedFiles.filter((f: any) => f.id !== file.id)"
                title="Remove file"
              >
                <i class="pi pi-times"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- File upload progress -->
        <div v-if="uploadingFiles.length" class="mb-4 space-y-2">
          <FileUploadProgress
            v-for="file in uploadingFiles"
            :key="file.id"
            :file-name="file.name"
            :size="file.size"
            :progress="file.progress"
          />
        </div>

        <!-- Email fields -->
        <div v-if="!composerState.minimizedFields" class="mb-4 border-t border-gray-200">
          <!-- TO field (editable chips) -->
          <div class="flex items-start min-h-[38px] border-b border-gray-200">
            <BravoLabel 
              text="To" 
              class="w-16 flex-shrink-0 py-2" 
            />
            <div class="flex-1 ml-6 py-2">
              <!-- Combined chips and input container -->
              <div class="flex flex-wrap items-center gap-1">
                <!-- Existing To chips -->
                <BravoChip 
                  v-for="(email, index) in composerState.to" 
                  :key="`to-${email}`"
                  :label="email"
                  removable
                  @remove="removeToRecipient(index)"
                  class="px-2 py-1 email-chip"
                />
                
                <!-- Inline To input (only show if no recipient exists) -->
                <div v-if="composerState.to.length === 0" class="flex items-center flex-1 min-w-0">
                  <InputText
                    v-model="newToRecipient"
                    placeholder="Add recipient"
                    class="flex-1 min-w-[80px] email-field-input"
                    :class="{ 'p-invalid': toInputError }"
                    @keydown="handleToInputKeydown"
                    @input="handleToInputChange"
                    @blur="addToRecipient"
                    v-tooltip.top="toInputError || ''"
                  />
                  <!-- Error indicator icon -->
                  <i 
                    v-if="toInputError" 
                    class="pi pi-exclamation-triangle text-red-500 text-sm ml-1"
                    v-tooltip.top="toInputError"
                  ></i>
                </div>
              </div>
            </div>
          </div>

          <!-- CC field (editable chips) -->
          <div class="flex items-start min-h-[38px] border-b border-gray-200">
            <BravoLabel 
              text="CC" 
              class="w-16 flex-shrink-0 py-2" 
            />
            <div class="flex-1 ml-6 py-2">
              <!-- Combined chips and input container -->
              <div class="flex flex-wrap items-center gap-1">
                <!-- Existing CC chips -->
                <BravoChip 
                  v-for="(email, index) in composerState.cc" 
                  :key="`cc-${email}`"
                  :label="email"
                  removable
                  @remove="removeCcRecipient(index)"
                  class="px-2 py-1 email-chip"
                />
                
                <!-- Inline CC input -->
                <div class="flex items-center flex-1 min-w-0">
                  <InputText
                    v-model="newCcRecipient"
                    :placeholder="composerState.cc.length === 0 ? 'Add CC recipients' : ''"
                    class="flex-1 min-w-[80px] email-field-input"
                    :class="{ 'p-invalid': ccInputError }"
                    @keydown="handleCcInputKeydown"
                    @input="handleCcInputChange"
                    @blur="addCcRecipient"
                    v-tooltip.top="ccInputError || ''"
                  />
                  <!-- Error indicator icon -->
                  <i 
                    v-if="ccInputError" 
                    class="pi pi-exclamation-triangle text-red-500 text-sm ml-1"
                    v-tooltip.top="ccInputError"
                  ></i>
                </div>
              </div>
            </div>
          </div>

          <!-- Subject field -->
          <div class="flex items-center h-[38px] border-b border-gray-200">
            <BravoLabel 
              text="Subject" 
              class="w-16 flex-shrink-0" 
            />
            <div class="flex-1 ml-6">
              <input 
                type="text" 
                v-model="composerState.subject"
                class="w-full border-0 bg-transparent px-0 py-1 text-gray-900 placeholder-gray-400 focus:ring-0 focus:outline-none bravo-body-styling"
                placeholder="Subject"
              />
            </div>
          </div>
        </div>
        
        <!-- Editor and content area -->
        <div class="bg-white relative flex-1 flex flex-col composer-content">
          <Editor
            v-model="composerState.content"
            :editorStyle="{ height: '100%', 'font-family': 'inherit' }"
            :disabled="isGeneratingReply || isReformatting || isCleaningUp"
            ref="editorRef"
            :class="{ 'toolbar-visible': showEditorToolbar }"
            class="email-editor flex-1"
            placeholder="Compose your email..."
          >
            <template v-slot:toolbar>
              <span class="ql-formats">
                <select class="ql-header" v-tooltip.bottom="'Headers'">
                  <option selected></option>
                  <option value="1"></option>
                  <option value="2"></option>
                  <option value="3"></option>
                </select>
              </span>
              <span class="ql-formats">
                <button class="ql-bold" v-tooltip.bottom="'Bold'"></button>
                <button class="ql-italic" v-tooltip.bottom="'Italic'"></button>
                <button class="ql-underline" v-tooltip.bottom="'Underline'"></button>
                <button class="ql-strike" v-tooltip.bottom="'Strikethrough'"></button>
              </span>
              <span class="ql-formats">
                <select class="ql-color" v-tooltip.bottom="'Text Color'"></select>
                <select class="ql-background" v-tooltip.bottom="'Background Color'"></select>
              </span>
              <span class="ql-formats">
                <button class="ql-list" value="ordered" v-tooltip.bottom="'Numbered List'"></button>
                <button class="ql-list" value="bullet" v-tooltip.bottom="'Bullet List'"></button>
              </span>
              <span class="ql-formats">
                <select class="ql-align" v-tooltip.bottom="'Text Alignment'">
                  <option selected></option>
                  <option value="center"></option>
                  <option value="right"></option>
                  <option value="justify"></option>
                </select>
              </span>
              <span class="ql-formats">
                <button class="ql-link" v-tooltip.bottom="'Insert Link'"></button>
                <button class="ql-image" v-tooltip.bottom="'Insert Image'"></button>
                <button class="ql-video" v-tooltip.bottom="'Insert Video'"></button>
              </span>
            </template>
          </Editor>
          
          <!-- Loading overlay for reply generation -->
          <div 
            v-if="isGeneratingReply || isReformatting || isCleaningUp"
            class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10"
          >
            <div class="text-center">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <div class="mt-2 text-gray-600 text-sm">
                {{ isGeneratingReply ? 'Generating reply...' : isReformatting ? 'Reformatting response...' : 'Cleaning up response...' }}
              </div>
            </div>
          </div>
        </div>
        
        <!-- Sticky bottom toolbar -->
        <div class="composer-toolbar bg-white border-t border-gray-200 px-4 py-2 flex justify-between items-center">
          <div class="flex items-center space-x-2">
            <BravoButton
              icon="pi pi-align-left"
              text
              severity="secondary"
              @click="showEditorToolbar = !showEditorToolbar"
              v-tooltip.bottom="'Formatting Options'"
            />
            <BravoButton
              icon="pi pi-paperclip"
              text
              severity="secondary"
              @click="($refs.fileInput as HTMLInputElement)?.click()"
              v-tooltip.bottom="'Attach File'"
            />
            <Dropdown
              v-model="selectedTemplateValue"
              :options="templateOptions"
              optionLabel="label"
              optionValue="value"
              placeholder=""
              class="quick-reply-dropdown"
              @change="applyTemplate"
              :filter="false"
              :showClear="false"
              :loading="loadingCannedResponses"
              :disabled="false"
              v-tooltip.bottom="'Quick Replies'"
            >
              <template #value>
                <div class="flex items-center">
                  <i class="pi pi-comments"></i>
                </div>
              </template>
              <template #option="slotProps">
                <div class="flex items-center">
                  <i class="pi pi-file mr-2 text-gray-400"></i>
                  <span>{{ slotProps.option.label }}</span>
                </div>
              </template>
            </Dropdown>
            <Dropdown
              v-model="selectedAiAction"
              :options="aiActions"
              optionLabel="label"
              optionValue="value"
              placeholder=""
              class="ai-assistant-dropdown"
              @change="handleAiAction"
              :filter="false"
              :showClear="false"
              v-tooltip.bottom="'AI Assistant'"
            >
              <template #value>
                <div class="flex items-center">
                  <i class="pi pi-sparkles"></i>
                </div>
              </template>
              <template #option="slotProps">
                <div class="flex items-center">
                  <i :class="slotProps.option.icon" class="mr-2 text-gray-400"></i>
                  <span>{{ slotProps.option.label }}</span>
                </div>
              </template>
            </Dropdown>
            <BravoButton
              :label="composerState.minimizedFields ? 'Show Fields' : 'Hide Fields'"
              text
              severity="secondary"
              size="small"
              @click="composerState.minimizedFields = !composerState.minimizedFields"
              v-tooltip.bottom="'Toggle To and Subject fields'"
            />
          </div>
          <BravoButton
            label="Send"
            size="small"
            severity="primary"
            :disabled="(!composerState.content.trim() && attachedFiles.length === 0) || isSendingEmail"
            :loading="isSendingEmail"
            @click="sendEmail"
          />
        </div>
        
        <input
          ref="fileInput"
          type="file"
          multiple
          class="hidden"
          @change="handleFileUpload"
        >
        
        <!-- Link Dialog -->
        <Dialog
          v-model:visible="showLinkDialog"
          modal
          header="Insert Link"
          :style="{ width: '400px' }"
          :closable="true"
        >
          <div class="space-y-4 p-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Text to Link
              </label>
              <div class="text-gray-600 bg-gray-50 p-2 rounded">
                {{ selectedText }}
              </div>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                URL
              </label>
              <input
                type="url"
                v-model="linkUrl"
                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="https://"
                @keydown.enter="insertLink"
              >
            </div>
          </div>
          <template #footer>
            <div class="flex justify-end gap-2">
              <button
                class="px-4 py-2 text-gray-700 bg-gray-100 rounded hover:bg-gray-200"
                @click="showLinkDialog = false"
              >
                Cancel
              </button>
              <button
                class="px-4 py-2 text-white bg-blue-600 rounded hover:bg-blue-700"
                @click="insertLink"
                :disabled="!linkUrl.trim()"
              >
                Insert
              </button>
            </div>
          </template>
        </Dialog>

        <!-- Summary Dialog -->
        <Dialog
          v-model:visible="showSummaryDialog"
          modal
          header="Conversation Summary"
          :style="{ width: '600px' }"
          :closable="true"
        >
          <div class="p-4">
            <div v-if="isSummarizing" class="flex flex-col items-center justify-center py-8">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <div class="mt-4 text-gray-600">
                Generating summary...
              </div>
            </div>
            <div 
              v-else
              class="prose max-w-none"
              v-html="summaryContent"
            ></div>
          </div>
          <template #footer>
            <div class="flex justify-end">
              <button
                class="px-4 py-2 text-gray-700 bg-gray-100 rounded hover:bg-gray-200"
                @click="showSummaryDialog = false"
              >
                Close
              </button>
            </div>
          </template>
        </Dialog>
      </div>
    </template>
  </div>
</template>

<style scoped>
.email-metadata-dropdown {
  min-width: 500px;
  max-width: calc(100vw - 2rem);
}

.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

:deep(.prose) {
  max-width: none;
  width: 100%;
  overflow-wrap: break-word;
  word-break: break-word;
  font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
  font-size: 1rem;
  line-height: 1.5;
}

:deep(.prose p) {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

:deep(.prose p:first-child) {
  margin-top: 0;
}

:deep(.prose p:last-child) {
  margin-bottom: 0;
}

/* Ensure proper line height for readable content */
:deep(.prose p:empty) {
  margin: 0.5em 0;
  height: 1em;
  display: block;
}

/* Hide empty paragraphs at the beginning - these are usually editor artifacts */
:deep(.prose p:first-child:empty) {
  display: none;
  margin: 0;
  height: 0;
}

/* Target only plain text elements for font standardization, preserve rich HTML formatting */
:deep(.prose p:not([style*="font-size"]):not([style*="font-family"])),
:deep(.prose div:not([class]):not([style]):not([id])) {
  font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
  font-size: 1rem;
  line-height: 1.5;
}

/* Ensure plain text spans inherit proper font (but preserve styled spans) */
:deep(.prose span:not([style*="font-size"]):not([style*="color"]):not([style*="font-family"]):not([class]):not([id])) {
  font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
}

:deep(.prose img) {
  max-width: 100%;
  height: auto;
}

:deep(.prose a) {
  color: #2563eb;
  text-decoration: underline;
}

:deep(.prose ul),
:deep(.prose ol) {
  padding-left: 1.5em;
  margin: 0.25em 0;
  list-style-position: outside;
}

:deep(.prose ul) {
  list-style-type: disc;
}

:deep(.prose ol) {
  list-style-type: decimal;
}

:deep(.prose ul ul) {
  list-style-type: circle;
  margin: 0;
}

:deep(.prose ol ol) {
  list-style-type: lower-alpha;
  margin: 0;
}

:deep(.prose li) {
  margin: 0.125em 0;
  padding-left: 0.25em;
}

/* Remove excessive spacing from common email elements - but preserve intentional line breaks */
:deep(.prose br + br + br) {
  display: none; /* Hide only when there are 3+ consecutive <br> tags */
}

:deep(.prose div:empty) {
  display: none;
}

/* Don't hide empty paragraphs - they might be intentional line breaks */

/* Ensure single and double line breaks are preserved */
:deep(.prose br) {
  display: block;
  content: "";
  margin: 0.125em 0;
}

:deep(.prose blockquote:not([style*="font-family"])) {
  border-left: 4px solid #e5e7eb;
  padding-left: 1em;
  color: #4b5563;
  font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
  font-size: 1rem;
}

/* Ensure headers in email content use consistent Inter font while preserving semantic sizing */
:deep(.prose h1:not([style*="font-family"])),
:deep(.prose h2:not([style*="font-family"])),
:deep(.prose h3:not([style*="font-family"])),
:deep(.prose h4:not([style*="font-family"])),
:deep(.prose h5:not([style*="font-family"])),
:deep(.prose h6:not([style*="font-family"])) {
  font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
}

/* Ensure strong/bold and emphasis maintain consistent font while preserving their styling */
:deep(.prose strong:not([style*="font-family"])),
:deep(.prose b:not([style*="font-family"])),
:deep(.prose em:not([style*="font-family"])),
:deep(.prose i:not([style*="font-family"])) {
  font-family: inherit;
}

/* Preserve table formatting and rich HTML email layouts */
:deep(.prose table),
:deep(.prose td),
:deep(.prose th),
:deep(.prose tr) {
  /* Don't override table styling - let rich HTML emails maintain their layout */
  font-family: inherit;
  font-size: inherit;
}

/* Preserve any elements with background colors, borders, or custom styling */
:deep(.prose [style*="background"]),
:deep(.prose [style*="border"]),
:deep(.prose [style*="padding"]),
:deep(.prose [style*="margin"]),
:deep(.prose [bgcolor]),
:deep(.prose [color]) {
  /* These are likely intentionally styled elements - preserve them */
  font-family: inherit;
  font-size: inherit;
}

:deep(.p-dropdown) {
  @apply border border-gray-200 rounded-lg shadow-lg;
  min-width: 200px;
}

:deep(.p-dropdown-item) {
  @apply px-4 py-2;
}

:deep(.p-dialog-header) {
  @apply py-4 px-6 border-b border-gray-200;
}

:deep(.p-dialog-content) {
  @apply p-0;
}

:deep(.p-dialog-footer) {
  @apply p-4 border-t border-gray-200;
}

:deep(.email-editor) {
  .ql-toolbar {
    display: none;
    border-bottom: 1px solid #e5e7eb;
  }

  &.toolbar-visible .ql-toolbar {
    display: block;
   }

  .ql-container {
    border: none !important;
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
  }

  .ql-editor {
    padding: 0.5rem;
    font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    color: var(--text-color-primary) !important;
    border: none !important;
    outline: none !important;
    height: 100% !important;
    min-height: 100% !important;
    overflow-y: auto !important;
  }

  /* Ensure placeholder text is visible */
  .ql-editor.ql-blank::before {
    color: var(--text-color-secondary) !important;
    font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif !important;
    font-size: 14px !important;
    font-style: normal !important;
    font-weight: normal !important;
    content: attr(data-placeholder) !important;
    pointer-events: none !important;
    position: absolute !important;
    left: 8px !important;
    top: 8px !important;
  }

  /* Alternative approach - style the placeholder directly */
  .ql-editor::before {
    color: var(--text-color-secondary) !important;
    font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif !important;
    font-size: 14px !important;
    font-style: normal !important;
  }
  
  /* Remove all borders from the editor wrapper */
  border: none !important;
  outline: none !important;
}

/* Quoted content styling */
.border-l-3 {
  border-left-width: 1px;
}

/* Reset deeply nested indentation to prevent content from getting too narrow */
.border-l-3 .border-l-3 .border-l-3 {
  margin-left: 0 !important;
  padding-left: 4px !important; /* Reduced padding for deep nesting */
}

.border-l-3 .border-l-3 .border-l-3 .border-l-3 {
  margin-left: 0 !important;
  padding-left: 4px !important; /* Even more reduced for very deep nesting */
}

/* Stop indentation after 8 levels deep - no more border or padding */
.border-l-3 .border-l-3 .border-l-3 .border-l-3 .border-l-3 .border-l-3 .border-l-3 .border-l-3 {
  border-left: none !important;
  padding-left: 0 !important;
  margin-left: 0 !important;
}

/* Also remove indentation for any deeper nesting */
.border-l-3 .border-l-3 .border-l-3 .border-l-3 .border-l-3 .border-l-3 .border-l-3 .border-l-3 .border-l-3 {
  border-left: none !important;
  padding-left: 0 !important;
  margin-left: 0 !important;
}

.quoted-content-toggle {
  transition: all 0.2s ease;
}

.quoted-content-toggle:hover {
  background-color: #f9fafb;
}

/* Quoted text styling */
:deep(.prose-sm) {
  font-size: 1rem;
  line-height: 1.5;
  font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
}

/* Target only plain text elements in quoted content, preserve rich formatting */
:deep(.prose-sm p:not([style*="font-size"]):not([style*="font-family"])),
:deep(.prose-sm div:not([class]):not([style]):not([id])) {
  font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
  font-size: 1rem;
  line-height: 1.5;
}

:deep(.prose-sm span:not([style*="font-size"]):not([style*="color"]):not([style*="font-family"]):not([class]):not([id])) {
  font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
}

:deep(.prose-sm p) {
  margin-top: 0.25em;
  margin-bottom: 0.25em;
}

:deep(.prose-sm p:first-child) {
  margin-top: 0;
}

:deep(.prose-sm p:last-child) {
  margin-bottom: 0;
}

/* Composer container styling */
.composer-container {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Composer content area - allows scrolling */
.composer-content {
  overflow: hidden;
}

/* Composer toolbar - stays at bottom */
.composer-toolbar {
  flex-shrink: 0;
  margin-left: -1rem;
  margin-right: -1rem;
  margin-bottom: -0.5rem;
}

/* Composer resize handle */
.composer-resize-handle {
  position: absolute;
  top: -8px;
  left: 0;
  right: 0;
  height: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: row-resize;
  z-index: 10;
}

.composer-resize-grabber {
  width: 60px;
  height: 4px;
  border-radius: 2px;
  background-color: #ddd;
  position: relative;
  transition: background-color 0.2s;
}

.composer-resize-handle:hover .composer-resize-grabber,
.composer-resize-handle.resizing .composer-resize-grabber {
  background-color: var(--primary-color, #3b82f6);
}

/* Add style for the body while resizing composer */
:global(body.resizing-composer) {
  cursor: row-resize !important;
  user-select: none;
}

/* Quick Reply Dropdown Styling */
:deep(.quick-reply-dropdown) {
  border: none !important;
  background: transparent !important;
  box-shadow: none !important;
  padding: 0 !important;
  width: auto !important;
  min-width: auto !important;
}

/* Target the specific dropdown element */
:deep(.quick-reply-dropdown .p-select-dropdown) {
  width: 0 !important;
}

:deep(.quick-reply-dropdown .p-dropdown-trigger) {
  background: transparent !important;
  border: none !important;
  color: #6b7280 !important;
  padding: 4px !important;
  width: auto !important;
  display: none !important; /* Hide the caret icon */
}

/* Additional selectors to ensure caret is hidden */
:deep(.quick-reply-dropdown .p-dropdown-trigger-icon) {
  display: none !important;
}

:deep(.quick-reply-dropdown .p-icon) {
  display: none !important;
}

:deep(.quick-reply-dropdown .pi-chevron-down) {
  display: none !important;
}

:deep(.quick-reply-dropdown .p-dropdown-label) {
  background: transparent !important;
  border: none !important;
  padding: 4px !important;
  color: #374151 !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  width: auto !important;
  min-width: auto !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

:deep(.quick-reply-dropdown:not(.p-disabled):hover) {
  background-color: #f9fafb !important;
  border-radius: 6px !important;
}

:deep(.quick-reply-dropdown:not(.p-disabled):hover .p-dropdown-label) {
  background-color: transparent !important;
}

:deep(.quick-reply-dropdown:not(.p-disabled):hover .p-dropdown-trigger) {
  background-color: transparent !important;
}

:deep(.quick-reply-dropdown.p-focus) {
  box-shadow: none !important;
  border: none !important;
  background-color: #f3f4f6 !important;
  border-radius: 6px !important;
}

/* AI Assistant Dropdown Styling - Same as Quick Reply */
:deep(.ai-assistant-dropdown) {
  border: none !important;
  background: transparent !important;
  box-shadow: none !important;
  padding: 0 !important;
  width: auto !important;
  min-width: auto !important;
}

:deep(.ai-assistant-dropdown .p-select-dropdown) {
  width: 0 !important;
}

:deep(.ai-assistant-dropdown .p-dropdown-trigger) {
  background: transparent !important;
  border: none !important;
  color: #6b7280 !important;
  padding: 4px !important;
  width: auto !important;
  display: none !important;
}

:deep(.ai-assistant-dropdown .p-dropdown-trigger-icon) {
  display: none !important;
}

:deep(.ai-assistant-dropdown .p-icon) {
  display: none !important;
}

:deep(.ai-assistant-dropdown .pi-chevron-down) {
  display: none !important;
}

:deep(.ai-assistant-dropdown .p-dropdown-label) {
  background: transparent !important;
  border: none !important;
  padding: 4px !important;
  color: #374151 !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  width: auto !important;
  min-width: auto !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

:deep(.ai-assistant-dropdown:not(.p-disabled):hover) {
  background-color: #f9fafb !important;
  border-radius: 6px !important;
}

:deep(.ai-assistant-dropdown:not(.p-disabled):hover .p-dropdown-label) {
  background-color: transparent !important;
}

:deep(.ai-assistant-dropdown:not(.p-disabled):hover .p-dropdown-trigger) {
  background-color: transparent !important;
}

:deep(.ai-assistant-dropdown.p-focus) {
  box-shadow: none !important;
  border: none !important;
  background-color: #f3f4f6 !important;
  border-radius: 6px !important;
}

/* Email field input styling */
:deep(.email-field-input) {
  border: none !important;
  background: transparent !important;
  box-shadow: none !important;
  padding: 4px 0 !important;
  font-size: 14px !important;
  color: #374151 !important;
}

:deep(.email-field-input:focus) {
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
  background: transparent !important;
}

:deep(.email-field-input::placeholder) {
  color: var(--text-color-secondary) !important;
  font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif !important;
  font-size: 14px !important;
  font-weight: 400 !important;
}

/* Subject field styling to match BravoBody */
.bravo-body-styling {
  font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif !important;
  font-size: 1rem !important;
  color: var(--text-color-primary, #374151) !important;
}

.bravo-body-styling::placeholder {
  color: var(--text-color-secondary) !important;
  font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif !important;
  font-size: 14px !important;
  font-weight: 400 !important;
}

/* To field placeholder text styling */
.placeholder-text {
  color: var(--text-color-secondary) !important;
  font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif !important;
  font-size: 14px !important;
  font-weight: 400 !important;
}

/* Email chip styling */
:deep(.email-chip) {
  font-size: 14px !important;
  background: transparent !important;
  border: 1px solid var(--surface-500) !important;
  color: var(--text-color-primary) !important;
  border-radius: 6px !important;
  height: 24px !important;
  transition: all 0.2s ease !important;
  cursor: default !important;
}

:deep(.email-chip:hover) {
  background: var(--surface-50) !important;
  border-color: var(--surface-600) !important;
}
</style>