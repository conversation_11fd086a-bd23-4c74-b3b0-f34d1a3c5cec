import type { InteractionEvent } from '@/composables/services/useInteractionEventsAPI'
import { useCasesStore } from '@/stores/cases'
import type { FileItem } from '@/composables/services/useFilesAPI'

// Extended interface for interaction events with additional properties
export interface ExtendedInteractionEvent extends InteractionEvent {
  log_id?: string
  type?: {
    id: string
    label: string
  }
  refs?: {
    user?: {
      user_avatar?: string
      users_full_name?: string
      users_nickname?: string
      first_name?: string
      last_name?: string
    }
  }
}

// Helper function to extract field name from bold text in HTML
export const extractFieldNameFromHtml = (htmlString: string): string => {
  // Regular expression to match the first bold text: <b>text</b> or <strong>text</strong>
  const boldRegex = /<(?:b|strong)>(.*?)<\/(?:b|strong)>/i
  const match = htmlString.match(boldRegex)
  return match ? match[1] : 'field'
}

// Helper function to extract file name from content
export const extractFileNameFromContent = (content: string): string => {
  // Look for file names in various formats
  // Could be "Uploaded file: filename.pdf" or just "filename.pdf"
  // Updated to handle spaces, parentheses, and other common filename characters
  const fileNameRegex = /([a-zA-Z0-9_\-\s\(\)\.]+\.[a-zA-Z0-9]{2,6})/
  const match = content.match(fileNameRegex)
  const fileName = match ? match[1] : ''
  
  return fileName
}

// Interface for structured content that includes BravoTag data and file data
export interface ContentPart {
  type: 'text' | 'tag' | 'file'
  value: string
  tagState?: string
  fileData?: FileItem
}

export interface StructuredContent {
  isStructured: true
  parts: ContentPart[]
}

// Helper function to format resolved content with structured data
export const formatResolvedContent = (originalContent: string): StructuredContent => {
  const parts: ContentPart[] = [
    { type: 'text', value: 'Status changed to ' },
    { type: 'tag', value: 'Resolved', tagState: 'resolved' }
  ]
  
  // Add line break and original content if it exists
  if (originalContent && originalContent !== 'No content available') {
    parts.push({ type: 'text', value: '<br><br>' + originalContent })
  }
  
  return {
    isStructured: true,
    parts
  }
}

// Helper function to format reopened content with structured data
export const formatReopenedContent = (): StructuredContent => {
  return {
    isStructured: true,
    parts: [
      { type: 'text', value: 'Status set to ' },
      { type: 'tag', value: 'Ready', tagState: 'ready' }
    ]
  }
}

// Helper function to format status change content with structured data
export const formatStatusChangeContent = (content: string): StructuredContent | string => {
  // Parse "Status changed from Waiting to Ready"
  const statusChangeRegex = /Status changed from (\w+) to (\w+)/i
  const match = content.match(statusChangeRegex)
  
  if (match) {
    const fromStatus = match[1]
    const toStatus = match[2]
    
    return {
      isStructured: true,
      parts: [
        { type: 'text', value: 'Status changed from ' },
        { type: 'tag', value: fromStatus, tagState: fromStatus.toLowerCase() },
        { type: 'text', value: ' to ' },
        { type: 'tag', value: toStatus, tagState: toStatus.toLowerCase() }
      ]
    }
  }
  
  return content
}

// Helper function to format file content with structured data
export const formatFileContent = (content: string, issueId?: string): StructuredContent | string => {
  const fileName = extractFileNameFromContent(content)
  
  if (!fileName || !issueId) {
    return content
  }

  // Get files from the store
  const casesStore = useCasesStore()
  
  // Try to find the file in the store
  const fileItem = casesStore.files.find(file => file.name === fileName)

  if (fileItem) {
    return {
      isStructured: true,
      parts: [
        { 
          type: 'file', 
          value: fileName, 
          fileData: fileItem 
        }
      ]
    }
  }

  // If file not found in store, return content with file name highlighted
  // This could happen if files haven't been loaded yet
  return {
    isStructured: true,
    parts: [
      { type: 'text', value: `📎 ${fileName}` }
    ]
  }
}

// Helper function to get user's full name
const getUserFullName = (user: any, issueId?: string): string => {
  // Check if user data indicates automation (all fields are empty/null)
  const isAutomation = user && (
    (!user.first_name || user.first_name === '') &&
    (!user.last_name || user.last_name === '') &&
    (!user.users_full_name || user.users_full_name === null) &&
    (!user.users_id || user.users_id === '') &&
    (!user.users_nickname || user.users_nickname === '')
  )
  
  if (isAutomation) {
    return 'Automation'
  }
  
  // First try to get from event user data
  const firstName = user?.first_name || ''
  const lastName = user?.last_name || ''
  const fullName = `${firstName} ${lastName}`.trim()
  const eventUserName = fullName || user?.users_full_name
  
  if (eventUserName && eventUserName !== 'Unknown User') {
    return eventUserName
  }
  
  // Fallback to case owner information from the store
  if (issueId) {
    const casesStore = useCasesStore()
    const currentIssue = casesStore.currentIssue
    
    if (currentIssue && currentIssue.id === issueId) {
      // Try case owner user name
      if ((currentIssue as any).c__owner_user_name) {
        return (currentIssue as any).c__owner_user_name
      }
      
      // Try member user data
      if (currentIssue.memberUser) {
        const memberFirstName = currentIssue.memberUser.first_name || ''
        const memberLastName = currentIssue.memberUser.last_name || ''
        const memberFullName = `${memberFirstName} ${memberLastName}`.trim()
        if (memberFullName) {
          return memberFullName
        }
      }
    }
  }
  
  return 'Unknown User'
}

// Helper function to format event titles based on event type
export const formatEventTitle = (event: ExtendedInteractionEvent, issueId?: string): string => {
  const eventTypeId = event.type?.id || event.category?.id
  const fullName = getUserFullName(event.refs?.user, issueId)
  
  switch (eventTypeId) {
    case 'fields-updated':
      const fieldName = extractFieldNameFromHtml(event.body?.data || '')
      return `${fullName} updated ${fieldName}`
    
    case 'ready':
      return `${fullName} changed Status to Ready`
    
    case 'waiting':
      return `${fullName} changed Status to Waiting`
    
    case 'resolved':
      return `${fullName} changed Status to Resolved`
    
    case 'reopened':
      return `${fullName} reopened the Case`
    
    case 'file-added':
      return `${fullName} uploaded a file`
    
    case 'case-deleted':
      return `${fullName} deleted the Case`
    
    case 'case-created':
      return `${fullName} created the Case`
    
    case 'note-added':
      return `${fullName} added a Note`

    case 'customer-changed':
      return `${fullName} changed the Location`

    case 'assigned-to-team':
      return `${fullName} assigned a new Owner Team`

    case 'reassigned':
      return `${fullName} assigned a new Owner User`

    case 'case-escalated':
      return `${fullName} escalated the Case`

    case 'case-de-escalated':
      return `${fullName} de-escalated the Case`

    case 'form-completed':
      return `${fullName} completed a Form`

    case 'survey-sent':
      return `${fullName} sent a Customer Survey`

    default:
      // Default to the original label
      return event.type?.label || event.category?.label || 'Unknown Event'
  }
}

// Helper function to format event content based on event type
export const formatEventContent = (event: ExtendedInteractionEvent, issueId?: string): string | StructuredContent => {
  const eventTypeId = event.type?.id || event.category?.id
  const originalContent = event.body?.data || 'No content available'
  
  switch (eventTypeId) {
    case 'ready':
    case 'waiting':
      return formatStatusChangeContent(originalContent)
    
    case 'file-added':
      return formatFileContent(originalContent, issueId)
    
    case 'case-deleted':
      return '' // Return empty content for case deletion events
    
    case 'case-created':
      // Translate specific text for case creation
      if (originalContent === 'Created from Boomtown') {
        return 'Created manually'
      }
      return originalContent
    
    case 'reopened':
      return formatReopenedContent()
    
    case 'note-added':
      return originalContent
    
    case 'resolved':
      return formatResolvedContent(originalContent)
    
    case 'survey-sent':
      // Replace "Customer Acceptance" with "Customer Survey" in the content
      return originalContent.replace(/Customer Acceptance/g, 'Customer Survey')
    
    default:
      return originalContent
  }
} 