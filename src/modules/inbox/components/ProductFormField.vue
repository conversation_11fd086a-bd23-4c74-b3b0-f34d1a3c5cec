<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import BravoFormField from '@/modules/knowledge/components/BravoFormField.vue'
import { useMemberStore } from '@/stores/member'
import type { Issue } from '@/services/IssuesAPI'

interface Props {
  label: string
  fieldName: string
  value: any
  displayValue: string | string[]
  issue?: Issue | null
  isEditing?: boolean
  isSaving?: boolean
  noValueText?: string
  dataTestId?: string
  isHorizontal?: boolean
}

interface Emits {
  (e: 'update', fieldName: string, value: any): void
  (e: 'save', fieldName: string, value: any): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  issue: null,
  isEditing: true,
  isSaving: false,
  noValueText: '—',
  isHorizontal: false
})

const emit = defineEmits<Emits>()

// Store instance
const memberStore = useMemberStore()

// Member products state
const memberProducts = ref<any[]>([])

// Computed loading state from member store
const isLoading = computed(() => memberStore.loadingProducts)

// Generate a unique instance ID for debugging
const instanceId = Math.random().toString(36).substr(2, 9)

// Computed options for the multiselect
const productOptions = computed(() => {
  return memberProducts.value.map((product: any) => ({
    lbl: product.lbl,
    val: product.val,
    id: product.id
  }))
})

// Computed display value - show product labels instead of IDs
const computedDisplayValue = computed(() => {
  if (!props.value || memberProducts.value.length === 0) {
    return props.displayValue
  }
  
  // Handle array values (multiselect)
  if (Array.isArray(props.value)) {
    const labels = props.value.map((val: any) => {
      const matchingProduct = memberProducts.value.find((product: any) => product.val === val)
      return matchingProduct ? matchingProduct.lbl : val
    })
    return labels
  }
  
  // Handle single value
  const matchingProduct = memberProducts.value.find((product: any) => product.val === props.value)
  if (matchingProduct) {
    return matchingProduct.lbl
  }
  
  // Fallback to original display value
  return props.displayValue
})

// Watch for member store products changes and sync with local array
watch(() => memberStore.memberProducts, (newProducts) => {
  if (newProducts && newProducts.length > 0) {
    memberProducts.value = newProducts.map((product: any) => ({
      val: product.id || product.members_devices_id,
      lbl: product.name || product.device_name || product.title,
      ...product
    }))

  }
}, { deep: true })

// Watch for issue changes to fetch products if needed
watch(() => props.issue, (newIssue) => {
  if (newIssue && memberProducts.value.length === 0 && !memberStore.loadingProducts) {
    fetchMemberProducts()
  }
}, { immediate: true })

// Fetch member products based on case data using member store
async function fetchMemberProducts() {
  if (!props.issue) {
    return
  }
  
  try {
    // Extract required data from the case
    const members_id = (props.issue as any).members_id
    const members_locations_id = props.issue.members_locations_id
    const members_organizations_id = (props.issue as any).members_organizations_id || (props.issue as any).org_id
    

    
    if (!members_id || !members_locations_id) {
      console.warn('ProductFormField: Missing required data for fetching member products:', {
        members_id,
        members_locations_id,
        members_organizations_id
      })
      return
    }
    
    // Use member store to fetch products
    await memberStore.fetchMemberProducts(members_id, members_locations_id, members_organizations_id)
    
    // Update local reference to use store data
    memberProducts.value = memberStore.memberProducts.map((product: any) => ({
      val: product.id || product.members_devices_id,
      lbl: product.name || product.device_name || product.title,
      ...product
    }))
  } catch (error) {
    console.error('ProductFormField: Failed to fetch member products:', error)
    memberProducts.value = []
  }
}

// Fetch products on component mount if we have the required case data
onMounted(() => {
  if (props.issue && memberProducts.value.length === 0 && !memberStore.loadingProducts) {
    fetchMemberProducts()
  }
})

// Forward events to parent
const handleUpdate = (fieldName: string, value: any) => {
  emit('update', fieldName, value)
}

const handleSave = (fieldName: string, value: any) => {
  emit('save', fieldName, value)
}

const handleCancel = () => {
  emit('cancel')
}

// Expose methods that parent might need
const bravoFormFieldRef = ref()

// Handle save completion - forward to the underlying BravoFormField
function handleSaveComplete(success: boolean = true) {
  // Get reference to the BravoFormField component
  const bravoField = bravoFormFieldRef.value
  
  if (bravoField && typeof bravoField.handleSaveComplete === 'function') {
    // Add a small delay to ensure the call happens after any pending updates
    setTimeout(() => {
      bravoField.handleSaveComplete(success)
    }, 10)
  } else {
    console.warn(`🛍️ ProductFormField [${instanceId}]: BravoFormField ref not found or handleSaveComplete not available`)
  }
}

// Expose the handleSaveComplete method so parent components can call it
defineExpose({
  handleSaveComplete
})
</script>

<template>
  <BravoFormField
    ref="bravoFormFieldRef"
    :label="label"
    :field-name="fieldName"
    :value="value"
    :display-value="computedDisplayValue"
    input-type="multiselect"
    display-type="chips"
    :options="productOptions"
    option-label="lbl"
    option-value="val"
    :is-loading="isLoading"
    :is-horizontal="isHorizontal"
    :is-editing="isEditing"
    :is-saving="isSaving"
    :no-value-text="noValueText"
    :data-test-id="dataTestId"
    @update="handleUpdate"
    @save="handleSave"
    @cancel="handleCancel"
    class="product-form-field"
  />
</template>

<style scoped>
.product-form-field {
  max-width: 500px;
}

/* Match the hover background styling from BravoFormField */
:deep(.field-value.editable-field) {
  border-radius: 8px;
}
</style> 