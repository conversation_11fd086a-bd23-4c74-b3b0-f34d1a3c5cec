import type { Issue } from '@/services/IssuesAPI'

export interface BlockConfigField {
  field: string
  label: string
  inputType: 'text' | 'dropdown' | 'multiselect' | 'date' | 'datetime'
  displayType: 'text' | 'chips' | 'tag'
  required?: boolean
  dataSource?: {
    type: 'api' | 'meta' | 'computed'
    endpoint?: string
    filters?: Array<{
      property: string
      value: string | ((issue: Issue) => any)
    }>
    optionLabel?: string
    optionValue?: string
  }
}

export interface BlockConfig {
  fields: BlockConfigField[]
  visibility?: {
    condition: (issue: Issue | null, casesStore: any) => boolean
  }
}

// Block config definitions - maps block config types to their field configurations
export const blockConfigs: Record<string, BlockConfig> = {
  owners: {
    fields: [
      {
        field: 'owner_partners_teams_id',
        label: 'Owner Team',
        inputType: 'dropdown',
        displayType: 'text',
        required: true,
        dataSource: {
          type: 'api',
          endpoint: 'members_locations_partners_teams_picklist',
          filters: [
            {
              property: 'members_locations_id',
              value: (issue: Issue) => issue.members_locations_id
            },
            {
              property: 'orig_team_id', 
              value: (issue: Issue) => (issue as any).owner_partners_teams_id
            }
          ],
          optionLabel: 'lbl',
          optionValue: 'val'
        }
      },
      {
        field: 'owner_users_id',
        label: 'Owner User',
        inputType: 'dropdown',
        displayType: 'text',
        required: false,
        dataSource: {
          type: 'api',
          endpoint: 'partners_teams_users',
          filters: [
            {
              property: 'team_ids',
              value: (issue: Issue) => [(issue as any).owner_partners_teams_id]
            },
            {
              property: 'orig_owner_id',
              value: (issue: Issue) => (issue as any).owner_users_id
            }
          ],
          optionLabel: 'lbl',
          optionValue: 'val'
        }
      }
    ]
  },
  resolve_issue: {
    fields: [
      {
        field: 'resolution_toggle',
        label: 'Resolve Case',
        inputType: 'dropdown', // We'll handle this as a special toggle
        displayType: 'text'
      },
      {
        field: 'resolution',
        label: 'Resolution Status',
        inputType: 'dropdown',
        displayType: 'text',
        dataSource: {
          type: 'meta',
          endpoint: 'pl__issues_resolution'
        }
      },
      {
        field: 'idr_resolution',
        label: 'Resolution Notes',
        inputType: 'text',
        displayType: 'text'
      }
    ],
    visibility: {
      condition: (issue, casesStore) => casesStore.isCurrentCaseResolvedOrClosed
    }
  },
  survey: {
    fields: [
      {
        field: 'survey_emails',
        label: 'Survey Email Recipients',
        inputType: 'multiselect',
        displayType: 'chips'
      },
      {
        field: 'customer_survey_checklist',
        label: 'Customer Survey Checklist',
        inputType: 'dropdown',
        displayType: 'text'
      }
    ],
    visibility: {
      condition: (issue, casesStore) => casesStore.isCurrentCaseResolvedOrClosed
    }
  }
} 