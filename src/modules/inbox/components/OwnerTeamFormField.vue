<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import BravoFormField from '@/modules/knowledge/components/BravoFormField.vue'
import { useMetaAPI } from '@/composables/services/useMetaAPI'
import type { Issue } from '@/services/IssuesAPI'

interface Props {
  label: string
  fieldName: string
  value: any
  displayValue: string | string[]
  issue?: Issue | null
  isEditing?: boolean
  isSaving?: boolean
  noValueText?: string
  dataTestId?: string
  isHorizontal?: boolean
}

interface Emits {
  (e: 'update', fieldName: string, value: any): void
  (e: 'save', fieldName: string, value: any): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  issue: null,
  isEditing: true,
  isSaving: false,
  noValueText: '—',
  isHorizontal: false
})

const emit = defineEmits<Emits>()

// State
const ownerTeams = ref<any[]>([])
const loading = ref(false)
const metaAPI = useMetaAPI()
const bravoFormFieldRef = ref<any>(null)

// Generate a unique instance ID for debugging
const instanceId = Math.random().toString(36).substr(2, 9)

// Computed options for the field
const fieldOptions = computed(() => {
  return ownerTeams.value.map((team: any) => ({
    lbl: team.lbl,
    val: team.val,
    id: team.id
  }))
})

// Computed display value - show team label instead of ID
const computedDisplayValue = computed(() => {
  if (!props.value || ownerTeams.value.length === 0) {
    return props.displayValue
  }
  
  // Find the team that matches the current value
  const matchingTeam = ownerTeams.value.find((team: any) => team.val === props.value)
  if (matchingTeam) {
    return matchingTeam.lbl
  }
  
  // Fallback to original display value
  return props.displayValue
})

// Fetch owner teams based on case data
async function fetchOwnerTeams() {
  if (!props.issue) return
  
  try {
    loading.value = true
    
    // Extract required data from the case
    const members_locations_id = props.issue.members_locations_id
    const orig_team_id = (props.issue as any).owner_partners_teams_id
    

    
    if (!members_locations_id) {
      console.warn('🏢 OwnerTeamFormField: Missing members_locations_id for fetching owner teams')
      return
    }
    
    // Fetch teams using the meta API
    const response = await metaAPI.fetchMembersLocationsPartners({
      members_locations_id,
      orig_team_id
    })
    
    // Update local array with formatted team data
    ownerTeams.value = (response.pl__members_partners_teams || []).map((team: any) => ({
      val: team.val,
      lbl: team.lbl,
      id: team.id,
      ...team
    }))
    

    
  } catch (error) {
    console.error('🏢 OwnerTeamFormField: Error fetching owner teams:', error)
    ownerTeams.value = []
  } finally {
    loading.value = false
  }
}

// Watch for issue changes and fetch teams
watch(() => props.issue, (newIssue) => {
  if (newIssue && ownerTeams.value.length === 0 && !loading.value) {
    fetchOwnerTeams()
  }
}, { immediate: true })

// Fetch on mount if issue is available
onMounted(() => {
  if (props.issue && ownerTeams.value.length === 0 && !loading.value) {
    fetchOwnerTeams()
  }
})

// Handle field updates
function handleUpdate(fieldName: string, value: any) {
  emit('update', fieldName, value)
}

// Handle field saves
function handleSave(fieldName: string, value: any) {
  emit('save', fieldName, value)
}

// Handle cancel
function handleCancel() {
  emit('cancel')
}

// Handle save completion - forward to the underlying BravoFormField
function handleSaveComplete(success: boolean = true) {
  // Get reference to the BravoFormField component
  const bravoField = bravoFormFieldRef.value
  
  if (bravoField && typeof bravoField.handleSaveComplete === 'function') {
    // Add a small delay to ensure the call happens after any pending updates
    setTimeout(() => {
      bravoField.handleSaveComplete(success)
    }, 10)
  } else {
    console.warn(`🏢 OwnerTeamFormField [${instanceId}]: BravoFormField ref not found or handleSaveComplete not available`)
  }
}

// Expose the handleSaveComplete method so parent components can call it
defineExpose({
  handleSaveComplete
})


</script>

<template>
  <BravoFormField
    ref="bravoFormFieldRef"
    :label="label"
    :field-name="fieldName"
    :value="value"
    :display-value="computedDisplayValue"
    input-type="dropdown"
    display-type="text"
    :options="fieldOptions"
    option-label="lbl"
    option-value="val"
    :is-loading="loading"
    :is-horizontal="isHorizontal"
    :is-editing="isEditing"
    :is-saving="isSaving"
    :no-value-text="noValueText"
    :data-test-id="dataTestId"
    @update="handleUpdate"
    @save="handleSave"
    @cancel="handleCancel"
  />
</template> 

<style scoped>
/* Match the hover background styling from BravoFormField */
:deep(.field-value.editable-field) {
  border-radius: 8px;
}
</style>