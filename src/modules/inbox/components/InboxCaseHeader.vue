<script setup lang="ts">
import { ref, nextTick, watch, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue';
import BravoMenu from '@services/ui-component-library/components/BravoMenu.vue';
import BravoSkeleton from '@services/ui-component-library/components/BravoSkeleton.vue';
import BravoSplitButton from '@services/ui-component-library/components/BravoSplitButton.vue';
import BravoTextarea from '@services/ui-component-library/components/BravoTextarea.vue';
import BravoTitle1 from '@services/ui-component-library/components/BravoTypography/BravoTitle1.vue';
import BravoTitle2 from '@services/ui-component-library/components/BravoTypography/BravoTitle2.vue';
import BravoTag from '@services/ui-component-library/components/BravoTag.vue';
import BravoChip from '@services/ui-component-library/components/BravoChip.vue';
import BravoLabel from '@services/ui-component-library/components/BravoLabel.vue';
import BravoCheckbox from '@services/ui-component-library/components/BravoCheckbox.vue';
import BravoAvatar from '@services/ui-component-library/components/BravoAvatar.vue';
import AvatarGroup from 'primevue/avatargroup';
import Dropdown from 'primevue/dropdown';
import AddTaskModal from './AddTaskModal.vue';
import WaitingModal from './WaitingModal.vue';
import SetToReadyModal from './SetToReadyModal.vue';
import ChangeCustomerModal from './ChangeCustomerModal.vue';
import EditableTitle from './EditableTitle.vue';
import type { MenuItem } from 'primevue/menuitem';
import type { Issue } from '../../../services/IssuesAPI';
import type { Task } from '../../../types/task';
import { useCasesStore } from '../../../stores/cases';
import { useMetaStore } from '../../../stores/meta';
import { createTask } from '../../../composables/services/useTasksApi';
import BravoCaption1 from '@services/ui-component-library/components/BravoTypography/BravoCaption1.vue';

// Interface for form data when creating a task
interface CreateTaskData {
    name: string;
    description: string;
    dueDate: string | Date | null;
    orgId: string | null;
    teamId: string | null;
    userId: string | null;
}

const props = defineProps<{ issue: Issue | null }>();

const emit = defineEmits<{
    (e: 'resolve'): void;
    (e: 'resolve-action', action: string): void;
    (e: 'reopen'): void;
    (e: 'escalate'): void;
    (e: 'de-escalate'): void;
    (e: 'add-file'): void;
}>();

// Status mapping based on the provided data
const statusMap = {
    '1': { label: 'New', state: 'new' },
    '2': { label: 'Ready', state: 'ready' },
    '3': { label: 'Scheduling', state: 'ready' },
    '4': { label: 'Scheduled', state: 'ready' },
    '5': { label: 'En Route', state: 'ready' },
    '6': { label: 'In Progress', state: 'ready' },
    '7': { label: 'Resolved', state: 'resolved' },
    '9': { label: 'Pending Close', state: 'resolved' },
    '10': { label: 'Closed', state: 'closed' },
    '79': { label: 'Waiting', state: 'waiting' },
    '89': { label: 'Canceled', state: 'closed' },
    '99': { label: 'Deleted', state: 'closed' },
} as const;

// Computed property for status info
const statusInfo = computed(() => {
    if (!props.issue?.status) return null;
    return statusMap[props.issue.status as keyof typeof statusMap] || null;
});

// Check if the case is resolved
const isResolved = computed(() => {
    // Check if status is '7' (Resolved) or if resolution field indicates resolved
    return (
        props.issue?.status === '7' ||
        props.issue?.resolution === 1 ||
        props.issue?.resolution === 3 ||
        props.issue?.c__d_status === 'Resolved' ||
        props.issue?.c__d_resolution === 'Completed'
    );
});

// Computed property for the main resolve button
const resolveButtonConfig = computed(() => {
    if (isResolved.value) {
        return {
            label: 'Reopen Case',
            action: 'reopen',
        };
    } else {
        return {
            label: 'Set to Resolved',
            action: 'resolve',
        };
    }
});

const casesStore = useCasesStore();
const metaStore = useMetaStore();

const isFavorite = ref(false);
const showNoteModal = ref(false);
const showTaskModal = ref(false);
const showWaitingModal = ref(false);
const showReadyModal = ref(false);
const showChangeCustomerModal = ref(false);
const noteText = ref('');
const isSavingNote = ref(false);
const noteError = ref<string | null>(null);
const noteTextareaRef = ref();
const editableTitleRef = ref();

const route = useRoute();
const router = useRouter();

// Watch for modal visibility and focus textarea when it opens
watch(showNoteModal, async (isVisible) => {
    if (isVisible) {
        await nextTick();
        // Use multiple attempts with increasing delays
        setTimeout(() => focusTextarea(), 100);
        setTimeout(() => focusTextarea(), 200);
        setTimeout(() => focusTextarea(), 300);
    }
});

const lightningActions = ref<MenuItem[]>([
    { label: 'Assign', icon: 'pi pi-user-plus', command: () => handleLightningAction('assign') },
    { label: 'Add Task', icon: 'pi pi-check-square', command: () => handleLightningAction('add-task') },
    { label: 'Add File', icon: 'pi pi-paperclip', command: () => handleLightningAction('add-file') },
]);

// Computed property for lightning actions based on escalation status
const currentLightningActions = computed(() => {
    const baseActions = [
        { label: 'Assign', icon: 'pi pi-user-plus', command: () => handleLightningAction('assign') },
        { label: 'Add Task', icon: 'pi pi-check-square', command: () => handleLightningAction('add-task') },
        { label: 'Add File', icon: 'pi pi-paperclip', command: () => handleLightningAction('add-file') },
    ];

    // Add escalate or de-escalate based on current status
    if (props.issue?.escalated) {
        baseActions.push({
            label: 'De-escalate',
            icon: 'pi pi-arrow-down',
            command: () => handleLightningAction('de-escalate'),
        });
    } else {
        baseActions.push({
            label: 'Escalate',
            icon: 'pi pi-arrow-up',
            command: () => handleLightningAction('escalate'),
        });
    }

    // Only add "Add Journey" if there's no workflow_id
    // Check both possible locations of workflow_id using dynamic property access
    const hasWorkflow = !!(props.issue && ((props.issue as any)['workflow_id'] || (props.issue as any)['journey_id']));

    if (!hasWorkflow) {
        baseActions.push({
            label: 'Add Journey',
            icon: 'pi pi-compass',
            command: () => handleLightningAction('add-journey'),
        });
    }

    // Add remaining actions
    baseActions.push({
        label: 'Change Customer',
        icon: 'pi pi-users',
        command: () => handleLightningAction('change-customer'),
    });

    return baseActions;
});

const resolveActions = ref<MenuItem[]>([
    { label: 'Set to Unresolved', icon: 'pi pi-question', command: () => handleResolveAction('unresolved') },
    { label: 'Set to Canceled', icon: 'pi pi-times', command: () => handleResolveAction('cancel') },
    { label: 'Set to Deleted', icon: 'pi pi-trash', command: () => handleResolveAction('delete') },
]);

const lightningMenu = ref();
const resolveMenu = ref();

function toggleFavorite() {
    isFavorite.value = !isFavorite.value;
}
function openNoteModal() {
    noteText.value = '';
    noteError.value = null;
    showNoteModal.value = true;
}

function focusTextarea() {
    // Try multiple methods to focus the textarea
    if (noteTextareaRef.value) {
        // Method 1: Try the component's $el
        const componentEl = noteTextareaRef.value.$el;
        if (componentEl) {
            const textarea = componentEl.querySelector('textarea');
            if (textarea) {
                textarea.focus();
                return true;
            }
        }
    }

    // Method 2: Find textarea in the dialog
    const dialogTextarea = document.querySelector('.note-form textarea');
    if (dialogTextarea) {
        (dialogTextarea as HTMLTextAreaElement).focus();
        return true;
    }

    // Method 3: Find any textarea in the dialog
    const anyTextarea = document.querySelector('[role="dialog"] textarea');
    if (anyTextarea) {
        (anyTextarea as HTMLTextAreaElement).focus();
        return true;
    }

    return false;
}

function closeNoteModal() {
    showNoteModal.value = false;
    noteText.value = '';
    noteError.value = null;
}
function handleLightningAction(action: string) {
    if (action === 'escalate') {
        emit('escalate');
    } else if (action === 'de-escalate') {
        emit('de-escalate');
    } else if (action === 'delete') {
        emit('resolve-action', 'delete');
    } else if (action === 'add-file') {
        emit('add-file');
    } else if (action === 'add-task') {
        showTaskModal.value = true;
    } else if (action === 'add-journey') {
        // Navigate to journeys tab and clear panel param
        router.push({
            query: {
                ...route.query,
                panel: undefined,
                tab: 'journeys',
            },
        });
    } else if (action === 'change-customer') {
        showChangeCustomerModal.value = true;
    } else {
        // Placeholder for other lightning actions - no action needed
    }
}
function handleResolveAction(action: string) {
    if (action === 'wait') {
        showWaitingModal.value = true;
    } else {
        // Emit resolve action to parent
        emit('resolve-action', action);
    }
}

function handlePrimaryResolve() {
    // Primary resolve action
    if (isResolved.value) {
        emit('reopen');
    } else {
        emit('resolve');
    }
}

async function saveNote() {
    if (!noteText.value.trim()) return;

    const issue = casesStore.currentIssue;
    if (!issue?.id) {
        noteError.value = 'No issue selected';
        return;
    }

    isSavingNote.value = true;
    noteError.value = null;

    try {
        await casesStore.addNote(issue.id, noteText.value.trim());
        closeNoteModal();
        // Note: In a real app, you might want to emit an event or refresh the notes list
        // For now, we'll just close the modal on success
    } catch (err) {
        noteError.value = err instanceof Error ? err.message : 'Failed to save note';
    } finally {
        isSavingNote.value = false;
    }
}

async function handleTaskSubmit(taskData: CreateTaskData) {
    console.log('handleTaskSubmit called with taskData:', taskData);

    try {
        // Prepare the task request data to match the working example format
        const taskRequest: any = {
            name: taskData.name,
            description: taskData.description || '',
            status: 'Not Started',
            completed: false,
            deleted: false,
            links: [],
        };

        // Include orgId if provided
        if (taskData.orgId) {
            taskRequest.orgId = taskData.orgId;
            console.log('Including orgId in request:', taskData.orgId);
        }

        // Include dueDate if it's set and convert to ISO string
        if (taskData.dueDate) {
            console.log('Including dueDate in request:', taskData.dueDate);
            if (typeof taskData.dueDate === 'string') {
                // If it's already a string, use it (assuming it's already ISO format)
                taskRequest.dueDate = taskData.dueDate;
            } else {
                // If it's a Date object, convert to ISO string
                taskRequest.dueDate = taskData.dueDate.toISOString();
            }
            console.log('Final dueDate in request:', taskRequest.dueDate);
        } else {
            console.log('No dueDate provided in taskData');
        }

        // Build links array exactly like the working example
        const links: Array<{ rel: string; id: string; href?: string }> = [];

        // Add team link first (matches working example order)
        if (taskData.teamId) {
            links.push({
                rel: 'team',
                id: taskData.teamId,
            });
        }

        // Add user link second (matches working example order)
        if (taskData.userId) {
            links.push({
                rel: 'user',
                id: taskData.userId,
            });
        }

        // Add case link last with href (matches working example)
        if (props.issue?.id) {
            links.push({
                rel: 'case',
                id: props.issue.id,
                href: `/#cases/edit/${props.issue.id}`,
            });
        }

        taskRequest.links = links;

        console.log('Final task request payload:', taskRequest);

        // Create the task via API
        const createdTask = await createTask(taskRequest);
        console.log('Task created successfully:', createdTask);

        // Close the modal on success
        showTaskModal.value = false;

        // TODO: You might want to refresh a tasks list or show a success message here
    } catch (error) {
        console.error('Failed to create task:', error);
        // TODO: Show error message to user
        throw error; // Re-throw so the modal can handle the error state
    }
}

function handleTaskCancel() {
    showTaskModal.value = false;
}

// Check if the case is in waiting status
const isWaiting = computed(() => {
    // Check both string and number formats, and also check c__d_status
    return (
        props.issue?.status === '79' || String(props.issue?.status) === '79' || props.issue?.c__d_status === 'Waiting'
    );
});

// Computed property for waiting reason options from metadata
const waitingReasonOptions = computed(() => {
    if (!metaStore.metaData?.pl__tags) return [];

    return metaStore.metaData.pl__tags
        .filter((tag: any) => tag.category === 50)
        .map((tag: any) => ({
            label: tag.lbl,
            value: tag.val || tag.id,
        }));
});

// Update resolve actions based on waiting status
const currentResolveActions = computed(() => {
    if (isWaiting.value) {
        // For waiting cases, show "Set to Ready" option
        return [
            { label: 'Set to Ready', icon: 'pi pi-play', command: () => (showReadyModal.value = true) },
            { label: 'Cancel', icon: 'pi pi-times', command: () => handleResolveAction('cancel') },
            { label: 'Delete', icon: 'pi pi-trash', command: () => handleResolveAction('delete') },
        ];
    } else if (isResolved.value) {
        // For resolved cases, only show delete option
        return [{ label: 'Delete', icon: 'pi pi-trash', command: () => handleResolveAction('delete') }];
    } else {
        // For unresolved cases, show resolve actions without wait (now has its own button)
        return resolveActions.value;
    }
});

// Function to handle waiting button click
function handleWaitingButtonClick() {
    showWaitingModal.value = true;
}

// Function to handle set to ready button click
function handleSetToReadyButtonClick() {
    showReadyModal.value = true;
}

// Function to handle setting case to waiting
async function handleSetToWaiting(waitingData: any) {
    try {
        // Use the cases store to update the issue
        await casesStore.updateCase(waitingData);

        closeWaitingModal();

        // Emit an event to refresh the case data
        emit('resolve-action', 'wait');
    } catch (err) {
        console.error('Failed to set case to waiting:', err);
        throw err; // Re-throw so the modal can handle the error
    }
}

// Function to handle setting case back to ready
async function handleSetToReady(readyData: any) {
    try {
        // Use the cases store's setToReady helper function
        await casesStore.setToReady(props.issue!.id, readyData.waiting_notes);

        closeReadyModal();

        // Emit an event to refresh the case data
        emit('resolve-action', 'ready');
    } catch (err) {
        console.error('Failed to set case to ready:', err);
        throw err; // Re-throw so the modal can handle the error
    }
}

function closeWaitingModal() {
    showWaitingModal.value = false;
}

function closeReadyModal() {
    showReadyModal.value = false;
}

// Function to handle changing customer
async function handleChangeCustomer(selectedCustomer: any) {
    try {
        if (!props.issue?.id) {
            throw new Error('No issue selected');
        }

        // Use the cases store to change the customer
        await casesStore.changeCustomer(props.issue.id, {
            members_id: selectedCustomer.members_id,
            members_locations_id: selectedCustomer.id,
            c__name: selectedCustomer.c__name || selectedCustomer.site_name,
        });

        closeChangeCustomerModal();

        // Emit an event to refresh the case data
        emit('resolve-action', 'customer-changed');
    } catch (err) {
        console.error('Failed to change customer:', err);
        throw err; // Re-throw so the modal can handle the error
    }
}

function closeChangeCustomerModal() {
    showChangeCustomerModal.value = false;
}

// Load metadata on component mount
onMounted(async () => {
    // Load meta data if not already loaded
    if (!metaStore.metaData) {
        await metaStore.loadMetaData();
    }
    
    // Load partner meta data if not already loaded (for avatars)
    if (!metaStore.partnerMetaData || metaStore.allPartners.length === 0) {
        await metaStore.loadPartnerMetaData();
    }
});

// Function to handle saving case name
async function handleSaveCaseName(newName: string) {
    if (!props.issue?.id) return;

    try {
        await casesStore.updateCase({
            id: props.issue.id,
            display_name: newName,
        });
        // Success - the store will update the local state automatically
        editableTitleRef.value?.handleSaveComplete(true);
    } catch (error) {
        console.error('Failed to update case name:', error);
        // You might want to show an error message to the user here
        // For now, we'll just log the error
        editableTitleRef.value?.handleSaveComplete(false);
    }
}

// Helper function to capitalize first letter
const capitalizeFirstLetter = (str: string): string => {
    if (!str) return str;
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

// Computed property for customer contact information
const customerInfo = computed(() => {
    if (!props.issue) return null;

    // Try to get customer name from different sources
    const customerName =
        props.issue.c__name ||
        (props.issue.memberUser?.first_name && props.issue.memberUser?.last_name
            ? `${props.issue.memberUser.first_name} ${props.issue.memberUser.last_name}`
            : null) ||
        'Unknown Customer';

    return {
        name: customerName,
        firstName: capitalizeFirstLetter(props.issue.memberUser?.first_name || ''),
        lastName: capitalizeFirstLetter(props.issue.memberUser?.last_name || ''),
    };
});

// Computed property for location name
const locationName = computed(() => {
    if (!props.issue) return null;
    
    // Try to get location name from different sources
    return props.issue.location?.name || 
           props.issue.location?.site_name || 
           props.issue.c__location || 
           null;
});

// Computed property for collaborator avatars
const collaboratorAvatars = computed(() => {
    // Access the property using bracket notation to avoid TypeScript errors
    const collaboratorIds = (props.issue as any)?.collaborator_partners_ids;
    
    if (!collaboratorIds || !Array.isArray(collaboratorIds)) {
        return [];
    }

    return collaboratorIds.map((partnerId: string) => {
        const partner = metaStore.getPartnerById(partnerId);
        const avatarUrl = metaStore.getPartnerAvatarById(partnerId);
        const partnerName = metaStore.getPartnerNameById(partnerId) || partnerId;

        return {
            id: partnerId,
            image: avatarUrl,
            label: partnerName,
            // For BravoAvatar fallback when no image
            firstName: partnerName.charAt(0).toUpperCase(),
            lastName: ''
        };
    });
});
</script>

<template>
    <div class="view-header">
        <div class="header-title-section">
            <!-- // vertically align my flexbox below me  -->

            <span v-if="issue" class="flex items-center justify-center gap-4">
                <BravoAvatar
                    v-if="customerInfo"
                    :firstName="customerInfo.firstName"
                    :lastName="customerInfo.lastName"
                    class="customer-avatar"
                    size="32"
                    v-tooltip.top="{ value: `Customer: ${customerInfo.name}`, showDelay: 400 }"
                />
                <div class="case-title-container">
                    <EditableTitle
                        :model-value="issue.display_name || issue.id || 'No Title'"
                        :loading="casesStore.loading"
                        @save="handleSaveCaseName"
                        ref="editableTitleRef"
                    />
                    <div v-if="issue.reference_num || statusInfo" class="case-meta-row">
                        <BravoTag v-if="statusInfo" :value="statusInfo.label" :state="statusInfo.state" class="mr-2" />
                        <BravoCaption1 v-if="issue.reference_num">{{ issue.reference_num }}</BravoCaption1>
                        <span v-if="issue.reference_num && locationName" class="pipe-separator">&nbsp;|&nbsp;</span>
                        <div v-if="locationName" class="location-container">
                            <BravoCaption1>{{ locationName }}</BravoCaption1>
                            <BravoButton
                                icon="pi pi-pencil"
                                size="small"
                                severity="secondary"
                                text
                                rounded
                                class="location-edit-btn"
                                aria-label="Change Location"
                                v-tooltip.top="{ value: 'Change Location', showDelay: 400 }"
                                @click="showChangeCustomerModal = true"
                            />
                        </div>
                    </div>
                </div>
                <BravoTag
                    v-if="issue.escalated"
                    value="Escalated"
                    severity="danger"
                    class="ml-2"
                    v-tooltip.top="issue.c__d_escalation_reasons || 'Case has been escalated'"
                />
            </span>
            <BravoSkeleton v-else width="200px" height="24px" />
        </div>
        <div class="header-actions">
            <!-- Collaborator Partners Avatar Group -->
            <AvatarGroup v-if="collaboratorAvatars.length > 1" class="collaborator-avatars mr-2">
                <BravoAvatar
                    v-for="collaborator in collaboratorAvatars.slice(0, 3)"
                    :key="collaborator.id"
                    :image="collaborator.image"
                    :firstName="collaborator.firstName"
                    :lastName="collaborator.lastName"
                    size="32"
                    shape="circle"
                    v-tooltip.top="{ value: collaborator.label, showDelay: 400 }"
                />
                <BravoAvatar
                    v-if="collaboratorAvatars.length > 3"
                    :label="`+${collaboratorAvatars.length - 3}`"
                    size="32"
                    shape="circle"
                    style="background-color: #e5e7eb; color: #374151"
                    v-tooltip.top="{ 
                        value: collaboratorAvatars.slice(3).map(c => c.label).join(', '), 
                        showDelay: 400 
                    }"
                />
            </AvatarGroup>
            <!-- <BravoButtonview-card
        icon="pi pi-star"
        :class="{ 'is-favorite': isFavorite }"
        severity="secondary"
        rounded
        aria-label="Favorite"
        @click="toggleFavorite"
        :outlined="!isFavorite"
        :style="{ marginRight: '0.5rem', border: 'none' }"
            /> -->
            <BravoButton
                icon="pi pi-comment"
                severity="secondary"
                class="mr-2"
                aria-label="Add Note"
                v-tooltip.top="{ value: 'Add Note', showDelay: 400 }"
                @click="openNoteModal"
            />
            <BravoMenu :model="currentLightningActions" popup ref="lightningMenu" />
            <BravoButton
                icon="pi pi-bolt"
                severity="secondary"
                class="mr-2"
                aria-label="Case Actions"
                v-tooltip.top="{ value: 'Case Actions', showDelay: 400 }"
                @click="lightningMenu?.toggle($event)"
            />
            <!-- Waiting/Ready Button -->
            <BravoButton
                v-if="isWaiting"
                icon="pi pi-clock"
                severity="secondary"
                class="mr-2"
                aria-label="Set to Ready"
                v-tooltip.top="{ value: 'Set to Ready', showDelay: 400 }"
                @click="handleSetToReadyButtonClick"
            />
            <BravoButton
                v-else-if="!isResolved"
                icon="pi pi-clock"
                severity="secondary"
                class="mr-2"
                aria-label="Set to Waiting"
                v-tooltip.top="{ value: 'Set to Waiting', showDelay: 400 }"
                @click="handleWaitingButtonClick"
            />
            <BravoSplitButton
                :label="resolveButtonConfig.label"
                :model="currentResolveActions"
                @click="handlePrimaryResolve"
                aria-label="Resolve"
            />
        </div>
        <!-- Note Modal -->
        <BravoDialog
            v-model:visible="showNoteModal"
            header="Add a Note"
            :modal="true"
            :style="{ width: '400px' }"
            :closable="true"
            :closeOnEscape="!isSavingNote"
            :dismissableMask="!isSavingNote"
        >
            <div class="note-form">
                <BravoTextarea
                    v-model="noteText"
                    rows="4"
                    placeholder="Type your note..."
                    class="w-full"
                    autoResize
                    :disabled="isSavingNote"
                    ref="noteTextareaRef"
                    autofocus
                />

                <div v-if="noteError" class="note-error">
                    {{ noteError }}
                </div>
            </div>

            <template #footer>
                <div class="dialog-footer">
                    <BravoButton label="Cancel" @click="closeNoteModal" severity="secondary" :disabled="isSavingNote" />
                    <BravoButton
                        :label="isSavingNote ? 'Saving...' : 'Save'"
                        @click="saveNote"
                        severity="primary"
                        :disabled="!noteText.trim() || isSavingNote"
                        :loading="isSavingNote"
                    />
                </div>
            </template>
        </BravoDialog>

        <!-- Task Modal -->
        <AddTaskModal
            v-model:visible="showTaskModal"
            :issue="issue"
            :onSubmit="handleTaskSubmit"
            :onCancel="handleTaskCancel"
        />

        <!-- Waiting Modal -->
        <WaitingModal
            v-model:visible="showWaitingModal"
            :issue="issue"
            :waitingReasonOptions="waitingReasonOptions"
            :onSubmit="handleSetToWaiting"
            :onCancel="closeWaitingModal"
        />

        <!-- Ready Modal -->
        <SetToReadyModal
            v-model:visible="showReadyModal"
            :issue="issue"
            :onSubmit="handleSetToReady"
            :onCancel="closeReadyModal"
        />

        <!-- Change Customer Modal -->
        <ChangeCustomerModal
            v-model:visible="showChangeCustomerModal"
            :issue="issue"
            :onSubmit="handleChangeCustomer"
            :onCancel="closeChangeCustomerModal"
        />
    </div>
</template>

<style scoped>
.view-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    height: 64px;
    border-bottom: 1px solid var(--border-color);
    background: #fff;
}

.header-title-section {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex: 1;
    min-width: 0;
    overflow: hidden;
}

.case-title-container {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
    flex: 1;
    min-width: 0;
    max-width: 430px;
    overflow: visible;
}

.case-meta-row {
    display: flex;
    align-items: center;
    margin-top: -0.25rem;
    max-width: 100%;
    overflow: visible;
}

.case-meta-row > * {
    flex-shrink: 0;
}



.case-meta-row :deep(.p-tag) {
    height: 20px !important;
    line-height: 20px !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    flex-shrink: 0;
}

.case-meta-row :deep(.bravo-caption1) {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 150px;
}

.pipe-separator {
    color: var(--text-color-secondary);
}

.location-container {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    white-space: nowrap;
    position: relative;
}

.location-container .bravo-caption1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.location-edit-btn {
    opacity: 0;
    transition: opacity 0.2s ease;
    padding: 0.25rem !important;
    width: 20px !important;
    height: 20px !important;
    min-width: 20px !important;
    flex-shrink: 0;
    margin-left: 0.25rem;
}

.location-container:hover .location-edit-btn {
    opacity: 1;
}

.location-edit-btn :deep(.p-button-icon) {
    font-size: 0.7rem;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    flex-shrink: 0;
}

.customer-avatar {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    flex-shrink: 0;
}

.is-favorite {
    color: #f59e42 !important;
}
.note-form {
    padding: 1rem 0;
}
.note-error {
    color: var(--red-600);
    font-size: 0.875rem;
    margin-top: 0.5rem;
    padding: 0.5rem;
    background: var(--red-50);
    border: 1px solid var(--red-200);
    border-radius: 4px;
}
.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
}

.escalated-chip {
    background-color: #fed7aa !important;
    color: #c2410c !important;
    font-weight: 600 !important;
}

.waiting-form,
.ready-form {
    padding: 1rem 0;
}

.error-message {
    color: var(--red-600);
    font-size: 0.875rem;
    padding: 0.5rem;
    background: var(--red-50);
    border: 1px solid var(--red-200);
    border-radius: 4px;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
}

.auto-return-fields {
    border-left: 3px solid var(--primary-color);
    padding-left: 1rem;
    margin-left: 0.5rem;
    background: var(--surface-50);
    border-radius: 0 4px 4px 0;
    padding: 1rem;
    margin-top: 1rem;
}

.specific-time-fields {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.date-input,
.time-input {
    padding: 0.5rem;
    border: 1px solid var(--surface-300);
    border-radius: 4px;
    font-size: 0.875rem;
}

.date-input:focus,
.time-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
  }

.collaborator-avatars {
    display: flex;
    align-items: center;
}

.collaborator-avatars :deep(.p-avatar) {
    margin-left: -0.5rem;
    border: 2px solid var(--surface-100);
    background-color: white;
}

.collaborator-avatars :deep(.p-avatar:first-child) {
    margin-left: 0;
}
  
  </style>
