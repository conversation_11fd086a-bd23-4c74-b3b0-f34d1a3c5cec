<template>
  <div class="files-list">
    <div v-if="loading" class="files-loading">
      <BravoSkeleton v-for="i in 3" :key="i" width="100%" height="60px" class="mb-2" />
    </div>
    
    <div v-else-if="files.length === 0" class="no-files">
      <p class="no-files-text">No files attached to this case.</p>
      <BravoButton
        label="Add File"
        icon="pi pi-plus"
        severity="secondary"
        @click="handleAddFile"
        class="add-file-button"
      />
    </div>
    
    <div v-else class="files-content">
      <div class="files-header">
        <BravoButton
          label="Add File"
          icon="pi pi-plus"
          severity="secondary"
          @click="handleAddFile"
          class="add-file-button"
        />
      </div>
      
      <div class="files-grid">
        <div 
          v-for="file in files" 
          :key="file.id" 
          class="file-item"
          @click="openFile(file)"
        >
          <div class="file-icon">
            <BravoSkeleton 
              v-if="file.thumbnail && (file.type.startsWith('image/') || file.type === 'application/pdf') && !failedThumbnails.has(file.id) && loadingThumbnails.has(file.id)"
              width="100%" 
              height="100%" 
              border-radius="4px"
            />
            <img 
              v-else-if="file.thumbnail && (file.type.startsWith('image/') || file.type === 'application/pdf') && !failedThumbnails.has(file.id)"
              :src="file.thumbnail"
              :alt="file.name"
              class="file-thumbnail"
              @load="handleThumbnailLoad(file.id)"
              @error="handleThumbnailError(file.id)"
              @loadstart="handleThumbnailLoadStart(file.id)"
            />
            <i v-else :class="getFileIcon(file.type)" class="file-type-icon"></i>
          </div>
          <div class="file-details">
            <div class="file-name" :title="file.name">{{ file.c__d__name }}</div>
            <div class="file-meta">
              <span class="file-size">{{ formatFileSize(file.size) }}</span>
              <span class="file-date">{{ file.c__d__created }}</span>
            </div>
            <div v-if="file.c__d__fullname" class="file-uploader">
              Uploaded by {{ file.c__d__fullname }}
            </div>
          </div>
          <div class="file-actions">
            <BravoButton
              icon="pi pi-ellipsis-h"
              severity="secondary"
              size="small"
              text
              @click="(event) => toggleFileMenu(event, file)"
              :title="'More actions'"
              class="file-menu-button"
            />
            <BravoMenu 
              :ref="(el) => { if (el) fileMenus[file.id] = el }"
              :model="getFileMenuItems(file)" 
              :popup="true" 
            />
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Confirmation Dialog -->
  <BravoConfirmDialog />
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import BravoSkeleton from '@services/ui-component-library/components/BravoSkeleton.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoMenu from '@services/ui-component-library/components/BravoMenu.vue'
import BravoConfirmDialog from '@services/ui-component-library/components/BravoConfirmDialog.vue'
import { useConfirm } from 'primevue/useconfirm'
import { useToast } from 'primevue/usetoast'
import { useFilesAPI, type FileItem } from '@/composables/services/useFilesAPI'

const props = defineProps<{
  files: FileItem[]
  loading?: boolean
}>()

const emit = defineEmits<{
  'file-deleted': [fileId: string]
  'add-file': []
}>()

const confirm = useConfirm()
const toast = useToast()
const filesAPI = useFilesAPI()

// Track files with failed thumbnails
const failedThumbnails = ref<Set<string>>(new Set())
const loadingThumbnails = ref<Set<string>>(new Set())

// File menu refs
const fileMenus = ref<Record<string, any>>({})

// Create menu items for each file
const getFileMenuItems = (file: FileItem) => [
  {
    label: 'Download',
    icon: 'pi pi-download',
    command: () => {
      downloadFile(file)
    }
  },
  {
    label: 'Delete',
    icon: 'pi pi-trash',
    class: 'text-red-600',
    command: () => {
      deleteFile(file)
    }
  }
]

// Methods
function getFileIcon(mimeType: string): string {
  if (mimeType.startsWith('image/')) {
    return 'pi pi-image'
  } else if (mimeType === 'application/pdf') {
    return 'pi pi-file-pdf'
  } else if (mimeType.includes('word') || mimeType.includes('document')) {
    return 'pi pi-file-word'
  } else if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) {
    return 'pi pi-file-excel'
  } else if (mimeType.includes('powerpoint') || mimeType.includes('presentation')) {
    return 'pi pi-file'
  } else if (mimeType.startsWith('video/')) {
    return 'pi pi-video'
  } else if (mimeType.startsWith('audio/')) {
    return 'pi pi-volume-up'
  } else if (mimeType.includes('zip') || mimeType.includes('archive')) {
    return 'pi pi-file-archive'
  } else {
    return 'pi pi-file'
  }
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

function openFile(file: FileItem) {
  // Open file in new tab
  window.open(file.file, '_blank')
}

function downloadFile(file: FileItem) {
  // Create a temporary link to download the file
  const link = document.createElement('a')
  link.href = file.file
  link.download = file.name
  link.target = '_blank'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

function deleteFile(file: FileItem) {
  confirm.require({
    message: `Are you sure you want to delete ${file.c__d__name || file.name}?`,
    header: 'Delete File',
    icon: 'pi pi-exclamation-triangle',
    acceptClass: 'p-button-danger',
    rejectClass: 'p-button-secondary',
    acceptLabel: 'Yes',
    rejectLabel: 'Cancel',
    accept: async () => {
      await performFileDelete(file)
    }
  })
}

async function performFileDelete(file: FileItem) {
  try {
    // Use the FilesAPI composable to delete the file
    const result = await filesAPI.deleteFile(file.id)

    if (result.success) {
      // Emit event to parent component to refresh the file list
      emit('file-deleted', file.id)
    } else {
      throw new Error(result.message || 'Failed to delete file')
    }
  } catch (error) {
    console.error('Error deleting file:', error)
    toast.add({
      severity: 'error',
      summary: 'Delete Failed',
      detail: error instanceof Error ? error.message : 'Failed to delete file',
      life: 5000,
    })
  }
}

function toggleFileMenu(event: Event, file: FileItem) {
  event.stopPropagation()
  const menu = fileMenus.value[file.id]
  if (menu) {
    menu.toggle(event)
  }
}

function handleThumbnailError(id: string) {
  // Handle thumbnail error - log as warning since fallback icon will be shown
  console.warn(`Thumbnail failed to load for file with id: ${id}, using fallback icon`)
  failedThumbnails.value.add(id)
}

function handleThumbnailLoad(id: string) {
  // Handle thumbnail load
  loadingThumbnails.value.delete(id)
}

function handleThumbnailLoadStart(id: string) {
  // Handle thumbnail load start
  loadingThumbnails.value.add(id)
}

function handleAddFile() {
  emit('add-file')
}
</script>

<style scoped>
.files-list {
  padding: 0;
}

.files-loading {
  padding: 1rem 0;
}

.no-files {
  padding: 2rem 1rem;
  text-align: center;
}

.no-files-text {
  margin: 0;
  color: var(--surface-500);
  font-style: italic;
}

.files-grid {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border: 1px solid var(--surface-200);
  border-radius: 6px;
  background: var(--surface-0);
  cursor: pointer;
  transition: all 0.2s ease;
}

.file-item:hover {
  border-color: var(--primary-color);
  background: var(--primary-50);
}

.file-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--surface-100);
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid var(--surface-200);
}

.file-type-icon {
  font-size: 1.25rem;
  color: var(--primary-color);
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-weight: 600;
  color: var(--surface-900);
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
  color: var(--surface-600);
  margin-bottom: 0.25rem;
}

.file-uploader {
  font-size: 0.75rem;
  color: var(--surface-500);
}

.file-actions {
  flex-shrink: 0;
}

.file-actions .p-button {
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.file-item:hover .file-actions .p-button {
  opacity: 1;
}

.file-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

.files-header {
  display: flex;
  justify-content: flex-end;
  padding-bottom: 1rem;
}

.add-file-button {
  margin-bottom: 0.5rem;
}
</style> 