<script setup lang="ts">
import BravoSkeleton from '@services/ui-component-library/components/BravoSkeleton.vue'
import BravoZeroStateScreen from '@services/ui-component-library/components/BravoZeroStateScreen.vue'
import { useCopilotStore } from '@/stores/copilot'
import { ref, computed, watch, nextTick } from 'vue'
import type { Issue } from '../../../services/IssuesAPI'
import type { CopilotSearchResult } from '@/types/copilot'
import InputText from 'primevue/inputtext'
import InputGroup from 'primevue/inputgroup'
import InputGroupAddon from 'primevue/inputgroupaddon'
import CopilotResponse from './CopilotResponse.vue'
import ZeroStateSearchSvg from '@/assets/zero-state-search.svg'
import { convertMarkdownToHtml } from '@/utils/textFormatting'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import { useToast } from 'primevue/usetoast'
import Tooltip from 'primevue/tooltip'

// Register the tooltip directive
const vTooltip = Tooltip

const props = withDefaults(defineProps<{
  issue?: Issue
  loading?: boolean
}>(), {
  issue: undefined,
  loading: false
})

const copilotStore = useCopilotStore()
const toast = useToast()
const searchQuery = ref('')
const searchLoading = ref(false)
const error = ref(null)
const textareaRef = ref<HTMLTextAreaElement | null>(null)
const contentWrapperRef = ref<HTMLDivElement | null>(null)

// Conversation history
interface ConversationItem {
  id: string
  userQuery: string
  response: CopilotSearchResult | null
  isStreaming: boolean
  streamingContent: string
  sourcesExpanded: boolean
}

const conversationHistory = ref<ConversationItem[]>([])



// Copy response to clipboard for a specific conversation item
const copyResponse = async (conversationItem: ConversationItem) => {
  try {
    const textToCopy = conversationItem.response?.message || conversationItem.streamingContent || '';
    await navigator.clipboard.writeText(textToCopy);
    toast.add({
      severity: 'success',
      summary: 'Copied!',
      detail: 'Response copied to clipboard',
      life: 2000
    });
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    toast.add({
      severity: 'error',
      summary: 'Copy failed',
      detail: 'Unable to copy to clipboard',
      life: 3000
    });
  }
}

// Toggle sources visibility for a specific conversation item
const toggleSources = (conversationItem: ConversationItem) => {
  conversationItem.sourcesExpanded = !conversationItem.sourcesExpanded;
}

// Convert markdown content to HTML for a specific conversation item
const getDisplayContent = (conversationItem: ConversationItem) => {
  const content = conversationItem.streamingContent || conversationItem.response?.message || '';
  return convertMarkdownToHtml(content);
}

// Scroll to bottom of conversation
const scrollToBottom = () => {
  nextTick(() => {
    if (contentWrapperRef.value) {
      contentWrapperRef.value.scrollTop = contentWrapperRef.value.scrollHeight;
    }
  });
}

// Auto-resize textarea
const autoResizeTextarea = () => {
  if (textareaRef.value) {
    textareaRef.value.style.height = 'auto';
    textareaRef.value.style.height = textareaRef.value.scrollHeight + 'px';
  }
}

// Watch for changes in searchQuery to auto-resize
watch(searchQuery, () => {
  nextTick(() => {
    autoResizeTextarea();
  });
})

// Focus textarea when clicking anywhere in the composer
const focusTextarea = () => {
  if (textareaRef.value) {
    textareaRef.value.focus();
  }
}

const handleSearch = async () => {
    console.log('searchQuery', searchQuery.value)
    console.log('---- execute the search! ')

    const query = searchQuery.value.trim()
    if (!query) return

    // Create new conversation item
    const conversationId = Date.now().toString()
    const newConversationItem: ConversationItem = {
        id: conversationId,
        userQuery: query,
        response: null,
        isStreaming: false,
        streamingContent: '',
        sourcesExpanded: false
    }
    
    // Add to conversation history
    conversationHistory.value.push(newConversationItem)
    
    // Clear search input and reset textarea height, but keep focus
    searchQuery.value = ''
    if (textareaRef.value) {
      textareaRef.value.style.height = 'auto';
      // Keep focus in textarea for quick follow-up questions
      textareaRef.value.focus();
    }

    // Scroll to bottom to show new conversation
    scrollToBottom()

    // Set loading state
    searchLoading.value = true
    error.value = null

    try {
        const result = await copilotStore.submitSearch(
            query,
            // Real-time streaming callback
            (content: string) => {
                // Find the current conversation item and update it
                const currentItem = conversationHistory.value.find(item => item.id === conversationId)
                if (currentItem) {
                    // Set streaming to true on first content update
                    if (!currentItem.isStreaming) {
                        currentItem.isStreaming = true
                        searchLoading.value = false
                        scrollToBottom() // Scroll when streaming starts
                    }
                    currentItem.streamingContent = content
                    // Auto-scroll during streaming to keep up with content
                    scrollToBottom()
                }
            }
        )
        
        // Update the conversation item with final response
        const currentItem = conversationHistory.value.find(item => item.id === conversationId)
        if (currentItem) {
            currentItem.isStreaming = false
            currentItem.response = result
            currentItem.streamingContent = ''
        }
        
        // Final scroll to ensure sources are visible
        scrollToBottom()
        
        searchLoading.value = false
        error.value = null
        
    } catch (e: any) {
        console.error('error', e)
        searchLoading.value = false
        error.value = e
        
        // Remove failed conversation item
        const index = conversationHistory.value.findIndex(item => item.id === conversationId)
        if (index > -1) {
            conversationHistory.value.splice(index, 1)
        }
    }
}
</script>

<template>
  <div class="panel-content">
    <div v-if="props.loading" class="copilot-skeleton">
      <div class="copilot-header-skeleton">
        <BravoSkeleton width="150px" height="24px" class="mb-4" />
      </div>
      <div class="copilot-content-skeleton">
        <BravoSkeleton width="100%" height="40px" border-radius="6px" class="mb-3" />
        <BravoSkeleton width="100%" height="40px" border-radius="6px" class="mb-3" />
        <BravoSkeleton width="70%" height="40px" border-radius="6px" />
      </div>
    </div>
    <div v-else class="copilot-main">
      <div class="card-layout">
        <div class="copilot-content-wrapper" ref="contentWrapperRef">
          <div :class="['copilot-content', { 'center-content': !searchLoading && conversationHistory.length === 0 && !error }]">
            <BravoZeroStateScreen 
              v-if="!searchLoading && conversationHistory.length === 0 && !error"
              title="Find Answers"
              message="Ask a question below and your CX Copilot will generate an answer for you from all the knowledge that it has access to."
              :showButton="false"
              :imageSrc="ZeroStateSearchSvg"
              imageAlt="Copilot Search"
              :actionHandler="() => {}"
            />
            
            <!-- Conversation History -->
            <div v-for="conversationItem in conversationHistory" :key="conversationItem.id" class="chat-conversation">
              <!-- User message bubble -->
              <div class="user-message-container">
                <div class="user-message-bubble">
                  <div class="user-message-content">
                    {{ conversationItem.userQuery }}
                  </div>
                </div>
              </div>
              
              <!-- AI thinking state -->
              <div v-if="conversationItem.isStreaming && !conversationItem.streamingContent" class="ai-thinking-container">
                <div class="ai-thinking-bubble">
                  <div class="ai-thinking-content">
                    <BravoSkeleton shape="circle" size="16px" class="thinking-spinner" />
                    <span class="thinking-text">Thinking...</span>
                  </div>
                </div>
              </div>
              
              <!-- AI response -->
              <div v-if="conversationItem.isStreaming || conversationItem.response" class="unified-response">
                <div class="copilot-response-body">
                  <span v-html="getDisplayContent(conversationItem)"></span>
                  <span v-if="conversationItem.isStreaming" class="streaming-cursor">|</span>
                </div>
                <div v-if="conversationItem.response" class="copilot-response-tagline">
                  <div class="tagline-content">
                    <BravoButton
                      icon="pi pi-copy"
                      text
                      severity="secondary"
                      @click="() => copyResponse(conversationItem)"
                      class="copy-button"
                      v-tooltip.top="'Copy response'"
                    />
                    <button
                      v-if="conversationItem.response?.sources?.length || conversationItem.response?.relatedLinks?.length"
                      @click="() => toggleSources(conversationItem)"
                      class="sources-toggle-button"
                    >
                      <span>Sources ({{ (conversationItem.response?.sources?.length || 0) + (conversationItem.response?.relatedLinks?.length || 0) }})</span>
                      <i :class="conversationItem.sourcesExpanded ? 'pi pi-chevron-up' : 'pi pi-chevron-down'" class="sources-chevron"></i>
                    </button>
                  </div>
                </div>
                
                <!-- Sources and Related Links (expandable) -->
                <div v-if="conversationItem.response && conversationItem.sourcesExpanded && (conversationItem.response?.sources?.length || conversationItem.response?.relatedLinks?.length)" class="sources-container">
                  <!-- Sources -->
                  <div v-if="conversationItem.response?.sources?.length" class="sources-section">
                    <div class="copilot-response-subheader">Sources</div>
                    <a
                      v-for="(source, index) in conversationItem.response.sources"
                      :key="'source-' + index"
                      :href="source.url"
                      target="_blank"
                      class="copilot-response-link"
                    >{{ source.title || source.url }}</a>
                  </div>
                  
                  <!-- Related links -->
                  <div v-if="conversationItem.response?.relatedLinks?.length" class="sources-section">
                    <div class="copilot-response-subheader">Related</div>
                    <a
                      v-for="(relatedLink, index) in conversationItem.response.relatedLinks"
                      :key="'related-' + index"
                      :href="relatedLink.url"
                      target="_blank"
                      class="copilot-response-link"
                    >{{ relatedLink.title || relatedLink.url }}</a>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Current thinking state (for new questions) -->
            <div v-if="searchLoading && conversationHistory.length > 0" class="ai-thinking-container">
              <div class="ai-thinking-bubble">
                <div class="ai-thinking-content">
                  <BravoSkeleton shape="circle" size="16px" class="thinking-spinner" />
                  <span class="thinking-text">Thinking...</span>
                </div>
              </div>
            </div>
            
            <div v-if="!searchLoading && error" class="error-message">We're sorry, something went wrong.</div>
          </div>
        </div>
        <div class="sticky-input-container">
          <div class="chatgpt-composer">
            <div class="composer-input-wrapper" @click="focusTextarea">
              <i class="pi pi-paperclip composer-attach-icon" @click.stop></i>
              <textarea
                class="composer-input"
                placeholder="Ask anything"
                v-model="searchQuery"
                @keydown.enter.exact.prevent="handleSearch"
                rows="1"
                ref="textareaRef"
              ></textarea>
              <button 
                @click.stop="handleSearch" 
                class="composer-send-button"
                :disabled="!searchQuery.trim()"
              >
                <i class="pi pi-arrow-up"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.panel-content {
  display: flex;
  flex-direction: column;
  padding: 0;
  gap: 0;
  flex: 1;
  overflow: hidden;
  height: 100%;
  min-height: 100%;
  width: 100%;
  max-width: 100%;
  position: relative;
}

.copilot-main {
  display: flex;
  justify-content: center;
  height: 100%;
  min-height: 100%;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  flex: 1;
}

.card-layout {
  background-color: white;
  font-family: 'Inter';
  color: #303336;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 100%;
  height: 100%;
  min-height: 100%;
  border-left: none;
  border: none;
  overflow: hidden;
  position: relative;
  flex: 1;
}

.copilot-content-wrapper {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 20px 20px 120px 20px; /* Increased bottom padding for sticky input */
  height: 100%;
  min-height: 0; /* Allow flex child to shrink */
}

.sticky-input-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: 16px 20px 24px 20px;
  z-index: 10;
}

.chatgpt-composer {
  width: 100%;
  max-width: 100%;
}

.composer-input-wrapper {
  display: flex;
  align-items: center;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 24px;
  padding: 14px 16px;
  gap: 12px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.composer-input-wrapper:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.composer-attach-icon {
  color: #6b7280;
  font-size: 18px;
  cursor: pointer;
  transition: color 0.2s ease;
}

.composer-attach-icon:hover {
  color: #374151;
}

.composer-input {
  flex: 1;
  border: none !important;
  background: transparent;
  outline: none !important;
  font-size: 14px;
  padding: 8px 0;
  box-shadow: none !important;
  resize: none;
  overflow: hidden;
  min-height: 20px;
  max-height: 120px;
  line-height: 1.4;
  font-family: inherit;
}

.composer-input:focus {
  outline: none !important;
  box-shadow: none !important;
  border: none !important;
}

/* Override any textarea focus styles */
.composer-input:focus,
.composer-input:focus-visible {
  outline: none !important;
  box-shadow: none !important;
  border-color: transparent !important;
}

.composer-input::placeholder {
  color: var(--text-color-secondary);
}

.composer-send-button {
  background: var(--primary-600);
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease, opacity 0.2s ease;
}

.composer-send-button:hover:not(:disabled) {
  background: #0d8f6f;
}

.composer-send-button:disabled {
  background: #d1d5db;
  cursor: not-allowed;
}

.composer-send-button i {
  color: white;
  font-size: 14px;
  font-weight: bold;
}

.copilot-content {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  min-height: 100%;
}

.center-content {
  justify-content: center;
  align-items: center;
  height: 100%;
}



.error-message {
  color: var(--red-500);
  text-align: center;
  padding: 1rem;
  width: 100%;
  overflow-wrap: break-word;
  word-wrap: break-word;
}

.copilot-skeleton {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
  max-width: 100%;
  padding: 20px;
  height: 100%;
  flex: 1;
}

.copilot-header-skeleton {
  display: flex;
  justify-content: center;
  align-items: center;
}

.copilot-content-skeleton {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.unified-response {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  max-width: 100%;
  overflow-wrap: break-word;
  word-wrap: break-word;
  background: #f8f9fa;
  border-radius: 18px;
  padding: 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.unified-response .copilot-response-body {
  color: var(--text-color-primary);
  font-size: 14px;
  font-weight: 400;
  width: 100%;
  overflow-wrap: break-word;
  word-wrap: break-word;
  white-space: pre-wrap;
  line-height: 1.5;
  margin: 0;
}

/* Markdown HTML styling for unified response */
.unified-response .copilot-response-body :deep(h1) {
  font-size: 18px;
  font-weight: 600;
  margin: 16px 0 8px 0;
  color: var(--text-color-primary);
  line-height: 1.3;
}

.unified-response .copilot-response-body :deep(h2) {
  font-size: 16px;
  font-weight: 600;
  margin: 14px 0 6px 0;
  color: var(--text-color-primary);
  line-height: 1.3;
}

.unified-response .copilot-response-body :deep(h3) {
  font-size: 15px;
  font-weight: 600;
  margin: 12px 0 4px 0;
  color: var(--text-color-primary);
  line-height: 1.3;
}

.unified-response .copilot-response-body :deep(p) {
  margin: 8px 0;
  line-height: 1.5;
  color: var(--text-color-primary);
}

.unified-response .copilot-response-body :deep(p:first-child) {
  margin-top: 0;
}

.unified-response .copilot-response-body :deep(p:last-child) {
  margin-bottom: 0;
}

.unified-response .copilot-response-body :deep(strong) {
  font-weight: 600;
  color: var(--text-color-primary);
}

.unified-response .copilot-response-body :deep(em) {
  font-style: italic;
  color: var(--text-color-primary);
}

.unified-response .copilot-response-body :deep(ul) {
  margin: 8px 0;
  padding-left: 20px;
  list-style-type: disc;
}

.unified-response .copilot-response-body :deep(ol) {
  margin: 8px 0;
  padding-left: 20px;
  list-style-type: decimal;
}

.unified-response .copilot-response-body :deep(li) {
  margin: 4px 0;
  line-height: 1.5;
  color: var(--text-color-primary);
}

.unified-response .copilot-response-body :deep(a) {
  color: #005db3;
  text-decoration: underline;
}

.unified-response .copilot-response-body :deep(a:hover) {
  text-decoration: none;
}

.unified-response .copilot-response-tagline {
  font-size: 14px;
  color: #6d7379;
  margin-top: 12px;
  width: 100%;
}

.tagline-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.copy-button {
  padding: 4px !important;
  min-width: auto !important;
  height: auto !important;
}

.sources-toggle-button {
  background: transparent;
  border: none;
  color: var(--text-color-secondary);
  font-size: 14px;
  padding: 4px 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  border-radius: 8px;
  transition: color 0.2s ease, background-color 0.2s ease;
}

.sources-toggle-button:hover {
  color: var(--text-color-primary);
  background-color: var(--surface-100);
}

.sources-chevron {
  font-size: 10px;
  transition: transform 0.2s ease;
}

/* Sources container styling */
.sources-container {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #e5e7eb;
  width: 100%;
}

.sources-section {
  margin-bottom: 16px;
}

.sources-section:last-child {
  margin-bottom: 0;
}

.copilot-response-subheader {
  font-size: 13px;
  color: var(--text-color-primary);
  text-transform: uppercase;
  font-weight: 500;
  margin-bottom: 8px;
  letter-spacing: 0.5px;
}

.copilot-response-link {
  color: #005db3;
  font-size: 13px;
  text-decoration: none;
  margin-top: 8px;
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-all;
  max-width: 100%;
  display: block;
  line-height: 1.4;
}

.copilot-response-link:hover {
  text-decoration: underline;
}

.streaming-cursor {
  display: inline-block;
  animation: blink 1s infinite;
  color: var(--text-color-primary);
  font-weight: 400;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

/* Chat conversation container */
.chat-conversation {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 0;
  margin-bottom: 32px; /* Space between conversation items */
}

.chat-conversation:last-child {
  margin-bottom: 0; /* No margin on last conversation */
}

/* User message bubble styling */
.user-message-container {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;
  width: 100%;
}

.user-message-bubble {
  background: #007bff;
  color: white;
  border-radius: 18px;
  max-width: 80%;
  padding: 12px 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.user-message-content {
  font-size: 14px;
  line-height: 1.4;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* AI thinking bubble styling */
.ai-thinking-container {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 16px;
  width: 100%;
}

.ai-thinking-bubble {
  background: #f8f9fa;
  border-radius: 18px;
  padding: 12px 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  max-width: 80%;
}

.ai-thinking-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.thinking-text {
  font-size: 14px;
  color: var(--text-color-secondary);
  font-style: italic;
}

.thinking-spinner {
  flex-shrink: 0;
}


</style> 