<script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue'

// Disable automatic attribute inheritance since we manually handle class
defineOptions({
  inheritAttrs: false
})
import <PERSON><PERSON> from 'primevue/button'
import BravoFormField from '@/modules/knowledge/components/BravoFormField.vue'
import AddContact from './AddContact.vue'
import { useMetaStore } from '@/stores/meta'
import type { Issue } from '@/services/IssuesAPI'

interface Props {
  label: string
  fieldName: string
  value: any
  displayValue: string | string[]
  issue?: Issue | null
  isHorizontal?: boolean
  isEditing?: boolean
  isSaving?: boolean
  noValueText?: string
  dataTestId?: string
}

interface Emits {
  (e: 'update', fieldName: string, value: any): void
  (e: 'save', fieldName: string, value: any): void
  (e: 'cancel'): void
  (e: 'add-contact'): void
}

const props = withDefaults(defineProps<Props>(), {
  issue: null,
  isHorizontal: false,
  isEditing: false,
  isSaving: false,
  noValueText: '—',
  dataTestId: ''
})

const emit = defineEmits<Emits>()

// Store instances
const metaStore = useMetaStore()

// Members users state
const membersUsers = ref<any[]>([])
const loadingMembersUsers = ref(false)

// Ref to the BravoFormField component
const bravoFormFieldRef = ref<any>(null)

// Flag to prevent auto-save when Add Contact button is clicked
const preventAutoSave = ref(false)

// Add Contact dialog state
const showAddContactDialog = ref(false)

// Computed options for the dropdown
const contactOptions = computed(() => {
  return membersUsers.value.map((user: any) => ({
    lbl: user.lbl,
    val: user.val,
    id: user.id
  }))
})

// Computed display value with proper user name lookup
const computedDisplayValue = computed(() => {
  if (!props.value) {
    return props.noValueText
  }
  
  // If membersUsers is still loading, show loading state
  if (loadingMembersUsers.value) {
    return 'Loading...'
  }
  
  // Try to find the user in the loaded options
  const user = membersUsers.value.find((user: any) => user.val === props.value)
  if (user) {
    return user.lbl
  }
  
  // If we have a value but no matching user, show empty state instead of raw ID
  return props.noValueText
})

// Handle field updates
const handleFieldUpdate = (fieldName: string, value: any) => {
  emit('update', fieldName, value)
}

// Handle field save
const handleFieldSave = (fieldName: string, value: any) => {
  // Don't save if we're in the middle of an "Add Contact" action
  if (preventAutoSave.value) {
    return
  }
  
  emit('save', fieldName, value)
}

// Handle Add Contact dialog submission
const handleAddContactSubmit = async (contactData: any) => {
  // TODO: Implement API call to create the contact
  // For now, just close the dialog
  showAddContactDialog.value = false
  
  // TODO: After successful creation, refresh the members users list
  // and optionally select the new contact
}

// Handle Add Contact dialog cancel
const handleAddContactCancel = () => {
  showAddContactDialog.value = false
}

// Handle cancel
const handleCancel = () => {
  emit('cancel')
}

// Handle add contact button click
const handleAddContact = () => {
  // Set flag to prevent auto-save
  preventAutoSave.value = true
  
  // Clear the flag after a delay to allow normal behavior to resume
  setTimeout(() => {
    preventAutoSave.value = false
  }, 1000) // 1 second should be enough time for the modal to open
  
  // Show the Add Contact dialog
  showAddContactDialog.value = true
  
  // Still emit the event for parent components that might want to handle it
  emit('add-contact')
}

// Fetch members users based on case data
async function fetchMembersUsers() {
  if (!props.issue) return
  
  loadingMembersUsers.value = true
  try {
    // Extract required data from the case
    const members_id = (props.issue as any).members_id
    const members_locations_id = props.issue.members_locations_id
    const sponsor_id = props.issue.sponsor_partners_id || props.issue.owner_partners_id
    const context_org_id = props.issue.owner_partners_id || props.issue.sponsor_partners_id
    
    if (!members_id || !members_locations_id || !sponsor_id || !context_org_id) {
      console.warn('CustomerContactFormField: Missing required data for fetching members users:', {
        members_id,
        members_locations_id,
        sponsor_id,
        context_org_id
      })
      return
    }
    
    const response = await metaStore.fetchMembersUsers({
      members_id,
      members_locations_id,
      sponsor_id,
      context_org_id
    })
    
    membersUsers.value = response.pl__members_users || []
  } catch (error) {
    console.error('CustomerContactFormField: Failed to fetch members users:', error)
    membersUsers.value = []
  } finally {
    loadingMembersUsers.value = false
  }
}

// Watch for issue changes to fetch members users
watch(() => props.issue, (newIssue) => {
  if (newIssue && membersUsers.value.length === 0 && !loadingMembersUsers.value) {
    fetchMembersUsers()
  }
}, { immediate: true })

// Fetch data on component mount
onMounted(async () => {
  if (props.issue && membersUsers.value.length === 0 && !loadingMembersUsers.value) {
    fetchMembersUsers()
  }
})

// Expose methods for parent component control
defineExpose({
  handleSaveComplete: (success: boolean) => {
    bravoFormFieldRef.value?.handleSaveComplete(success)
  },
  startEditing: () => {
    bravoFormFieldRef.value?.startEditing()
  }
})
</script>

<template>
  <div :class="$attrs.class">
    <BravoFormField
      ref="bravoFormFieldRef"
      :label="label"
      :field-name="fieldName"
      :value="value"
      :display-value="computedDisplayValue"
      input-type="dropdown"
      display-type="text"
      :options="contactOptions"
      option-label="lbl"
      option-value="val"
      :is-loading="loadingMembersUsers"
      :is-horizontal="isHorizontal"
      :is-editing="isEditing"
      :is-saving="isSaving"
      :no-value-text="noValueText"
      :data-test-id="dataTestId"
      @update="handleFieldUpdate"
      @save="handleFieldSave"
      @cancel="handleCancel"
    >
      <!-- Custom footer template with Add Contact button -->
      <template #footer>
        <div class="add-contact-footer p-dropdown-panel p-component-overlay">
          <Button 
            label="Add Contact" 
            icon="pi pi-plus"
            severity="secondary" 
            text 
            size="small"
            class="add-contact-button"
            @click="handleAddContact"
            @mousedown.stop
            @click.stop
            :data-testid="`${dataTestId}-add-contact-btn`"
          />
        </div>
      </template>
    </BravoFormField>

    <!-- Add Contact Dialog -->
    <AddContact
      :visible="showAddContactDialog"
      @update:visible="showAddContactDialog = $event"
      :onSubmit="handleAddContactSubmit"
      :onCancel="handleAddContactCancel"
    />
  </div>
</template>

<style scoped>
.add-contact-footer {
  padding: 0.75rem;
  border-top: 1px solid var(--surface-300);
  background-color: var(--surface-50);
}

.add-contact-button {
  width: 100%;
  justify-content: center;
  gap: 0.5rem;
}

.add-contact-button:hover {
  background-color: var(--surface-100);
}
</style> 