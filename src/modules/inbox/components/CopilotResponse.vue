<script setup lang="ts">
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import { ref, computed } from 'vue';
import type { CopilotSearchResult } from '@/types/copilot';
import { useToast } from 'primevue/usetoast';
import { convertMarkdownToHtml } from '@/utils/textFormatting';

const toast = useToast();

// Define props
const props = defineProps<{
    response: CopilotSearchResult;
}>();

// Convert markdown message to HTML
const htmlMessage = computed(() => {
    const originalMessage = props.response.message || '';
    const convertedMessage = convertMarkdownToHtml(originalMessage);
    
    // Debug logging to see what's happening
    console.log('🔍 Markdown conversion debug:');
    console.log('Original message:', originalMessage);
    console.log('Converted message:', convertedMessage);
    
    return convertedMessage;
});

// Define the method to handle feedback
const handleSubmitFeedback = (feedback: string) => {
    console.log('Feedback submitted:', feedback);
    toast.add({ severity: 'success', summary: 'Feedback submitted', detail: feedback, life: 3000 });
};
</script>

<template>
    <div class="copilot-response">
        <div class="copilot-response-title">
            {{ response.title }}
        </div>
        <div class="copilot-response-body" v-html="htmlMessage"></div>
        <div class="copilot-response-tagline">Generated using AI</div>
        <div class="copilot-response-source">
            <div class="copilot-response-subheader">Sources</div>
            <a
                v-for="(source, index) in response.sources"
                :key="index"
                :href="source.url"
                target="_blank"
                class="copilot-response-link"
                >{{ source.title || source.url }}</a
            >
        </div>
        <div class="copilot-response-related-content" v-if="response.relatedLinks.length">
            <div class="copilot-response-subheader">Related</div>
            <a
                v-for="(relatedLink, index) in response.relatedLinks"
                :key="index"
                :href="relatedLink.url"
                target="_blank"
                class="copilot-response-link"
                >{{ relatedLink.title || relatedLink.url }}</a
            >
        </div>
        <div class="copilot-feedback" v-if="false">
            <div>Did this answer your question?</div>
            <div class="copilot-feedback-buttons">
                <BravoButton label="Yes" outlined @click="handleSubmitFeedback('Yes')" />
                <BravoButton label="Partially" outlined @click="handleSubmitFeedback('Partially')" />
                <BravoButton label="No" outlined @click="handleSubmitFeedback('No')" />
            </div>
        </div>
    </div>
</template>

<style scoped lang="scss">
.copilot-response {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    width: 100%;
    max-width: 100%;
    overflow-wrap: break-word;
    word-wrap: break-word;
}

.copilot-response-title {
    color: #303336;
    font-size: 15px;
    font-weight: 500;
    width: 100%;
    overflow-wrap: break-word;
    word-wrap: break-word;
}

.copilot-response-body {
    color: var(--text-color-primary);
    font-size: 14px;
    font-weight: 400;
    margin-top: 4px;
    width: 100%;
    overflow-wrap: break-word;
    word-wrap: break-word;
    white-space: pre-wrap;
}

/* Markdown HTML styling */
.copilot-response-body :deep(h1) {
    font-size: 18px;
    font-weight: 600;
    margin: 16px 0 8px 0;
    color: var(--text-color-primary);
    line-height: 1.3;
}

.copilot-response-body :deep(h2) {
    font-size: 16px;
    font-weight: 600;
    margin: 14px 0 6px 0;
    color: var(--text-color-primary);
    line-height: 1.3;
}

.copilot-response-body :deep(h3) {
    font-size: 15px;
    font-weight: 600;
    margin: 12px 0 4px 0;
    color: var(--text-color-primary);
    line-height: 1.3;
}

.copilot-response-body :deep(p) {
    margin: 8px 0;
    line-height: 1.5;
    color: var(--text-color-primary);
}

.copilot-response-body :deep(p:first-child) {
    margin-top: 0;
}

.copilot-response-body :deep(p:last-child) {
    margin-bottom: 0;
}

.copilot-response-body :deep(strong) {
    font-weight: 600;
    color: var(--text-color-primary);
}

.copilot-response-body :deep(em) {
    font-style: italic;
    color: var(--text-color-primary);
}

.copilot-response-body :deep(ul) {
    margin: 8px 0;
    padding-left: 20px;
    list-style-type: disc;
}

.copilot-response-body :deep(ol) {
    margin: 8px 0;
    padding-left: 20px;
    list-style-type: decimal;
}

.copilot-response-body :deep(li) {
    margin: 4px 0;
    line-height: 1.5;
    color: var(--text-color-primary);
}

.copilot-response-body :deep(br) {
    line-height: 1.5;
}

.copilot-response-body :deep(a) {
    color: #005db3;
    text-decoration: underline;
}

.copilot-response-body :deep(a:hover) {
    text-decoration: none;
}

.copilot-response-tagline {
    font-size: 12px;
    color: #6d7379;
    border-bottom: 1px solid var(--border-color);
    margin-top: 6px;
    width: 100%;
    padding-bottom: 16px;
}

.copilot-response-subheader {
    font-size: 14px;
    color: var(--text-color-primary);
    text-transform: uppercase;
    font-weight: 500;
    margin-bottom: 8px;
}

.copilot-feedback {
    margin-top: 20px;
    width: 100%;
}

.copilot-feedback-buttons {
    display: flex;
    justify-content: space-between;
    gap: 10px;
    margin-top: 10px;
    flex-wrap: wrap;
}

.copilot-response-related-content,
.copilot-response-source {
    display: flex;
    flex-direction: column;
    margin-top: 20px;
    width: 100%;
    max-width: 100%;
}

.copilot-response-link {
    color: #005db3;
    font-size: 13px;
    text-decoration: none;
    margin-top: 10px;
    overflow-wrap: break-word;
    word-wrap: break-word;
    word-break: break-all;
    max-width: 100%;
}

.copilot-response-link:hover {
    text-decoration: underline;
}
</style> 