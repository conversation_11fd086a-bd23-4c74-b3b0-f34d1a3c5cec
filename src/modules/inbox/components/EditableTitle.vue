<script setup lang="ts">
import { ref, nextTick, watch } from 'vue'
import BravoTitle1 from '@services/ui-component-library/components/BravoTypography/BravoTitle1.vue'
import BravoInputText from '@services/ui-component-library/components/BravoInputText.vue'

interface Props {
  modelValue: string
  loading?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'save', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()

const isEditing = ref(false)
const editValue = ref('')
const inputRef = ref()
const isSaving = ref(false)

// Watch for changes to start editing
watch(() => props.modelValue, (newValue) => {
  if (!isEditing.value) {
    editValue.value = newValue
  }
})

async function startEditing() {
  editValue.value = props.modelValue
  isEditing.value = true
  await nextTick()
  setTimeout(() => {
    if (inputRef.value && inputRef.value.$el) {
      const inputElement = inputRef.value.$el.querySelector('input') || inputRef.value.$el;
      if (inputElement && inputElement.focus) {
        inputElement.focus();
        // Place cursor at the end
        if (inputElement.setSelectionRange) {
          const len = inputElement.value.length;
          inputElement.setSelectionRange(len, len);
        }
      }
    }
  }, 100);
}

function cancelEditing() {
  isEditing.value = false
  editValue.value = props.modelValue
  isSaving.value = false
}

async function saveEditing() {
  if (editValue.value.trim() && editValue.value !== props.modelValue) {
    isSaving.value = true
    try {
      emit('save', editValue.value.trim())
      // Don't exit edit mode here - wait for parent to call handleSaveComplete
    } catch (error) {
      console.error('Error saving:', error)
      isSaving.value = false
    }
  } else {
    // If no changes, just exit edit mode
    isEditing.value = false
  }
}

// Function for parent to call when save is complete
function handleSaveComplete(success: boolean = true) {
  isSaving.value = false
  if (success) {
    isEditing.value = false
  }
  // If not successful, stay in edit mode
}

function handleKeydown(event: KeyboardEvent) {
  if (event.key === 'Enter') {
    event.preventDefault()
    saveEditing()
  } else if (event.key === 'Escape') {
    event.preventDefault()
    cancelEditing()
  }
}

function handleBlur() {
  // Small delay to allow click events to fire first
  setTimeout(() => {
    if (isEditing.value && !isSaving.value) {
      saveEditing()
    }
  }, 100)
}

// Expose the handleSaveComplete function for parent components
defineExpose({
  handleSaveComplete
})
</script>

<template>
  <div class="editable-title">
    <BravoTitle1 
      v-if="!isEditing"
      class="cursor-pointer hover-editable-field rounded transition-colors title-display"
      @click="startEditing"
      :title="'Click to edit: ' + modelValue"
    >
      {{ modelValue || 'No Title' }}
    </BravoTitle1>
    
    <div v-else class="editing-container">
      <BravoInputText
        ref="inputRef"
        v-model="editValue"
        class="title-input"
        :disabled="loading || isSaving"
        @keydown="handleKeydown"
        @blur="handleBlur"
        :style="{ fontSize: '18px', fontWeight: '600', padding: '0.25rem 0.5rem' }"
      />
      
      <!-- Loading spinner -->
      <div v-if="isSaving || loading" class="loading-spinner">
        <i class="pi pi-spin pi-spinner" style="font-size: 1rem; color: #3b82f6;"></i>
      </div>
    </div>
  </div>
</template>

<style scoped>
.editable-title {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  width: 100%;
}

.title-display {
  max-width: 350px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

.editing-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.title-input {
  min-width: 300px;
  margin-left: -4px;
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.hover-editable-field {
  transition: background-color 0.2s ease;
  padding: 2px 4px;
  border-radius: 8px;
}

.hover-editable-field:hover {
  background-color: rgba(0, 0, 0, 0.05);
}
</style> 