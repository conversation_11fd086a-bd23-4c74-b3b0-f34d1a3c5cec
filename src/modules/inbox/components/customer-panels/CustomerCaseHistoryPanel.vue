<script setup lang="ts">
import { computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useCasesStore } from '@/stores/cases'
import { usePartnerStore } from '@/stores/partner'
import BravoSubhead from '@services/ui-component-library/components/BravoTypography/BravoSubhead.vue'
import BravoCaption2 from '@services/ui-component-library/components/BravoTypography/BravoCaption2.vue'
import BravoTimestamp from '@services/ui-component-library/components/BravoTimestamp.vue'
import BravoTag from '@services/ui-component-library/components/BravoTag.vue'
import { getStatusInfo, getCaseIcon, getCaseIconLabel, getCaseTeamName } from '../../utils/caseHelper'

const props = defineProps<{
  issue: any
}>()

const router = useRouter()
const casesStore = useCasesStore()
const partnerStore = usePartnerStore()

const memberId = computed(() => props.issue?.member?.id)
const locationId = computed(() => props.issue?.location?.id)
const orgId = computed(() => props.issue?.member?.context_org_id)

async function loadMemberCases() {
  if (memberId.value && locationId.value && orgId.value) {
    await casesStore.fetchMemberCases(memberId.value, locationId.value, orgId.value)
  }
}

function navigateToCase(caseId: string) {
  router.push(`/inbox/cases/${caseId}`)
}

watch(memberId, () => {
  loadMemberCases()
})

onMounted(async () => {
  loadMemberCases()
  
  // Load partner teams if not already loaded
  if (partnerStore.partnerTeams.length === 0) {
    await partnerStore.fetchPartnerTeams(false)
  }
})
</script>

<template>
  <div class="py-2 flex flex-col gap-2 w-full max-w-full overflow-hidden">
    <div v-if="casesStore.loadingMemberCases" class="text-slate-500 italic">Loading case history...</div>
    <div v-else-if="casesStore.memberCasesError" class="text-red-500">{{ casesStore.memberCasesError }}</div>
    <template v-else-if="casesStore.memberCases.length">
      <div 
        v-for="caseItem in casesStore.memberCases" 
        :key="caseItem.id"
        class="bg-white border border-slate-200 rounded p-3 cursor-pointer hover:bg-slate-50 transition-colors w-full max-w-full overflow-hidden min-w-0"
        @click="navigateToCase(caseItem.id)"
      >
        <!-- Top row: Case name and status -->
        <div class="flex items-start justify-between mb-1">
          <div class="flex items-center gap-2 min-w-0 flex-1 mr-3">
            <div class="w-5 flex justify-center flex-shrink-0">
              <i 
                :class="getCaseIcon(caseItem)" 
                class="text-slate-500 text-lg"
                :title="getCaseIconLabel(caseItem)"
              ></i>
            </div>
            <div class="flex items-center gap-1 min-w-0 truncate">
              <BravoSubhead v-if="caseItem.reference_num" class="flex-shrink-0">
                {{ caseItem.reference_num }} |
              </BravoSubhead>
              <BravoSubhead class="truncate min-w-0">{{ caseItem.display_name }}</BravoSubhead>
            </div>
          </div>
          <div class="flex-shrink-0">
            <BravoTag 
              :value="getStatusInfo(caseItem.status).label"
              :state="getStatusInfo(caseItem.status).state"
            />
          </div>
        </div>
        <!-- Bottom row: Team info and timestamp -->
        <div class="flex items-center justify-between">
          <div v-if="(caseItem as any).owner_partners_teams_id" class="flex items-center gap-2">
            <div class="w-5 flex justify-center flex-shrink-0">
              <img 
                v-if="(caseItem as any).c__partners_avatar" 
                :src="(caseItem as any).c__partners_avatar" 
                :alt="getCaseTeamName(caseItem, partnerStore.partnerTeams)"
                class="w-5 h-5 rounded-sm object-cover object-center"
              />
            </div>
            <BravoCaption2 :style="{ color: 'var(--text-color-secondary)' }">
              {{ getCaseTeamName(caseItem, partnerStore.partnerTeams) }}
            </BravoCaption2>
          </div>
          <div v-else class="w-5"></div>
          <div class="flex-shrink-0">
            <BravoTimestamp 
              v-if="caseItem.updated" 
              :datetime="caseItem.updated" 
              length="long" 
              :style="{ color: 'var(--text-color-secondary)' }"
            />
            <BravoCaption2 v-else :style="{ color: 'var(--text-color-secondary)' }">
              —
            </BravoCaption2>
          </div>
        </div>
      </div>
    </template>
    <div v-else class="text-slate-500 italic">No case history available</div>
  </div>
</template> 