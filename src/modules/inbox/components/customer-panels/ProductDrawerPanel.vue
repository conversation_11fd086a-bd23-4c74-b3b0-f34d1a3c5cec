<script setup lang="ts">
const props = defineProps<{
  product: any
}>()
</script>

<template>
  <div v-if="product" class="product-details">
    <!-- Product Header -->
    <div class="product-header mb-6">
      <div class="flex items-center gap-3 mb-3">
        <div class="product-avatar">
          <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
            <i class="pi pi-cog text-green-600 text-xl"></i>
          </div>
        </div>
        <div>
          <h4 class="text-xl font-semibold text-slate-800">{{ product.label || product.lbl || 'Product' }}</h4>
          <div class="flex items-center gap-2 mt-1">
            <span 
              :class="[
                'rounded px-2 py-0.5 text-xs font-semibold',
                product.c__d_status === 'Ok' ? 'bg-green-100 text-green-600' :
                product.c__d_status === 'Unknown' ? 'bg-yellow-100 text-yellow-600' :
                'bg-red-100 text-red-600'
              ]"
            >
              {{ product.c__d_status || 'Unknown' }}
            </span>
            <span 
              :class="[
                'rounded px-2 py-0.5 text-xs font-semibold',
                product.c__d_active === 'Yes' ? 'bg-green-100 text-green-600' : 'bg-slate-100 text-slate-600'
              ]"
            >
              {{ product.c__d_active || 'Inactive' }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Product Information -->
    <div class="product-info space-y-4">
      <div v-if="product.make" class="info-item">
        <label class="text-sm font-medium text-slate-600">Manufacturer</label>
        <div class="mt-1 text-slate-800">{{ product.make }}</div>
      </div>

      <div v-if="product.model" class="info-item">
        <label class="text-sm font-medium text-slate-600">Model</label>
        <div class="mt-1 text-slate-800">{{ product.model }}</div>
      </div>

      <div v-if="product.c__d_type" class="info-item">
        <label class="text-sm font-medium text-slate-600">Product Type</label>
        <div class="mt-1 text-slate-800">{{ product.c__d_type }}</div>
      </div>

      <div v-if="product.created" class="info-item">
        <label class="text-sm font-medium text-slate-600">Created Date</label>
        <div class="mt-1 text-slate-800">{{ new Date(product.created).toLocaleDateString() }}</div>
      </div>

      <div v-if="product.updated" class="info-item">
        <label class="text-sm font-medium text-slate-600">Last Updated</label>
        <div class="mt-1 text-slate-800">{{ new Date(product.updated).toLocaleDateString() }}</div>
      </div>

      <!-- Additional product details -->
      <div class="info-item">
        <label class="text-sm font-medium text-slate-600">Product ID</label>
        <div class="mt-1 text-slate-500 text-sm font-mono">{{ product.id }}</div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.product-details {
  padding: 1rem;
}

.product-header {
  border-bottom: 1px solid #e2e8f0;
  padding-bottom: 1rem;
}

.product-avatar {
  flex-shrink: 0;
}

.product-info .info-item {
  padding: 0.75rem 0;
}

.info-item {
  padding: 0.75rem 0;
}

.info-item label {
  display: block;
  margin-bottom: 0.25rem;
}
</style> 