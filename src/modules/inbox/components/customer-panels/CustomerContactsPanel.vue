<script setup lang="ts">
import { computed, onMounted, watch } from 'vue'
import { useMemberStore } from '@/stores/member'
import type { MemberUser } from '@/composables/services/useMemberAPI'

const props = defineProps<{
  issue: any
}>()

const emit = defineEmits<{
  (e: 'contact-click', contact: any): void
}>()

const memberStore = useMemberStore()

const memberId = computed(() => props.issue?.member?.id)
const locationId = computed(() => props.issue?.location?.id)
const orgId = computed(() => props.issue?.member?.context_org_id)

// Map MemberUser to Contact interface for display
const contacts = computed(() => {
  return memberStore.memberUsers.map(user => ({
    id: user.id,
    name: user.full_name || user.public_name || `${user.first_name} ${user.last_name}`.trim(),
    title: '', // Could be added if available in the API response
    department: '', // Could be added if available in the API response
    email: user.email,
    phone: '', // Could be added if available in the API response
    status: user.active ? 'Active' : 'Inactive'
  }))
})

const loadingContacts = computed(() => memberStore.loadingMemberUsers)
const contactsError = computed(() => memberStore.memberUsersError)

async function loadMemberUsers() {
  if (memberId.value && locationId.value && orgId.value) {
    console.log('CustomerContactsPanel: Loading member users for:', {
      members_id: memberId.value,
      members_locations_id: locationId.value,
      context_org_id: orgId.value
    })
    
    try {
      await memberStore.fetchMemberUsers({
        members_id: memberId.value,
        members_locations_id: locationId.value,
        context_org_id: orgId.value
      })
    } catch (error) {
      console.error('CustomerContactsPanel: Error loading member users:', error)
    }
  }
}

// Watch for changes in the required IDs and reload contacts
watch([memberId, locationId, orgId], () => {
  loadMemberUsers()
}, { immediate: false })

onMounted(() => {
  loadMemberUsers()
})
</script>

<template>
  <div class="py-2 flex flex-col gap-2">
    <div v-if="loadingContacts" class="text-slate-500 italic">Loading contacts...</div>
    <div v-else-if="contactsError" class="text-red-500">{{ contactsError }}</div>
    <template v-else-if="contacts.length">
      <div 
        v-for="contact in contacts" 
        :key="contact.id"
        class="bg-white border border-slate-200 rounded p-3 cursor-pointer hover:bg-slate-50 transition-colors"
        @click="emit('contact-click', contact)"
      >
        <div class="flex items-start justify-between">
          <div>
            <div class="font-medium text-slate-700">{{ contact.name }}</div>
            <div class="text-sm text-slate-500 mt-1">
              <span v-if="contact.title" class="mr-2">{{ contact.title }}</span>
              <span v-if="contact.department">{{ contact.department }}</span>
            </div>
          </div>
          <div class="flex items-center gap-2">
            <span 
              :class="[
                'rounded px-2 py-0.5 text-xs font-semibold',
                contact.status === 'Active' ? 'bg-green-100 text-green-600' : 'bg-slate-100 text-slate-600'
              ]"
            >
              {{ contact.status || 'Unknown' }}
            </span>
          </div>
        </div>
        <div class="mt-2 flex flex-wrap gap-4 text-xs text-slate-500">
          <div v-if="contact.email">
            <span class="font-medium">Email:</span> 
            <a :href="`mailto:${contact.email}`" class="text-blue-600 hover:text-blue-800">
              {{ contact.email }}
            </a>
          </div>
          <div v-if="contact.phone">
            <span class="font-medium">Phone:</span> {{ contact.phone }}
          </div>
        </div>
      </div>
    </template>
    <div v-else class="text-slate-500 italic">No contacts available</div>
  </div>
</template> 