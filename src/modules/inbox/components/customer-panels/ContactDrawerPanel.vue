<script setup lang="ts">
import But<PERSON> from 'primevue/button'

const props = defineProps<{
  contact: any
}>()

const emit = defineEmits<{
  'email-contact': [email: string]
  'call-contact': [phone: string]
}>()

const handleEmailContact = (email: string) => {
  emit('email-contact', email)
}

const handleCallContact = (phone: string) => {
  emit('call-contact', phone)
}
</script>

<template>
  <div v-if="contact" class="contact-details">
    <!-- Contact Header -->
    <div class="contact-header mb-6">
      <div class="flex items-center gap-3 mb-3">
        <div class="contact-avatar">
          <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
            <span class="text-blue-600 font-semibold text-lg">
              {{ contact.name.charAt(0).toUpperCase() }}
            </span>
          </div>
        </div>
        <div>
          <h4 class="text-xl font-semibold text-slate-800">{{ contact.name }}</h4>
          <div class="flex items-center gap-2 mt-1">
            <span 
              :class="[
                'rounded px-2 py-0.5 text-xs font-semibold',
                contact.status === 'Active' ? 'bg-green-100 text-green-600' : 'bg-slate-100 text-slate-600'
              ]"
            >
              {{ contact.status }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Contact Information -->
    <div class="contact-info space-y-4">
      <div v-if="contact.email" class="info-item">
        <label class="text-sm font-medium text-slate-600">Email</label>
        <div class="mt-1">
          <a 
            :href="`mailto:${contact.email}`" 
            class="text-blue-600 hover:text-blue-800 underline"
          >
            {{ contact.email }}
          </a>
        </div>
      </div>

      <div v-if="contact.phone" class="info-item">
        <label class="text-sm font-medium text-slate-600">Phone</label>
        <div class="mt-1 text-slate-800">{{ contact.phone }}</div>
      </div>

      <div v-if="contact.title" class="info-item">
        <label class="text-sm font-medium text-slate-600">Title</label>
        <div class="mt-1 text-slate-800">{{ contact.title }}</div>
      </div>

      <div v-if="contact.department" class="info-item">
        <label class="text-sm font-medium text-slate-600">Department</label>
        <div class="mt-1 text-slate-800">{{ contact.department }}</div>
      </div>

      <!-- Additional contact details -->
      <div class="info-item">
        <label class="text-sm font-medium text-slate-600">Contact ID</label>
        <div class="mt-1 text-slate-500 text-sm font-mono">{{ contact.id }}</div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="contact-actions mt-8 pt-6 border-t border-slate-200">
      <div class="flex flex-col gap-2">
        <Button 
          v-if="contact.email"
          icon="pi pi-envelope" 
          label="Send Email" 
          class="w-full"
          @click="() => handleEmailContact(contact.email)"
        />
        <Button 
          v-if="contact.phone"
          icon="pi pi-phone" 
          label="Call Contact" 
          severity="secondary"
          class="w-full"
          @click="() => handleCallContact(contact.phone)"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.contact-details {
  padding: 1rem;
}

.info-item {
  padding: 0.75rem 0;
}

.info-item label {
  display: block;
  margin-bottom: 0.25rem;
}

.contact-actions {
  margin-top: 2rem;
}

.contact-header {
  border-bottom: 1px solid #e2e8f0;
  padding-bottom: 1rem;
}

.contact-avatar {
  flex-shrink: 0;
}
</style> 