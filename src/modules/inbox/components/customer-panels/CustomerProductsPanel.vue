<script setup lang="ts">
import { computed, onMounted, watch } from 'vue'
import { useMemberStore } from '@/stores/member'

const props = defineProps<{
  issue: any
}>()

const emit = defineEmits<{
  'product-click': [product: any]
}>()

const memberStore = useMemberStore()

const memberId = computed(() => props.issue?.member?.id)
const locationId = computed(() => props.issue?.location?.id)
const orgId = computed(() => props.issue?.member?.context_org_id)

async function loadMemberProducts() {
  if (memberId.value && locationId.value && orgId.value) {
    await memberStore.fetchMemberProducts(memberId.value, locationId.value, orgId.value)
  }
}

watch(memberId, () => {
  loadMemberProducts()
})

onMounted(() => {
  loadMemberProducts()
})

const handleProductClick = (product: any) => {
  emit('product-click', product)
}
</script>

<template>
  <div class="py-2 flex flex-col gap-2">
    <div v-if="memberStore.loadingProducts" class="text-slate-500 italic">Loading products...</div>
    <div v-else-if="memberStore.productsError" class="text-red-500">{{ memberStore.productsError }}</div>
    <template v-else-if="memberStore.memberProducts.length">
      <div 
        v-for="product in memberStore.memberProducts" 
        :key="product.id"
        class="bg-white border border-slate-200 rounded p-3 cursor-pointer hover:bg-slate-50 hover:border-slate-300 transition-colors"
        @click="handleProductClick(product)"
      >
        <div class="flex items-start justify-between">
          <div>
            <div class="font-medium text-slate-700">{{ product.label || product.lbl }}</div>
            <div class="text-sm text-slate-500 mt-1">
              <span v-if="product.make" class="mr-2">{{ product.make }}</span>
              <span v-if="product.model">{{ product.model }}</span>
            </div>
          </div>
          <div class="flex items-center gap-2">
            <span 
              :class="[
                'rounded px-2 py-0.5 text-xs font-semibold',
                product.c__d_status === 'Ok' ? 'bg-green-100 text-green-600' :
                product.c__d_status === 'Unknown' ? 'bg-yellow-100 text-yellow-600' :
                'bg-red-100 text-red-600'
              ]"
            >
              {{ product.c__d_status || 'Unknown' }}
            </span>
            <span 
              :class="[
                'rounded px-2 py-0.5 text-xs font-semibold',
                product.c__d_active === 'Yes' ? 'bg-green-100 text-green-600' : 'bg-slate-100 text-slate-600'
              ]"
            >
              {{ product.c__d_active || 'No' }}
            </span>
          </div>
        </div>
        <div class="mt-2 flex flex-wrap gap-4 text-xs text-slate-500">
          <div v-if="product.created">
            <span class="font-medium">Created:</span> {{ new Date(product.created).toLocaleDateString() }}
          </div>
          <div v-if="product.updated">
            <span class="font-medium">Updated:</span> {{ new Date(product.updated).toLocaleDateString() }}
          </div>
          <div v-if="product.c__d_type">
            <span class="font-medium">Type:</span> {{ product.c__d_type }}
          </div>
        </div>
      </div>
    </template>
    <div v-else class="text-slate-500 italic">No products available</div>
  </div>
</template> 