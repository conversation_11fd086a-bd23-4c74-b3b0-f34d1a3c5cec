<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router'
import TaskDetail from '@/modules/inbox/components/TaskDetail.vue'

const route = useRoute()
const router = useRouter()

function handleBack() {
  router.back()
}
</script>

<template>
  <div class="view-card">
    <div class="view-content">
      <TaskDetail :key="String(route.params.id ?? '')" @back="handleBack" />
    </div>
  </div>
</template>

<style scoped>
.view-card {
  background: white;
  border-radius: 0;
  box-shadow: none;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  margin-top: 0;
  padding-top: 0;
  width: 100%;
  min-width: 0;
}

.view-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  margin-top: 0;
  padding-top: 0;
  flex: 1;
  min-width: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .view-card {
    margin: 0;
    border-radius: 0;
  }
  
  .view-content {
    padding: 0;
  }
}
</style>