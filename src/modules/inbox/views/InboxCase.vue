<script setup lang="ts">
import { ref, onMounted, computed, watchEffect, onBeforeUnmount } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useCasesStore } from '../../../stores/cases'
import { useToast } from 'primevue/usetoast'
import BravoProgressSpinner from '@services/ui-component-library/components/BravoProgressSpinner.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoMenu from '@services/ui-component-library/components/BravoMenu.vue'
import type { MenuItem } from 'primevue/menuitem'
import type { Issue } from '../../../services/IssuesAPI'
import CaseActivity from '../components/CaseActivity.vue'
import CaseDetails from '../components/CaseDetails.vue'
import InboxCaseHeader from '../components/InboxCaseHeader.vue'
import CaseNavSidebar from '../components/CaseNavSidebar.vue'
import CaseCustomerDetails from '../components/CaseCustomerDetails.vue'
import CaseKnowledgeBase from '../components/CaseKnowledgeBase.vue'
import CaseApplications from '../components/CaseApplications.vue'
import ResolveCaseModal from '../components/ResolveCaseModal.vue'
import DeleteCaseModal from '../components/DeleteCaseModal.vue'
import ReopenCaseModal from '../components/ReopenCaseModal.vue'
import EscalateCaseModal from '../components/EscalateCaseModal.vue'
import DeEscalateCaseModal from '../components/DeEscalateCaseModal.vue'
import AddFileModal from '../components/AddFileModal.vue'
import BravoZeroStateScreen from '@services/ui-component-library/components/BravoZeroStateScreen.vue'
import ZeroStateSearchSvg from '@/assets/zero-state-search.svg'

const route = useRoute()
const router = useRouter()
const casesStore = useCasesStore()
const toast = useToast()
const cases = ref<Issue[]>([])
const caseDetails = ref<Issue | null>(null)
const isLoading = ref(false)
const error = ref<string | null>(null)

const viewId = computed(() => route.params.id as string)

// Find the view details in the store based on the route param ID
const viewDetails = computed(() => {
  const storeWithViews = casesStore as any
  const views = storeWithViews.views || []
  return views.find((view: any) => view.id === viewId.value)
})

const issueId = computed(() => route.params.id as string)
const isIssueLoading = ref(false)
const issueError = ref<string | null>(null)

const currentIssue = computed(() => {
  return casesStore.currentIssue && casesStore.currentIssue.id === issueId.value
    ? casesStore.currentIssue
    : null
})

const isFavorite = ref(false)
const showNoteModal = ref(false)
const showResolveModal = ref(false)
const showCancelModal = ref(false)
const showDeleteModal = ref(false)
const showReopenModal = ref(false)
const showUnresolvedModal = ref(false)
const showEscalateModal = ref(false)
const showDeEscalateModal = ref(false)
const showAddFileModal = ref(false)
const resolutionType = ref<'resolve' | 'cancel' | 'unresolved'>('resolve')

function toggleFavorite() {
  isFavorite.value = !isFavorite.value
}
function openNoteModal() {
  showNoteModal.value = true
}
function closeNoteModal() {
  showNoteModal.value = false
}
function handleResolveAction(action: string) {
  if (action === 'resolve') {
    resolutionType.value = 'resolve'
    showResolveModal.value = true
  } else if (action === 'cancel') {
    resolutionType.value = 'cancel'
    showCancelModal.value = true
  } else if (action === 'unresolved') {
    showUnresolvedModal.value = true
  } else if (action === 'reopen') {
    showReopenModal.value = true
  } else if (action === 'delete') {
    showDeleteModal.value = true
  } else if ( action === 'customer-changed' ) {
    // do nothing
  } else if (action === 'wait' || action === 'ready') {
    // These actions are handled by the InboxCaseHeader component
    // No need to show an alert or do anything here
    console.log(`Case action '${action}' handled by InboxCaseHeader`)
  } else {
    // Placeholder for other resolve actions
    alert('Resolve action: ' + action)
  }
}

async function handleResolveConfirm(resolutionNotes: string, type: 'resolve' | 'cancel' | 'unresolved') {
  if (!currentIssue.value) return
  
  try {
    let resolutionValue: string
    if (type === 'cancel') {
      resolutionValue = '3' // cancelled
    } else if (type === 'unresolved') {
      resolutionValue = '2' // unresolved
    } else {
      resolutionValue = '1' // completed/resolved
    }
    
    const updateParams = {
      id: currentIssue.value.id,
      resolution: resolutionValue,
      isResolved: 1,
      idr_resolution: resolutionNotes
    }
    
    const updatedIssue = await casesStore.updateCase(updateParams)
    
    console.log(`Case ${type} successfully:`, updatedIssue)
    
  } catch (err) {
    console.error(`Error setting case to ${type}:`, err)
    throw err // Re-throw to let modal handle the error
  }
}

// TODO: Remove these cancel handlers - no longer needed with prop-based approach
function handleResolveCancel() {
  showResolveModal.value = false
  showCancelModal.value = false
  showUnresolvedModal.value = false
}

async function handleReopenConfirm() {
  if (!currentIssue.value) return
  
  try {
    await casesStore.reopenCase(currentIssue.value.id)
    
  } catch (error) {
    console.error('Failed to reopen case:', error)
    throw error
  }
}

function handleReopenCancel() {
  showReopenModal.value = false
}

async function handleDeleteConfirm() {
  if (!currentIssue.value) return
  
  const caseId = currentIssue.value.id // Store the ID before deletion
  
  try {
    await casesStore.deleteCase({ id: caseId })
    
    console.log('Case deleted successfully:', caseId)
    
    // Navigate back to inbox immediately, preserving the view parameter if it exists
    const redirectQuery = route.query.view ? { view: route.query.view } : {}
    router.push({ name: 'inbox', query: redirectQuery })
    
    toast.add({
      severity: 'success',
      summary: 'Case Deleted',
      detail: 'The case has been deleted successfully.',
      life: 3000
    })
    
  } catch (err) {
    console.error('Error deleting case:', err)
    throw err // Re-throw to let modal handle the error
  }
}

function handleDeleteCancel() {
  showDeleteModal.value = false
}

const fetchIssueIfNeeded = async () => {
  if (!currentIssue.value && issueId.value) {
    isIssueLoading.value = true
    issueError.value = null
    try {
      await casesStore.fetchCurrentIssue(issueId.value)
      console.log('currentIssue', casesStore.currentIssue)
    } catch (err) {
      console.error('Error fetching issue:', err)
      // Check if it's a 404 error or case not found
      const errorMessage = err instanceof Error ? err.message : 'Failed to load case'
      if (errorMessage.includes('404') || errorMessage.includes('not found') || errorMessage.includes('Not Found') || errorMessage.includes('Failed to fetch')) {
        issueError.value = 'Case not found - it may have been deleted or you may not have permission to access it'
      } else {
        issueError.value = errorMessage
      }
    } finally {
      isIssueLoading.value = false
    }
  }
}

onMounted(fetchIssueIfNeeded)
onMounted(async () => {
  // Schema is now fetched automatically when we fetch the issue with fetchCurrentIssue
  console.log('InboxCase mounted - schema will be fetched with issue data')
})
watchEffect(fetchIssueIfNeeded)

const panelParam = computed(() => route.query.panel)
const detailsComponent = computed(() => {
  switch (panelParam.value) {
    case 'customer':
      return CaseCustomerDetails
    case 'knowledge':
      return CaseKnowledgeBase
    case 'applications':
      return CaseApplications
    default:
      return CaseDetails
  }
})

function handleEscalate() {
  showEscalateModal.value = true
}

function handleDeEscalate() {
  showDeEscalateModal.value = true
}

function handleAddFile() {
  showAddFileModal.value = true
}

async function handleEscalateConfirm(params: {
  escalationTeamId: string
  escalationUserId?: string
  escalationReasonId: string
  escalationNotes?: string
}) {
  if (!currentIssue.value) return
  
  try {
    await casesStore.escalateCase({
      id: currentIssue.value.id,
      escalated: true,
      _escalation_team_id: params.escalationTeamId,
      _escalation_user_id: params.escalationUserId,
      escalation_reasons: params.escalationReasonId,
      escalation_notes: params.escalationNotes
    })
    
  } catch (error) {
    console.error('Failed to escalate case:', error)
    throw error
  }
}

async function handleDeEscalateConfirm(params: {
  deEscalationTeamId: string
  deEscalationUserId?: string
  deEscalationNotes?: string
}) {
  if (!currentIssue.value) return
  
  try {
    await casesStore.deEscalateCase({
      id: currentIssue.value.id,
      escalated: false,
      _escalation_team_id: '', // Empty for de-escalation
      _escalation_user_id: '', // Empty for de-escalation
      escalation_reasons: '', // Empty for de-escalation
      escalation_notes: '', // Empty for de-escalation
      _deescalation_team_id: params.deEscalationTeamId,
      _deescalation_user_id: params.deEscalationUserId,
      deescalation_notes: params.deEscalationNotes
    })
    
  } catch (error) {
    console.error('Failed to de-escalate case:', error)
    throw error
  }
}

async function handleAddFileConfirm(file: File, fileTag: string) {
  if (!currentIssue.value) return
  
  try {
    await casesStore.uploadFile(file, currentIssue.value.id, fileTag)
    
    toast.add({
      severity: 'success',
      summary: 'File Uploaded',
      detail: `${file.name} has been uploaded successfully.`,
      life: 3000
    })
    
  } catch (error) {
    console.error('Failed to upload file:', error)
    toast.add({
      severity: 'error',
      summary: 'Upload Failed',
      detail: 'Failed to upload file. Please try again.',
      life: 5000
    })
    throw error
  }
}

// Clean up panel parameter when leaving the component
onBeforeUnmount(() => {
  const query = { ...route.query }
  delete query.panel
  router.replace({ query })
})

</script>

<template>
  <div class="inbox-content">
    <div class="main-content">
      <!-- Error State -->
      <div v-if="issueError" class="error-state-container">
        <BravoZeroStateScreen
          title="Case Not Found"
          :message="issueError"
          :imageSrc="ZeroStateSearchSvg"
          imageAlt="Case not found"
          buttonLabel="Go Back to Inbox"
          :actionHandler="() => router.push({ name: 'inbox', query: route.query.view ? { view: route.query.view } : {} })"
        />
      </div>
      
      <!-- Case Content -->
      <template v-else>
        <InboxCaseHeader 
          :issue="currentIssue" 
          @resolve="showResolveModal = true"
          @reopen="showReopenModal = true"
          @resolve-action="handleResolveAction"
          @escalate="handleEscalate"
          @de-escalate="handleDeEscalate"
          @add-file="handleAddFile"
        />
        
        <div class="view-card">
          <div class="case-activity-panel">
            <CaseActivity :issue="currentIssue" :hide-title="true" class="w-full" />
          </div>
          <div class="case-details-panel">
            <div class="flex-1 overflow-auto">
              <component
                :is="detailsComponent"
                :issue="currentIssue"
                class="w-full"
              />
            </div>
          </div>
          <div class="case-nav-panel">
            <CaseNavSidebar />
          </div>
        </div>
      </template>

      <!-- Note Modal Placeholder -->
      <div v-if="currentIssue && showNoteModal" class="note-modal-backdrop">
        <div class="note-modal">
          <h3>Add Note</h3>
          <textarea rows="4" style="width:100%" placeholder="Type your note..."></textarea>
          <div class="modal-actions">
            <BravoButton label="Cancel" @click="closeNoteModal" severity="secondary" />
            <BravoButton label="Save" @click="closeNoteModal" severity="primary" />
          </div>
        </div>
      </div>

      <!-- Resolve Case Modal -->
      <ResolveCaseModal
        v-model:visible="showResolveModal"
        :issue="currentIssue"
        resolution-type="resolve"
        :on-submit="handleResolveConfirm"
      />

      <!-- Cancel Case Modal -->
      <ResolveCaseModal
        v-model:visible="showCancelModal"
        :issue="currentIssue"
        resolution-type="cancel"
        :on-submit="handleResolveConfirm"
      />

      <!-- Unresolved Case Modal -->
      <ResolveCaseModal
        v-model:visible="showUnresolvedModal"
        :issue="currentIssue"
        resolution-type="unresolved"
        :on-submit="handleResolveConfirm"
      />

      <!-- Delete Case Modal -->
      <DeleteCaseModal
        v-model:visible="showDeleteModal"
        :issue="currentIssue"
        :on-submit="handleDeleteConfirm"
      />

      <!-- Reopen Case Modal -->
      <ReopenCaseModal
        v-model:visible="showReopenModal"
        :issue="currentIssue"
        :on-submit="handleReopenConfirm"
      />

      <!-- Escalate Case Modal -->
      <EscalateCaseModal
        v-model:visible="showEscalateModal"
        :issue="currentIssue"
        :on-submit="handleEscalateConfirm"
      />

      <!-- De-Escalate Case Modal -->
      <DeEscalateCaseModal
        v-model:visible="showDeEscalateModal"
        :issue="currentIssue"
        :on-submit="handleDeEscalateConfirm"
      />

      <!-- Add File Modal -->
      <AddFileModal
        v-model:visible="showAddFileModal"
        :issue="currentIssue"
        :on-submit="handleAddFileConfirm"
        :upload-progress="casesStore.uploadProgress"
      />

    </div>
  </div>
</template>

<style scoped>
.inbox-content {
  display: flex;
  flex: 1;
  min-height: 0;
}

.main-content {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.view-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  display: grid;
  grid-template-columns: 1fr 1fr minmax(48px, 48px);
  grid-template-areas: "activity details nav";
  height: 100%;
  min-height: 0;
  min-width: 450px; /* Minimum width for the three-panel layout */
}

.case-activity-panel {
  grid-area: activity;
  min-width: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.case-details-panel {
  grid-area: details;
  min-width: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.case-nav-panel {
  grid-area: nav;
  width: 48px;
  flex-shrink: 0;
  overflow: hidden;
}

/* Responsive breakpoints - CaseNavSidebar always stays at 48px */
@media (max-width: 1600px) {
  .view-card {
    grid-template-columns: minmax(320px, 1fr) minmax(280px, 1fr) minmax(48px, 48px);
  }
}

@media (max-width: 1400px) {
  .view-card {
    grid-template-columns: minmax(300px, 1fr) minmax(260px, 1fr) minmax(48px, 48px);
  }
}

@media (max-width: 1200px) {
  .view-card {
    grid-template-columns: minmax(280px, 1fr) minmax(240px, 1fr) minmax(48px, 48px);
  }
}

@media (max-width: 1100px) {
  .view-card {
    grid-template-columns: minmax(260px, 1fr) minmax(220px, 1fr) minmax(48px, 48px);
  }
}

@media (max-width: 1000px) {
  .view-card {
    grid-template-columns: minmax(240px, 1fr) minmax(200px, 1fr) minmax(48px, 48px);
  }
}

@media (max-width: 900px) {
  .view-card {
    grid-template-columns: minmax(220px, 1fr) minmax(180px, 1fr) minmax(48px, 48px);
  }
}

@media (max-width: 850px) {
  .view-card {
    grid-template-columns: minmax(200px, 1fr) minmax(160px, 1fr) minmax(48px, 48px);
  }
}

@media (max-width: 800px) {
  .view-card {
    grid-template-columns: minmax(180px, 1fr) minmax(150px, 1fr) minmax(48px, 48px);
  }
}

/* Keep panels side by side even at smaller screens */
@media (max-width: 750px) {
  .view-card {
    grid-template-columns: 1fr 1fr;
    grid-template-areas: "activity details";
  }
  
  .case-nav-panel {
    display: none;
  }
}

@media (max-width: 600px) {
  .view-card {
    grid-template-columns: 1fr 1fr;
    grid-template-areas: "activity details";
  }
}

/* Only stack on very small mobile screens */
@media (max-width: 480px) {
  .view-card {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr auto;
    grid-template-areas: 
      "activity"
      "details";
  }
}

.view-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.note-modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.note-modal {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  padding: 2rem;
  min-width: 320px;
  max-width: 90vw;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  gap: 1rem;
  flex: 1;
  height: 100%;
}

.error-state-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 100%;
  padding: 2rem;
}

.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem;
  gap: 1rem;
  background-color: var(--red-50);
  color: var(--red-600);
  margin: 1rem;
  border-radius: 4px;
}

.no-cases {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  color: var(--surface-600);
  flex: 1;
}

.cases-list {
  list-style: none;
  padding: 0;
  margin: 0;
  overflow-y: auto;
  flex: 1;
}

.case-item {
  padding: 1rem;
  border-bottom: 1px solid var(--surface-100);
  transition: background-color 0.2s;
  cursor: pointer;
}

.case-item:hover {
  background-color: var(--surface-50);
}

.case-header {
  margin-bottom: 0.5rem;
}

.case-id {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--primary-color);
}

.case-details {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  font-size: 0.875rem;
}

.label {
  font-weight: 500;
  color: var(--surface-700);
}

.value {
  color: var(--surface-900);
}

.case-actions {
  margin-top: 1rem;
  display: flex;
  justify-content: flex-end;
}
</style>