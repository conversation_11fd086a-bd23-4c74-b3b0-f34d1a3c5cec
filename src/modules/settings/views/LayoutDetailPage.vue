<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useCMSStore } from '@/stores/cms';
import type { CMSLayout, SchemaField } from '@/composables/services/useCMSAPI';

import BravoTitlePage from '@services/ui-component-library/components/BravoTypography/BravoTitlePage.vue';
import BravoTitle1 from '@services/ui-component-library/components/BravoTypography/BravoTitle1.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import BravoProgressSpinner from '@services/ui-component-library/components/BravoProgressSpinner.vue';

const route = useRoute();
const router = useRouter();
const cmsStore = useCMSStore();

const layoutId = computed(() => route.params.id as string);
const currentLayout = ref<CMSLayout | null>(null);
const loading = ref(false);
const error = ref<string | null>(null);

// Map layout ID to appropriate fetch function
const fetchLayoutById = async (id: string) => {
  switch (id) {
    case '96f47064-16b3-428d-969c-79d561a286f6':
      return await cmsStore.fetchCaseLayout();
    case 'add9456-72a1-4d49-b43a-5b4278d3e6f3':
      return await cmsStore.fetchCustomerLayout();
    case 'f05d011a-6440-41d4-b904-8cc0ca3fe2b3':
      return await cmsStore.fetchLocationLayout();
    case '726a2762-13fd-440e-89a9-fe6410a7628c':
      return await cmsStore.fetchContactLayout();
    case 'users':
      return await cmsStore.fetchUsersLayout();
    case 'members_devices':
      return await cmsStore.fetchProductLayout();
    case 'issues-3':
      return await cmsStore.fetchCaseWorkOrderLayout();
    default:
      // Try to get from cached layouts first
      const cachedLayout = cmsStore.getLayoutById(id);
      if (cachedLayout) {
        return cachedLayout;
      }
      // If not in cache, try to fetch directly by ID
      return await cmsStore.fetchLayoutById(id);
  }
};

const goBack = () => {
  router.push('/settings/layouts');
};

const getFieldTypeDisplayName = (fieldType: string | undefined, type: number) => {
  if (fieldType) {
    return fieldType.charAt(0).toUpperCase() + fieldType.slice(1);
  }
  
  // Fallback to type number mapping
  const typeMap: Record<number, string> = {
    1: 'Input',
    2: 'Section',
    5: 'Complex'
  };
  
  return typeMap[type] || `Type ${type}`;
};

const getFieldRequiredText = (field: SchemaField) => {
  if (field.required) return 'Required';
  if (field.requiredResolve) return 'Required on Resolve';
  return 'Optional';
};

onMounted(async () => {
  loading.value = true;
  error.value = null;
  
  try {
    const layout = await fetchLayoutById(layoutId.value);
    currentLayout.value = layout;
  } catch (err) {
    console.error('Failed to fetch layout:', err);
    error.value = err instanceof Error ? err.message : 'Failed to fetch layout';
  } finally {
    loading.value = false;
  }
});
</script>

<template>
  <div class="layout-detail">
    <div class="header-container">
      <div class="header-content">
        <BravoButton 
          variant="text" 
          icon="pi pi-arrow-left" 
          @click="goBack"
          label="Back to Layouts"
          class="back-button"
        />
        <div>
          <BravoTitlePage>
            {{ currentLayout?.title || 'Layout Detail' }}
          </BravoTitlePage>
          <p v-if="currentLayout?.description" class="description">
            {{ currentLayout.description }}
          </p>
        </div>
      </div>
    </div>

    <div class="content-container">
      <BravoProgressSpinner v-if="loading" />
      
      <div v-else-if="error" class="error-message">
        <p>{{ error }}</p>
        <BravoButton 
          variant="primary" 
          label="Try Again" 
          @click="onMounted" 
        />
      </div>
      
      <div v-else-if="currentLayout" class="layout-content">
        <!-- Layout Info -->
        <div class="layout-info-card">
          <BravoTitle1>Layout Information</BravoTitle1>
          <div class="info-grid">
            <div class="info-item">
              <span class="label">ID:</span>
              <span class="value">{{ currentLayout.id }}</span>
            </div>
            <div class="info-item">
              <span class="label">Object:</span>
              <span class="value">{{ currentLayout.object }}</span>
            </div>
            <div class="info-item">
              <span class="label">Partner ID:</span>
              <span class="value">{{ currentLayout.partners_id }}</span>
            </div>
            <div class="info-item">
              <span class="label">Template:</span>
              <span class="value">{{ currentLayout.isTemplate ? 'Yes' : 'No' }}</span>
            </div>
            <div class="info-item">
              <span class="label">Total Fields:</span>
              <span class="value">{{ currentLayout.schemaFields?.length || 0 }}</span>
            </div>
          </div>
        </div>

        <!-- Schema Fields -->
        <div class="schema-fields-card">
          <BravoTitle1>Schema Fields</BravoTitle1>
          
          <div v-if="!currentLayout.schemaFields || currentLayout.schemaFields.length === 0" class="empty-state">
            <p>No schema fields found for this layout.</p>
          </div>
          
          <div v-else class="fields-list">
            <div 
              v-for="field in currentLayout.schemaFields" 
              :key="`${field.field}-${field.position}`"
              class="field-item"
            >
              <div class="field-header">
                <div class="field-main-info">
                  <h3 class="field-label">{{ field.lbl }}</h3>
                  <span class="field-name">{{ field.field }}</span>
                </div>
                <div class="field-meta">
                  <span class="field-type">{{ getFieldTypeDisplayName(field.fieldType, field.type) }}</span>
                  <span class="field-position">Position: {{ field.position }}</span>
                </div>
              </div>
              
              <div class="field-details">
                <div class="field-attributes">
                  <span 
                    class="attribute-badge" 
                    :class="{ 
                      'required': field.required, 
                      'required-resolve': field.requiredResolve && !field.required,
                      'optional': !field.required && !field.requiredResolve 
                    }"
                  >
                    {{ getFieldRequiredText(field) }}
                  </span>
                  <span v-if="field.readOnly" class="attribute-badge readonly">Read Only</span>
                </div>
                
                <div v-if="field.options && field.options.length > 0" class="field-options">
                  <span class="options-label">Options:</span>
                  <div class="options-list">
                    <span 
                      v-for="option in field.options" 
                      :key="option.id || option.val"
                      class="option-item"
                      :class="{ 'default': option.default }"
                    >
                      {{ option.lbl }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.layout-detail {
  max-width: 1200px;
}

.header-container {
  padding: 1.5rem 0;
  border-bottom: 1px solid var(--surface-border);
  margin-bottom: 2rem;
}

.header-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.back-button {
  align-self: flex-start;
}

.description {
  margin: 0.5rem 0 0 0;
  color: var(--text-color-secondary);
}

.content-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.error-message {
  padding: 2rem;
  text-align: center;
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 8px;
}

.layout-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.layout-info-card,
.schema-fields-card {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 8px;
  padding: 2rem;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem;
  background: var(--surface-ground);
  border-radius: 4px;
}

.info-item .label {
  font-weight: 600;
  color: var(--text-color-secondary);
}

.info-item .value {
  color: var(--text-color);
  font-family: monospace;
}

.empty-state {
  text-align: center;
  padding: 2rem;
  color: var(--text-color-secondary);
}

.fields-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1rem;
}

.field-item {
  border: 1px solid var(--surface-border);
  border-radius: 6px;
  padding: 1.5rem;
  background: var(--surface-ground);
}

.field-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.field-main-info {
  flex: 1;
}

.field-label {
  margin: 0 0 0.25rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-color);
}

.field-name {
  font-family: monospace;
  font-size: 0.9rem;
  color: var(--text-color-secondary);
  background: var(--surface-card);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.field-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.field-type {
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
  background: var(--primary-color);
  color: white;
  border-radius: 4px;
}

.field-position {
  font-size: 0.8rem;
  color: var(--text-color-secondary);
}

.field-details {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.field-attributes {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.attribute-badge {
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 500;
}

.attribute-badge.required {
  background: #fee;
  color: #c53030;
  border: 1px solid #fecaca;
}

.attribute-badge.required-resolve {
  background: #fef3cd;
  color: #92400e;
  border: 1px solid #fde68a;
}

.attribute-badge.optional {
  background: #f0fff4;
  color: #22543d;
  border: 1px solid #c6f6d5;
}

.attribute-badge.readonly {
  background: #f7fafc;
  color: #4a5568;
  border: 1px solid #e2e8f0;
}

.field-options {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.options-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-color-secondary);
}

.options-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.option-item {
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 4px;
  color: var(--text-color);
}

.option-item.default {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

@media (max-width: 768px) {
  .field-header {
    flex-direction: column;
    gap: 1rem;
  }
  
  .field-meta {
    align-items: flex-start;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
}
</style> 