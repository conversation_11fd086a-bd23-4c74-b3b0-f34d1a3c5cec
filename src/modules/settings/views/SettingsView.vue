<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import SettingsSidebar from '../components/SettingsSidebar.vue';
import SettingsGeneral from '../components/SettingsGeneral.vue';
import SettingsSecurity from '../components/SettingsSecurity.vue';
import SettingsWorkflows from '../components/SettingsWorkflows.vue';
import SettingsApis from '../components/SettingsApis.vue';
import SettingsForms from '../components/SettingsForms.vue';
import SettingsTeams from '../components/SettingsTeams.vue';
import SettingsUsers from '../components/SettingsUsers.vue';
import SettingsLayouts from '../components/SettingsLayouts.vue';
import SettingsCustomFields from '../components/SettingsCustomFields.vue';
import SettingsTaskTemplates from '../components/SettingsTaskTemplates.vue';
import SettingsAutomations from '../components/SettingsAutomations.vue';
import SettingsCommsChannels from '../components/SettingsCommsChannels.vue';
import SettingsCommTemplates from '../components/SettingsCommTemplates.vue';
import SettingsCommsQuickReplies from '../components/SettingsCommsQuickReplies.vue';
import SettingsKnowledgeLibraries from '../components/SettingsKnowledgeLibraries.vue';
import SettingsKnowledgeTokens from '../components/SettingsKnowledgeTokens.vue';
import SettingsRolesPermissions from '../components/SettingsRolesPermissions.vue';
import SettingsHome from '../components/SettingsHome.vue';
import SettingsEmailConfiguration from '../components/SettingsEmailConfiguration.vue';
import LayoutDetailPage from './LayoutDetailPage.vue';

const router = useRouter();
const route = useRoute();

// Default section when no URL parameter is provided
const DEFAULT_SECTION = 'home';

// Keep track of the active section
const activeSection = ref(DEFAULT_SECTION);

// Check if we're viewing a layout detail
const isLayoutDetail = computed(() => {
  return activeSection.value === 'layouts' && route.params.id;
});

// Handle section changes from the sidebar
const handleSectionChange = (sectionId: string) => {
  // Update the active section
  activeSection.value = sectionId;
  
  // Update the URL to reflect the selected section
  if (route.params.section !== sectionId) {
    router.push(`/settings/${sectionId}`);
  }
};

// Initialize the active section based on the URL when component mounts
onMounted(() => {
  // If there's a section parameter in the URL, use it
  if (route.params.section) {
    activeSection.value = route.params.section as string;
  } else if (route.path === '/settings') {
    // If we're on the base settings path with no section,
    // update the URL to include the default section
    router.replace(`/settings/${DEFAULT_SECTION}`);
  }
});

// Watch for route changes to update the active section
watch(
  () => route.params.section,
  (newSection) => {
    if (newSection) {
      activeSection.value = newSection as string;
    } else if (route.path === '/settings') {
      // If we're at the base settings path with no section,
      // redirect to the default section
      router.replace(`/settings/${DEFAULT_SECTION}`);
    }
  },
  { immediate: true }
);
</script>

<template>
  <div class="settings-view">
    <div class="settings-container">
      <SettingsSidebar class="sidebar" @change-section="handleSectionChange" />
      <div class="content">
        <!-- Display content based on active section -->
        <SettingsHome v-if="activeSection === 'home'" />
        <SettingsGeneral v-else-if="activeSection === 'general'" />
        <SettingsSecurity v-else-if="activeSection === 'security'" />
        <SettingsWorkflows v-else-if="activeSection === 'workflows'" />
        <SettingsApis v-else-if="activeSection === 'apis'" />
        <SettingsForms v-else-if="activeSection === 'forms'" />
        <SettingsTeams v-else-if="activeSection === 'teams'" />
        <SettingsUsers v-else-if="activeSection === 'users'" />
        <LayoutDetailPage v-else-if="activeSection === 'layouts' && route.params.id" />
        <SettingsLayouts v-else-if="activeSection === 'layouts'" />
        <SettingsCustomFields v-else-if="activeSection === 'custom-fields'" />
        <SettingsTaskTemplates v-else-if="activeSection === 'task-templates'" />
        <SettingsAutomations v-else-if="activeSection === 'automations'" />
        <SettingsCommsChannels v-else-if="activeSection === 'channels'" />
        <SettingsCommTemplates v-else-if="activeSection === 'templates'" />
        <SettingsCommsQuickReplies v-else-if="activeSection === 'quick-replies'" />
        <SettingsKnowledgeLibraries v-else-if="activeSection === 'libraries'" />
        <SettingsKnowledgeTokens v-else-if="activeSection === 'tokens'" />
        <SettingsRolesPermissions v-else-if="activeSection === 'roles-permissions'" />
        <SettingsEmailConfiguration v-else-if="activeSection === 'email-configuration'" />
        <div v-else class="placeholder-content">
          <h2>{{ activeSection.charAt(0).toUpperCase() + activeSection.slice(1).replace('-', ' ') }} Settings</h2>
          <p>This section is under development.</p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.settings-view {
  background: var(--surface-0);
}

.settings-container {
  display: flex;
}

.content {
  flex: 1;
  padding: 0rem 2rem 1.5rem 2rem;
}

.settings-content {
  max-width: 800px;
}

.setting-group {
  margin-top: 1.5rem;
  padding: 1.5rem;
  border: 1px solid var(--surface-border);
  border-radius: 8px;
}

.form-row {
  margin-bottom: 1rem;
}

.form-row label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.form-row input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--surface-border);
  border-radius: 4px;
}

.placeholder-content {
  background-color: var(--surface-50);
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
}
</style> 