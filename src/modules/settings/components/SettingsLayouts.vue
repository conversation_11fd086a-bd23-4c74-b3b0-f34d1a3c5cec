<script setup lang="ts">
import BravoTitlePage from '@services/ui-component-library/components/BravoTypography/BravoTitlePage.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import BravoProgressSpinner from '@services/ui-component-library/components/BravoProgressSpinner.vue';
import BravoDataTable from 'primevue/datatable';
import Column from 'primevue/column';
import { ref, onMounted } from 'vue';
import { useCMSStore } from '@/stores/cms';
import { useRouter } from 'vue-router';

const isEditing = ref(false);
const cmsStore = useCMSStore();
const router = useRouter();

const toggleEdit = () => {
  isEditing.value = !isEditing.value;
};

const navigateToLayout = (event: any) => {
  // Get the layout data from the event
  const layoutData = event.data;
  router.push(`/settings/layouts/${layoutData.id}`);
};

onMounted(async () => {
  try {
    await cmsStore.fetchAllLayouts();
  } catch (error) {
    console.error('Failed to fetch layouts:', error);
  }
});
</script>

<template>
  <div class="layouts-list">
    <div class="header">
        <BravoTitlePage>Layouts</BravoTitlePage>
      <div class="button-group">
        <BravoButton variant="primary" :label="isEditing ? 'Save' : 'Edit'" @click="toggleEdit" />
      </div>
    </div>
    
    <div v-if="cmsStore.loading" class="loading-container">
      <BravoProgressSpinner />
    </div>
      
    <div v-else-if="cmsStore.error" class="error-message">
      <p>Error loading layouts: {{ cmsStore.error }}</p>
    </div>
    
    <BravoDataTable 
      v-else
      :value="cmsStore.layouts"
      :columns="[]" 
      dataKey="id"
      :rowHover="true"
      :stripedRows="false"
      :showGridlines="false" 
      class="p-datatable-sm layouts-table" 
      :sortField="'title'"
      :sortOrder="1"
      removableSort
      :rows="25"
      :totalRecords="cmsStore.layouts.length"
      :paginator="cmsStore.layouts.length > 25"
      :lazy="false"
      paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown"
      :rowsPerPageOptions="[10, 25, 50]"
      scrollable
      scrollHeight="calc(100vh - 230px)"
      paginatorPosition="bottom"
      paginatorClassName="fixed-paginator"
      @rowClick="navigateToLayout"
    >
      <template #header>
        <div>
          <span>
            Showing {{ cmsStore.layouts.length }} layouts
          </span>
        </div>
      </template>
      
      <template #empty>
        <div>No layouts found</div>
      </template>
      
      <Column field="title" header="Layout Name" sortable style="min-width: 200px">
        <template #body="{ data }">
          <div class="layout-name-cell">
            <span class="layout-title">{{ data.title }}</span>
            <span class="layout-id">{{ data.id }}</span>
          </div>
        </template>
      </Column>
      
      <Column field="object" header="Object Type" sortable style="min-width: 120px">
        <template #body="{ data }">
          <span class="object-type">{{ data.object }}</span>
        </template>
      </Column>
      
      <Column field="description" header="Description" style="min-width: 250px">
        <template #body="{ data }">
          <span class="description-text">{{ data.description || 'No description available' }}</span>
        </template>
      </Column>
      
      <Column field="schemaFields" header="Fields" sortable style="min-width: 80px">
        <template #body="{ data }">
          <span class="field-count">{{ data.schemaFields?.length || 0 }}</span>
        </template>
      </Column>
      
      <Column field="isTemplate" header="Template" sortable style="min-width: 80px">
        <template #body="{ data }">
          <span class="template-status">{{ data.isTemplate ? 'Yes' : 'No' }}</span>
        </template>
      </Column>
      
      <Column field="partners_id" header="Partner" sortable style="min-width: 80px">
        <template #body="{ data }">
          <span class="partner-id">{{ data.partners_id }}</span>
        </template>
      </Column>
      
      <Column :exportable="false" style="width: 4rem">
        <template #body>
          <div class="flex align-items-center justify-content-center">
            <i class="pi pi-angle-right" style="color: var(--text-color-secondary);"></i>
          </div>
        </template>
      </Column>
    </BravoDataTable>
  </div>
</template>

<style scoped>
.layouts-list {
  padding: 1rem 2rem 0rem 2rem;
  display: flex;
  flex-direction: column;
  height: 100vh;
  box-sizing: border-box;
  max-height: 100vh;
  position: relative;
}

.header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  align-items: center;
  flex: 0 0 auto;
}

.button-group {
  display: flex;
  gap: .75rem;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  flex: 1;
}

.error-message {
  padding: 1rem;
  background-color: #fee;
  border: 1px solid #fcc;
  border-radius: 4px;
  color: #c00;
  margin-bottom: 1rem;
}

/* Make the data table take up available space and paginator fixed at bottom */
:deep(.p-datatable) {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: calc(100vh - 230px);
  position: relative;
}

:deep(.p-datatable-wrapper) {
  flex-grow: 1;
  overflow: auto;
  min-height: 200px;
  padding-bottom: 56px;
}

:deep(.p-paginator) {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--surface-a);
  z-index: 1;
}

:deep(.layouts-table) {
  .p-datatable-tbody > tr {
    cursor: pointer;
}

  .p-datatable-tbody > tr:hover {
    background-color: var(--surface-hover) !important;
  }
}

:deep(.p-datatable-table) {
  border-collapse: separate;
  border-spacing: 0;
  margin-bottom: 0 !important;
}

:deep(.p-datatable .p-datatable-header) {
  border: none !important;
}

:deep(.fixed-paginator) {
  position: absolute !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  margin: 0 !important;
}

.layout-name-cell {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.layout-title {
  font-weight: 600;
  color: var(--text-color);
}

.layout-id {
  font-size: 0.8rem;
  color: var(--text-color-secondary);
  font-family: monospace;
}

.object-type {
  font-family: monospace;
  font-size: 0.9rem;
  background: var(--surface-ground);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  color: var(--text-color);
}

.description-text {
  color: var(--text-color-secondary);
  font-size: 0.9rem;
}

.field-count {
  font-weight: 600;
  color: var(--primary-color);
}

.template-status {
  font-size: 0.9rem;
  color: var(--text-color-secondary);
}

.partner-id {
  font-family: monospace;
  font-size: 0.9rem;
  color: var(--text-color-secondary);
}
</style>
