import { http, HttpResponse, delay } from 'msw';
import { RESPONSE_DELAY_IN_MS } from '.';
import { FIXTURE_CHANGE_EVENT } from '../fixtures-manager';
import { getAuthMock } from '../data/fixtures';
import {
    mockUserAuthResponse,
    mockUserAuthWithExternalResponse,
    mockUserAuthSsoOnlyResponse,
    mockUserAuthMultipleSsoResponse,
    mockUserAuthFailedResponse
} from '../data/auth.mock';

// Define the structure of the auth mock data
interface AuthMockData {
    mockUserLoginResponse?: any;
    mockUserStatusResponse?: any;
    mockUserAuthResponse?: any;
    mockUserAuthWithExternalResponse?: any;
    mockUserAuthSsoOnlyResponse?: any;
    mockUserAuthMultipleSsoResponse?: any;
    mockUserAuthFailedResponse?: any;
    mockMetaPartnersResponse?: any;
    mockMetaTokensResponse?: any;
    [key: string]: any;
}

// Track if data has been loaded
let dataLoaded = false;

// Initialize handlers with empty mock data
let mockUserLoginResponse: any = {
    success: true,
    authenticated: true,
    csrf_token: 'mock-csrf-token-12345',
    message: 'Login successful',
};

let mockUserStatusResponse: any = {
    success: true,
    authenticated: true,
    csrf_token: 'mock-csrf-token-12345',
    users: {
        email: '<EMAIL>',
        first_name: 'Test',
        last_name: 'User',
        id: '123',
        perms: [
            'issue_view',
            'issue_edit',
            'issue_add',
            'cust_view',
            'cust_loc_edit',
            'cust_user_edit',
            'ship_view',
            'billing_view',
        ],
    },
};

// Initialize SSO auth responses
let userAuthResponse = mockUserAuthResponse;
let userAuthWithExternalResponse = mockUserAuthWithExternalResponse;
let userAuthSsoOnlyResponse = mockUserAuthSsoOnlyResponse;
let userAuthMultipleSsoResponse = mockUserAuthMultipleSsoResponse;
let userAuthFailedResponse = mockUserAuthFailedResponse;

let mockMetaPartnersResponse: any = {
    success: true,
    pl__all_partners: [
        {
            val: "A3C",
            id: "A3C",
            lbl: "Boomtown",
            avatar: "https://api1.goboomtown.com/avatar/partners/A3C/48",
            url_avatar: "https://api1.goboomtown.com/avatar/partners/A3C/48",
            canProvider: true,
            canRelate: true,
            canRoute: true,
            canSponsor: false,
            defaultPartner: false,
            defaultTeamId: "A3C-VR6",
            hasEcosystem: false,
            rootKbs: null
        },
        {
            val: "B4D",
            id: "B4D",
            lbl: "Partner Two",
            avatar: "https://api1.goboomtown.com/avatar/partners/B4D/48",
            url_avatar: "https://api1.goboomtown.com/avatar/partners/B4D/48",
            canProvider: true,
            canRelate: true,
            canRoute: false,
            canSponsor: true,
            defaultPartner: true,
            defaultTeamId: "B4D-ABC",
            hasEcosystem: true,
            rootKbs: null
        },
        {
            val: "C5E",
            id: "C5E",
            lbl: "Test Partner",
            avatar: "https://api1.goboomtown.com/avatar/partners/C5E/48",
            url_avatar: "https://api1.goboomtown.com/avatar/partners/C5E/48",
            canProvider: false,
            canRelate: true,
            canRoute: true,
            canSponsor: false,
            defaultPartner: false,
            defaultTeamId: "C5E-XYZ",
            hasEcosystem: false,
            rootKbs: null
        }
    ],
    pl__partners_users: [
        {
            val: "A3C",
            id: "A3C",
            lbl: "Boomtown"
        },
        {
            val: "B4D",
            id: "B4D",
            lbl: "Partner Two"
        }
    ],
    current_server_time: new Date().toISOString()
};

let mockMetaTokensResponse: any = {
    success: true,
    pl__tokens: [
        {
          "val": "8cd4202b-3c3c-4375-82a3-d5c35e323ce2",
          "id": "8cd4202b-3c3c-4375-82a3-d5c35e323ce2",
          "lbl": "8757",
          "content": "xyz",
          "creator_user_id": null
        },
        // Abbreviated for brevity - real data will come from fixtures
    ]
};

// Function to load mock data
async function loadMockData() {
    try {
        const mockData: AuthMockData = await getAuthMock();

        // Override defaults with loaded data if available
        mockUserLoginResponse = mockData.mockUserLoginResponse || mockUserLoginResponse;
        mockUserStatusResponse = mockData.mockUserStatusResponse || mockUserStatusResponse;
        mockMetaPartnersResponse = mockData.mockMetaPartnersResponse || mockMetaPartnersResponse;
        mockMetaTokensResponse = mockData.mockMetaTokensResponse || mockMetaTokensResponse;

        // Load SSO auth responses
        userAuthResponse = mockData.mockUserAuthResponse || userAuthResponse;
        userAuthWithExternalResponse = mockData.mockUserAuthWithExternalResponse || userAuthWithExternalResponse;
        userAuthSsoOnlyResponse = mockData.mockUserAuthSsoOnlyResponse || userAuthSsoOnlyResponse;
        userAuthMultipleSsoResponse = mockData.mockUserAuthMultipleSsoResponse || userAuthMultipleSsoResponse;
        userAuthFailedResponse = mockData.mockUserAuthFailedResponse || userAuthFailedResponse;
    } catch (error) {
        console.error('❌ Error loading auth mock data:', error);
    }
}

// Event handler for fixture changes
const handleFixtureChange = () => {
    loadMockData();
};

// Ensure data is loaded - only loads once if already loaded
export async function ensureMockDataLoaded(): Promise<void> {
    if (!dataLoaded) {
        await loadMockData();
        dataLoaded = true;
    }
}

// Initialize data loading
ensureMockDataLoaded()
    .catch(err => console.error('❌ Failed to load auth mock data:', err));

// Set up event listener for fixture changes
if (typeof window !== 'undefined') {
    // Only add listener in browser context
    window.removeEventListener(FIXTURE_CHANGE_EVENT, handleFixtureChange);
    window.addEventListener(FIXTURE_CHANGE_EVENT, handleFixtureChange);
}

// Mock email validation for getUserAuth
const validEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];
const hybridSsoEmails = ['<EMAIL>', '<EMAIL>'];
const ssoOnlyEmails = ['<EMAIL>'];
const multipleSsoEmails = ['<EMAIL>'];

export const authHandlers = [
    // Add a handler for POST login requests
    http.post('*/admin/v4/core/index.php', async ({ request }) => {
        const url = new URL(request.url);
        const action = url.searchParams.get('sAction');

        if (action === 'userLogin') {
            await delay(RESPONSE_DELAY_IN_MS);
            const formData = await request.formData();
            const email = formData.get('email')?.toString() || '';
            const password = formData.get('password')?.toString() || '';

            // Simple validation
            if (!email || !password || password === 'wrong') {
                return HttpResponse.json({ success: false, message: 'Invalid credentials' }, { status: 401 });
            }

            return HttpResponse.json(mockUserLoginResponse);
        }

        return new Response(null, { status: 404 });
    }),

    // Add a handler for POST getUserAuth requests for SSO checking
    http.post('*/admin/v4/core/', async ({ request }) => {
        const url = new URL(request.url);
        const action = url.searchParams.get('sAction');

        if (action === 'getUserAuth') {
            await delay(RESPONSE_DELAY_IN_MS);

            // Try to parse form data
            let email = '';
            try {
                const formData = await request.formData();
                email = formData.get('email')?.toString() || '';
            } catch (error) {
                // If formData fails, try to parse body directly
                const body = await request.text();
                const params = new URLSearchParams(body);
                email = params.get('email') || '';
            }

            // Check if it's a valid email
            if (!email) {
                return HttpResponse.json({ success: false, message: 'Email is required' }, { status: 400 });
            }

            // Return different responses based on the email
            if (validEmails.includes(email)) {
                return HttpResponse.json(userAuthResponse);
            } else if (hybridSsoEmails.includes(email)) {
                return HttpResponse.json(userAuthWithExternalResponse);
            } else if (ssoOnlyEmails.includes(email)) {
                return HttpResponse.json(userAuthSsoOnlyResponse);
            } else if (multipleSsoEmails.includes(email)) {
                return HttpResponse.json(userAuthMultipleSsoResponse);
            } else {
                return HttpResponse.json(userAuthFailedResponse);
            }
        }

        if (action === 'userResetPassword') {
            await delay(RESPONSE_DELAY_IN_MS);

            const formData = await request.formData();
            const email = formData.get('email')?.toString() || '';

            if (!email) {
                return HttpResponse.json({ success: false, message: 'Email is required' }, { status: 400 });
            }

            // Mock successful password reset
            return HttpResponse.json({
                success: true,
                message: 'Password reset instructions have been sent to your email.',
                csrf_token: 'mock-csrf-token-12345'
            });
        }

        if (action === 'userChangePassword') {
            await delay(RESPONSE_DELAY_IN_MS);

            const formData = await request.formData();
            const email = formData.get('email')?.toString() || '';
            const password = formData.get('password')?.toString() || '';
            const confirmPassword = formData.get('confirm_password')?.toString() || '';
            const token = formData.get('token')?.toString() || '';

            if (!email || !password || !confirmPassword || !token) {
                return HttpResponse.json({ 
                    success: false, 
                    message: 'All fields are required' 
                }, { status: 400 });
            }

            if (password !== confirmPassword) {
                return HttpResponse.json({ 
                    success: false, 
                    message: 'Passwords do not match' 
                }, { status: 400 });
            }

            // Mock successful password change
            return HttpResponse.json({
                success: true,
                message: 'Password has been successfully changed.',
                csrf_token: 'mock-csrf-token-12345'
            });
        }

        return new Response(null, { status: 404 });
    }),

    // Keep the existing GET handler for other operations
    http.get('*/admin/v4/core/', async ({ request }) => {
        const url = new URL(request.url);
        const action = url.searchParams.get('sAction');

        // Handle different actions based on the query parameter
        switch (action) {
            case 'userStatus':
                await delay(RESPONSE_DELAY_IN_MS);
                return HttpResponse.json(mockUserStatusResponse);

            case 'metaPartners':
                await delay(RESPONSE_DELAY_IN_MS);
                return HttpResponse.json(mockMetaPartnersResponse);

            case 'metaTokens':
                await delay(RESPONSE_DELAY_IN_MS);
                return HttpResponse.json(mockMetaTokensResponse);

            case 'logout':
                await delay(RESPONSE_DELAY_IN_MS);
                return HttpResponse.json({
                    success: true,
                    message: 'Successfully logged out',
                });

            default:
                return new Response(null, { status: 404 });
        }
    }),
];
