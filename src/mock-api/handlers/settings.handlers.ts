import { http, HttpResponse, delay } from 'msw';
import { getSettingsMock } from '../data/fixtures';
import { RESPONSE_DELAY_IN_MS } from '.';
import { FIXTURE_CHANGE_EVENT } from '../fixtures-manager';

// Define the structure of the settings mock data
interface SettingsMockData {
    mockGetSettingsResponse?: any;
    [key: string]: any;
}

// Track if data has been loaded
let dataLoaded = false;

// Initialize handlers with empty mock data
let mockGetSettingsResponse: any = {};

// Function to load mock data
async function loadMockData() {
    try {
        const mockData: SettingsMockData = await getSettingsMock();
        
        mockGetSettingsResponse = mockData.mockGetSettingsResponse || {};
    } catch (error) {
        console.error('❌ Error loading settings mock data:', error);
    }
}

// Event handler for fixture changes
const handleFixtureChange = () => {
    loadMockData();
};

// Ensure data is loaded - only loads once if already loaded
export async function ensureMockDataLoaded(): Promise<void> {
    if (!dataLoaded) {
        await loadMockData();
        dataLoaded = true;
    }
}

// Initialize data loading
ensureMockDataLoaded()
    .catch(err => console.error('❌ Failed to load settings mock data:', err));

// Set up event listener for fixture changes
if (typeof window !== 'undefined') {
    // Only add listener in browser context
    window.removeEventListener(FIXTURE_CHANGE_EVENT, handleFixtureChange);
    window.addEventListener(FIXTURE_CHANGE_EVENT, handleFixtureChange);
}

export const settingsHandlers = [
    // Handle general settings requests with URL parameters
    http.get('*/admin/v4/settings/', async ({ request }) => {
        const url = new URL(request.url);
        const action = url.searchParams.get('sAction');
        
        await delay(RESPONSE_DELAY_IN_MS);
        
        // Handle different actions
        if (action === 'viewsListing') {
            console.log('📋 Mock API: Serving viewsListing request');
            
            // Extract views from the general settings response if available
            if (mockGetSettingsResponse && mockGetSettingsResponse.relay_views) {
                return HttpResponse.json(mockGetSettingsResponse);
            }
            
            // Fallback if no views data is available
            return HttpResponse.json({
                success: true,
                relay_views: {
                    totalCount: 3,
                    results: [
                        {
                            id: 'view1',
                            label: 'All Cases',
                            type: 1,
                            object: 'issues'
                        },
                        {
                            id: 'view2',
                            label: 'Open Cases',
                            type: 2,
                            object: 'issues'
                        },
                        {
                            id: 'view3',
                            label: 'My Cases',
                            type: 3,
                            object: 'issues'
                        }
                    ]
                }
            });
        }
        
        // Default response for other settings actions
        return HttpResponse.json(mockGetSettingsResponse);
    }),

    // Mock getting user settings - general endpoint
    http.get('*/admin/v4/settings', async () => {
        await delay(RESPONSE_DELAY_IN_MS);
        return HttpResponse.json(mockGetSettingsResponse);
    }),

    // Mock updating user settings
    http.put('*/admin/v4/settings', async ({ request }) => {
        const updates = await request.json() as Record<string, any>;
        await delay(300);

        return HttpResponse.json({
            success: true,
            settings: {
                theme: 'light',
                notifications: true,
                language: 'en',
                timezone: 'UTC',
                email_preferences: {
                    daily_digest: true,
                    issue_updates: true,
                    marketing: false,
                },
                ...updates,
            },
        });
    }),
];
