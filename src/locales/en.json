{"common": {"welcome": "Welcome", "save": "Save", "saving": "Saving...", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "loading": "Loading...", "settings": "Settings", "details": "Details", "events": "Events", "back": "Back", "collapse": "Collapse", "expand": "Expand", "archive": "Archive", "unpublish": "Unpublish", "comments": "Comments", "success": "Success", "error": "Error", "clone": "<PERSON><PERSON>"}, "nav": {"home": "Home", "settings": "Settings", "profile": "Profile", "inbox": "Inbox", "search": "Search", "journeys": "Journeys", "knowledge": "Knowledge", "customers": "Customers", "analytics": "Analytics", "aistudio": "AI Studio", "logout": "Logout"}, "login": {"email": "Email", "password": "Password", "login": "<PERSON><PERSON>", "forgot_password": "Forgot your password?", "remember_me": "Remember me on this device", "use_sso": "Use single sign-on (SSO)", "or": "Or", "sign_in_with_google": "Sign in with Google", "sign_in_with_microsoft": "Sign in with Microsoft"}, "errors": {"something_went_wrong": "Something went wrong", "required_field": "This field is required"}, "knowledge": {"error": "Error", "knowledge": "Knowledge", "library": "Library", "libraries": "Libraries", "content": "Content", "new_label": "New Label", "new_article": "New Article", "new_template": "New Template", "templates": "Templates", "showing": "Showing", "article": "Article", "articles": "Articles", "template": "Template", "all_statuses": "All Statuses", "comments": "Comments", "draft": "Draft", "published": "Published", "unpublished": "Unpublished", "previously_published": "Previously published", "archived": "Archived", "internal": "Internal", "public": "Public", "ecosystem": "Ecosystem", "partner ecosystem": "Ecosystem", "unknown": "Unknown", "no_content_available": "No content available", "title": "Title", "status": "Status", "access": "Access", "views": "Views", "last_updated": "Last updated", "updated": "Updated", "actions": "Actions", "article_actions": "Article actions", "articles_selected": "Articles selected", "collapse": "Collapse", "expand": "Expand", "viewing": "Viewing", "article_information": "Article Information", "template_information": "Template Information", "system_information": "System Information", "related_articles": "Related Articles", "no_related_articles": "No related articles found", "discard_draft": "Discard draft", "discard_changes": "Discard changes", "export_to_pdf": "Export to PDF", "delete_article": "Delete article", "search_articles": "Search articles...", "search_templates": "Search templates...", "add_filter": "Add filter", "add_title": "Add a Title...", "add_subtitle": "Add a Subtitle...", "publish": "Publish", "publish_articles": "Publish articles", "unpublish_articles": "Unpublish articles", "restore": "Rest<PERSON>", "restore_as_draft": "<PERSON>ore as Draft", "edit_as_draft": "Edit as Draft", "edit": "Edit", "unpublish": "Unpublish", "unarchive": "Unarchive", "no_events": "No events found", "no_comments": "No comments found", "failed_to_resolve_comment": "Failed to resolve comment", "comment_resolved": "Comment resolved", "delete_article_confirmation_title": "Delete article", "delete_article_confirmation_message": "Are you sure you want to delete the article \"{title}\"?", "delete_article_success_title": "Article deleted", "delete_article_success_message": "The article \"{title}\" was successfully deleted.", "delete_article_error_title": "Error deleting article", "delete_article_error_message": "Failed to delete article: {error}", "discard_draft_confirmation_title": "Discard draft", "discard_draft_confirmation_message": "Are you sure you want to discard the draft \"{title}\"?", "discard_draft_success_title": "Draft discarded", "discard_draft_success_message": "The draft \"{title}\" was successfully discarded.", "discard_draft_error_title": "Error discarding draft", "discard_draft_error_message": "Failed to discard draft: {error}", "show_resolved_comments": "Show resolved comments", "hide_resolved_comments": "Hide resolved comments", "add_comment": "Add Comment", "add_comment_title": "Add comment", "no_comments_title": "No open comments", "no_comments_message": "Add a comment by clicking below", "failed_to_add_comment": "Failed to add comment", "delete_label": "Delete Label", "delete_label_confirmation_message": "Are you sure you want to delete the label \"{label}\"?", "delete_label_sub_message": "Any sub-labels under it will also be deleted.", "load_more": "Load More Events", "refresh": "Refresh", "articlesMenu": {"delete_article_error_title": "Error deleting article", "delete_article_error_message": "Failed to delete article: {error}"}, "actionsMenu": {"update_labels": "Update Labels", "update_access_controls": "Update Access Controls", "add_rem_products": "Add/Remove Products", "add_rem_tags": "Add/Remove Tags", "share_with_orgs": "Share with Organizations", "unshare_from_orgs": "Unshare from Organizations", "publish": "Publish", "unpublish": "Unpublish", "archive": "Archive", "share": "Share", "unshare": "Unshare", "tags": "Tags", "update": "Update"}, "filters": {}, "add_label": {"title": "Add Label", "label_name": "Label Name", "nest_under": "Nest Label Under", "add_label": "Add Label", "cancel": "Cancel", "save": "Save"}, "edit_label": {"title": "Edit Label", "label_name": "Label Name", "nest_under": "Nest Label Under", "icon": "Icon", "change_icon": "Change Icon", "remove_icon": "Remove Icon", "alt_text": "Alternative Text for Icon", "alt_text_placeholder": "Used primarily for screen readers"}}}