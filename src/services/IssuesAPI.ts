import type { <PERSON><PERSON>tat<PERSON>, FetchCasesParams, Issue, IssueResponse } from '@/composables/services/useIssuesAPI';

/**
 * @deprecated Use the useIssuesAPI composable from '@/composables/services' instead
 * This class is kept for backward compatibility but should not be used in new code
 */
class IssuesAPI {
  constructor() {
    console.warn('IssuesAPI is deprecated. Use useIssuesAPI composable from @/composables/services instead.');
  }

  private logDeprecationWarning(methodName: string): any {
    console.warn(`IssuesAPI.${methodName} is deprecated. Use useIssuesAPI composable from @/composables/services instead.`);
    return { success: false, message: 'Method deprecated. Use useIssuesAPI composable.' };
  }

  async fetchIssue(issueId: string): Promise<Issue> {
    return this.logDeprecationWarning('fetchIssue');
  }

  async fetchViews(): Promise<any> {
    return this.logDeprecationWarning('fetchViews');
  }

  async fetchCases(params: FetchCasesParams): Promise<Issue[]> {
    return this.logDeprecationWarning('fetchCases');
  }
}

export { IssuesAPI };
export type { CaseStatus, FetchCasesParams, Issue, IssueResponse };
export const issuesAPI = new IssuesAPI(); 