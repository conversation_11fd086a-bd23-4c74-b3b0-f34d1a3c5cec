import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useMetaAPI, type Partner, type FetchMembersUsersParams, type MembersUsersResponse, type FetchMemberProductsParams, type MemberProductsResponse, type FetchCannedResponsesParams, type CannedResponsesResponse } from '@/composables/services/useMetaAPI'

// Re-export Partner type for convenience
export type { Partner } from '@/composables/services/useMetaAPI'

export const useMetaStore = defineStore('meta', () => {
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const lastRefreshTime = ref<Date | null>(null)
  const metaData = ref<any>(null)
  const partnerMetaData = ref<any>(null)
  const cannedResponses = ref<any>(null)
  const allPartners = ref<Partner[]>([])

  // Computed properties for easy access to partner data
  const partnerIds = computed(() => allPartners.value.map(partner => partner.id))
  const partnerAvatars = computed(() => {
    const avatarMap: Record<string, string> = {}
    allPartners.value.forEach(partner => {
      avatarMap[partner.id] = partner.url_avatar || partner.avatar
    })
    return avatarMap
  })

  async function loadMetaData() {
    try {
      isLoading.value = true
      error.value = null
      
      const metaAPI = useMetaAPI()
      const response = await metaAPI.fetchMetaData()
      metaData.value = response
      lastRefreshTime.value = new Date()
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load meta data'
      console.error('Error loading meta data:', err)
    } finally {
      isLoading.value = false
    }
  }

  async function loadPartnerMetaData() {
    try {
      isLoading.value = true
      error.value = null
      
      const metaAPI = useMetaAPI()
      const response = await metaAPI.fetchPartnerMetaData()
      
      partnerMetaData.value = response
      
      // Extract and store pl__all_partners data
      if (response?.pl__all_partners && Array.isArray(response.pl__all_partners)) {
        allPartners.value = response.pl__all_partners as Partner[]
        console.log(`📊 Meta: Loaded ${allPartners.value.length} partners from pl__all_partners`)
      } else {
        console.warn('📊 Meta: No pl__all_partners found in response')
        allPartners.value = []
      }
      
      lastRefreshTime.value = new Date()
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load partner meta data'
      console.error('Error loading partner meta data:', err)
    } finally {
      isLoading.value = false
    }
  }

  function getPartnerLabelById(partnerId: string): string {
    if (!partnerMetaData.value || !partnerMetaData.value.pl__partners_users) {
        return '';
    }

    const partner = partnerMetaData.value.pl__partners_users.find((partner: any) => partner.id === partnerId);

    return partner ? partner.lbl || '' : '';
  }

  // Helper functions for pl__all_partners data
  function getPartnerById(partnerId: string): Partner | null {
    return allPartners.value.find(partner => partner.id === partnerId) || null
  }

  function getPartnerAvatarById(partnerId: string): string {
    const partner = getPartnerById(partnerId)
    return partner?.url_avatar || partner?.avatar || ''
  }

  function getPartnerNameById(partnerId: string): string {
    const partner = getPartnerById(partnerId)
    return partner?.lbl || ''
  }

  async function reload() {
    return Promise.all([loadMetaData(), loadPartnerMetaData()])
  }

  async function fetchMembersUsers(params: FetchMembersUsersParams): Promise<MembersUsersResponse> {
    try {
      const metaAPI = useMetaAPI()
      return await metaAPI.fetchMembersUsers(params)
    } catch (err) {
      console.error('Error fetching members users:', err)
      throw err
    }
  }

  async function fetchMemberProducts(params: FetchMemberProductsParams): Promise<MemberProductsResponse> {
    try {
      const metaAPI = useMetaAPI()
      return await metaAPI.fetchMemberProducts(params)
    } catch (err) {
      console.error('Error fetching member products:', err)
      throw err
    }
  }

  async function fetchCannedResponses(params: FetchCannedResponsesParams): Promise<CannedResponsesResponse> {
    try {
      const metaAPI = useMetaAPI()
      const response = await metaAPI.fetchCannedResponses(params)
      
      // Normalize the response to ensure we have pl__canned_responses
      if (response.results && !response.pl__canned_responses) {
        response.pl__canned_responses = response.results
      }
      
      cannedResponses.value = response
      return response
    } catch (err) {
      console.error('Error fetching canned responses:', err)
      throw err
    }
  }

  function clearMetaData() {
    metaData.value = null
    partnerMetaData.value = null
    cannedResponses.value = null
    allPartners.value = []
    error.value = null
    lastRefreshTime.value = null
  }

  return {
    isLoading,
    error,
    lastRefreshTime,
    metaData,
    partnerMetaData,
    cannedResponses,
    allPartners,
    partnerIds,
    partnerAvatars,
    loadMetaData,
    loadPartnerMetaData,
    getPartnerLabelById,
    getPartnerById,
    getPartnerAvatarById,
    getPartnerNameById,
    reload,
    fetchMembersUsers,
    fetchMemberProducts,
    fetchCannedResponses,
    clearMetaData
  }
});
