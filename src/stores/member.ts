import { defineStore } from 'pinia';
import { ref } from 'vue';
import type { MemberProduct, MemberLocation, MemberUser, FetchMemberUsersParams, UpdateMemberParams, UpdateMemberResponse, UpdateLocationParams, UpdateLocationResponse } from '@/composables/services/useMemberAPI';
import { useMemberAPI } from '@/composables/services/useMemberAPI';

export const useMemberStore = defineStore('member', () => {
  const memberProducts = ref<MemberProduct[]>([]);
  const loadingProducts = ref(false);
  const productsError = ref<string | null>(null);

  // Customer locations state
  const customerLocations = ref<MemberLocation[]>([]);
  const loadingCustomerLocations = ref(false);
  const customerLocationsError = ref<string | null>(null);

  // Member users state
  const memberUsers = ref<MemberUser[]>([]);
  const loadingMemberUsers = ref(false);
  const memberUsersError = ref<string | null>(null);

  // Update member state
  const loadingUpdateMember = ref(false);
  const updateMemberError = ref<string | null>(null);

  // Update location state
  const loadingUpdateLocation = ref(false);
  const updateLocationError = ref<string | null>(null);

  async function fetchMemberProducts(memberId: string, locationId: string, orgId: string) {
    loadingProducts.value = true;
    productsError.value = null;

    try {
      const memberAPI = useMemberAPI();
      memberProducts.value = await memberAPI.fetchMemberProducts({
        memberId,
        locationId,
        orgId,
        limit: 25
      });
      console.log('Member Store: Products set to:', memberProducts.value);
    } catch (err) {
      console.error('Member Store: Error in fetchMemberProducts:', err);
      productsError.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loadingProducts.value = false;
    }
  }

  async function searchCustomerLocations(query: string) {
    loadingCustomerLocations.value = true;
    customerLocationsError.value = null;

    try {
      const memberAPI = useMemberAPI();
      customerLocations.value = await memberAPI.searchCustomerLocations(query);
      console.log('Member Store: Customer locations set to:', customerLocations.value);
    } catch (err) {
      console.error('Member Store: Error in searchCustomerLocations:', err);
      customerLocationsError.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loadingCustomerLocations.value = false;
    }
  }

  function clearCustomerLocations() {
    customerLocations.value = [];
    customerLocationsError.value = null;
  }

  async function fetchMemberUsers(params: FetchMemberUsersParams) {
    loadingMemberUsers.value = true;
    memberUsersError.value = null;

    try {
      const memberAPI = useMemberAPI();
      memberUsers.value = await memberAPI.fetchMemberUsers(params);
      console.log('Member Store: Member users set to:', memberUsers.value);
      return memberUsers.value;
    } catch (err) {
      console.error('Member Store: Error in fetchMemberUsers:', err);
      memberUsersError.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loadingMemberUsers.value = false;
    }
  }

  async function updateMember(params: UpdateMemberParams): Promise<UpdateMemberResponse> {
    loadingUpdateMember.value = true;
    updateMemberError.value = null;

    try {
      const memberAPI = useMemberAPI();
      const response = await memberAPI.updateMember(params);
      console.log('Member Store: Member updated successfully:', response);
      return response;
    } catch (err) {
      console.error('Member Store: Error in updateMember:', err);
      updateMemberError.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loadingUpdateMember.value = false;
    }
  }

  async function updateLocation(params: UpdateLocationParams): Promise<UpdateLocationResponse> {
    loadingUpdateLocation.value = true;
    updateLocationError.value = null;

    try {
      const memberAPI = useMemberAPI();
      const response = await memberAPI.updateLocation(params);
      console.log('Member Store: Location updated successfully:', response);
      return response;
    } catch (err) {
      console.error('Member Store: Error in updateLocation:', err);
      updateLocationError.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loadingUpdateLocation.value = false;
    }
  }

  return {
    memberProducts,
    loadingProducts,
    productsError,
    fetchMemberProducts,
    customerLocations,
    loadingCustomerLocations,
    customerLocationsError,
    searchCustomerLocations,
    clearCustomerLocations,
    memberUsers,
    loadingMemberUsers,
    memberUsersError,
    fetchMemberUsers,
    loadingUpdateMember,
    updateMemberError,
    updateMember,
    loadingUpdateLocation,
    updateLocationError,
    updateLocation
  };
}); 