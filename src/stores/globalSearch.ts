import { defineStore } from 'pinia';
import { ref, computed, watch } from 'vue';
import { useSearchAPI, type SearchParams, type CombinedSearchResponse, type SearchResult, type SearchScope } from '@/composables/services/useSearchAPI';

export interface SearchHistoryItem {
  query: string;
  timestamp: Date;
  results: SearchResult[];
}

export const useGlobalSearchStore = defineStore('globalSearch', () => {
  // State
  const isModalVisible = ref(false);
  const searchQuery = ref('');
  const isSearching = ref(false);
  const searchResults = ref<SearchResult[]>([]);
  const searchHistory = ref<SearchHistoryItem[]>([]);
  const error = ref<string | null>(null);
  const lastSearchTime = ref<string>('');

  // Default search scopes
  const defaultScopes: SearchScope[] = ['kb', 'issues', 'members_locations', 'members_users'];

  // API instance
  const searchAPI = useSearchAPI();

  // Computed properties
  const hasResults = computed(() => searchResults.value.length > 0);
  const hasHistory = computed(() => searchHistory.value.length > 0);
  const recentSearches = computed(() => 
    searchHistory.value
      .slice(-5) // Get last 5 searches
      .reverse() // Most recent first
  );

  // Actions
  function openModal() {
    isModalVisible.value = true;
  }

  function closeModal() {
    isModalVisible.value = false;
    // Optionally clear search when closing
    // clearSearch();
  }

  function clearSearch() {
    searchQuery.value = '';
    searchResults.value = [];
    error.value = null;
  }

  function clearHistory() {
    searchHistory.value = [];
  }

  async function performSearch(query?: string, scopes?: SearchScope[]) {
    const searchTerm = query || searchQuery.value;
    const searchScopes = scopes || defaultScopes;

    if (!searchTerm.trim()) {
      clearSearch();
      return;
    }

    isSearching.value = true;
    error.value = null;

    try {
      const params: SearchParams = {
        query: searchTerm,
        scope: searchScopes,
        searchAll: true,
        limit: 5
      };

      const response: CombinedSearchResponse = await searchAPI.searchAll(params);

      if (response.success) {
        searchResults.value = response.results;
        lastSearchTime.value = response.currentServerTime;

        // Add to search history
        const historyItem: SearchHistoryItem = {
          query: searchTerm,
          timestamp: new Date(),
          results: response.results
        };

        // Remove duplicate queries from history
        searchHistory.value = searchHistory.value.filter(item => item.query !== searchTerm);
        
        // Add new search to history
        searchHistory.value.push(historyItem);

        // Keep only last 20 searches
        if (searchHistory.value.length > 20) {
          searchHistory.value = searchHistory.value.slice(-20);
        }
      } else {
        throw new Error('Search failed');
      }
    } catch (err) {
      console.error('Global Search Error:', err);
      error.value = err instanceof Error ? err.message : 'An error occurred during search';
      searchResults.value = [];
    } finally {
      isSearching.value = false;
    }
  }

  async function searchKnowledgeOnly(query?: string) {
    await performSearch(query, ['kb']);
  }

  async function searchIssuesOnly(query?: string) {
    await performSearch(query, ['issues']);
  }

  async function searchLocationsOnly(query?: string) {
    await performSearch(query, ['members_locations']);
  }

  async function searchUsersOnly(query?: string) {
    await performSearch(query, ['members_users']);
  }

  // searchDevicesOnly removed - no longer needed

  function selectHistoryItem(historyItem: SearchHistoryItem) {
    searchQuery.value = historyItem.query;
    searchResults.value = historyItem.results;
  }

  function getResultsByType(type: SearchScope): SearchResult | undefined {
    return searchResults.value.find(result => result.type === type);
  }

  function getTotalResultsCount(): number {
    return searchResults.value.reduce((total, result) => total + result.totalCount, 0);
  }

  function getDisplayLabel(type: SearchScope): string {
    const labels: Record<SearchScope, string> = {
      kb: 'Articles',
      issues: 'Cases',
      members_locations: 'Locations',
      members_users: 'Contacts'
    };
    return labels[type] || type;
  }

  function getDisplayIcon(type: SearchScope): string {
    const icons: Record<SearchScope, string> = {
      kb: 'pi pi-book',
      issues: 'pi pi-briefcase',
      members_locations: 'pi pi-map-marker',
      members_users: 'pi pi-users'
    };
    return icons[type] || 'pi pi-search';
  }

  // Persist search history to localStorage
  function saveHistoryToStorage() {
    try {
      localStorage.setItem('globalSearchHistory', JSON.stringify(searchHistory.value));
    } catch (error) {
      console.warn('Failed to save search history to localStorage:', error);
    }
  }

  function loadHistoryFromStorage() {
    try {
      const stored = localStorage.getItem('globalSearchHistory');
      if (stored) {
        const parsed = JSON.parse(stored);
        // Convert timestamp strings back to Date objects
        searchHistory.value = parsed.map((item: any) => ({
          ...item,
          timestamp: new Date(item.timestamp)
        }));
      }
    } catch (error) {
      console.warn('Failed to load search history from localStorage:', error);
    }
  }

  // Auto-save history when it changes
  function setupHistoryPersistence() {
    // Load history on store initialization
    loadHistoryFromStorage();

    // Save history whenever it changes
    const stopWatcher = watch(
      searchHistory,
      () => {
        saveHistoryToStorage();
      },
      { deep: true }
    );

    return stopWatcher;
  }

  // Initialize history persistence
  const stopHistoryWatcher = setupHistoryPersistence();

  return {
    // State
    isModalVisible,
    searchQuery,
    isSearching,
    searchResults,
    searchHistory,
    error,
    lastSearchTime,

    // Computed
    hasResults,
    hasHistory,
    recentSearches,

    // Actions
    openModal,
    closeModal,
    clearSearch,
    clearHistory,
    performSearch,
    searchKnowledgeOnly,
    searchIssuesOnly,
    searchLocationsOnly,
    searchUsersOnly,
    selectHistoryItem,
    getResultsByType,
    getTotalResultsCount,
    getDisplayLabel,
    getDisplayIcon,

    // Cleanup
    stopHistoryWatcher
  };
}); 