import { defineStore } from 'pinia';
import type { CopilotSearchResult } from '@/types/copilot';

export const useCopilotStore = defineStore('copilot', {
    state: () => {
        return {};
    },
    getters: {},
    actions: {
        async submitSearch(
            search: string, 
            onContentUpdate?: (content: string) => void
        ): Promise<CopilotSearchResult> {
            try {
                const data = {
                    message: search,
                };
                
                const url = 'https://genai.ovationcxm.ai/api/search/stream';
                
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data),
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const reader = response.body?.getReader();
                if (!reader) {
                    throw new Error('Failed to get response reader');
                }

                const decoder = new TextDecoder();
                let content = '';
                let metadata: any = null;
                let buffer = '';
                
                try {
                    while (true) {
                        const { done, value } = await reader.read();
                        
                        if (done) break;
                        
                        const chunk = decoder.decode(value, { stream: true });
                        buffer += chunk;
                        
                        // Split by lines and process each complete line
                        const lines = buffer.split('\n');
                        // Keep the last potentially incomplete line in buffer
                        buffer = lines.pop() || '';
                        
                        for (const line of lines) {
                            const trimmedLine = line.trim();
                            if (!trimmedLine) continue;
                            
                            try {
                                // Try parsing as direct JSON first
                                let parsed;
                                if (trimmedLine.startsWith('data: ')) {
                                    // Server-Sent Events format
                                    const jsonData = trimmedLine.slice(6);
                                    if (jsonData.trim() === '[DONE]') break;
                                    parsed = JSON.parse(jsonData);
                                } else {
                                    // Direct JSON format
                                    parsed = JSON.parse(trimmedLine);
                                }
                                
                                if (parsed.type === 'content') {
                                    content += parsed.content || '';
                                    // Call the callback with the updated content for real-time streaming
                                    if (onContentUpdate) {
                                        onContentUpdate(content);
                                    }
                                } else if (parsed.type === 'metadata') {
                                    metadata = parsed;
                                } else if (parsed.type === 'done') {
                                    break;
                                }
                            } catch (parseError) {
                                console.warn('Failed to parse line:', trimmedLine, parseError);
                            }
                        }
                    }
                    
                    // Process any remaining buffer
                    if (buffer.trim()) {
                        try {
                            const parsed = JSON.parse(buffer.trim());
                            if (parsed.type === 'content') {
                                content += parsed.content || '';
                                if (onContentUpdate) {
                                    onContentUpdate(content);
                                }
                            } else if (parsed.type === 'metadata') {
                                metadata = parsed;
                            }
                        } catch (parseError) {
                            console.warn('Failed to parse final buffer:', buffer, parseError);
                        }
                    }
                } finally {
                    reader.releaseLock();
                }

                // Use content if available, otherwise fall back to complete_message from metadata
                const finalMessage = content || metadata?.complete_message || '';

                return {
                    message: finalMessage,
                    sources: metadata?.source ? metadata.source.map((s: string) => ({
                        url: s
                    })) : [],
                    relatedLinks: metadata?.relatedLinks ? metadata.relatedLinks.map((r: string) => ({
                        url: r
                    })) : [],
                    triggers: metadata?.triggers || { esscalcateCase: false },
                };
            } catch (error) {
                console.error('Failed to submit search:', error);
                throw error;
            }
        },
    },
}); 