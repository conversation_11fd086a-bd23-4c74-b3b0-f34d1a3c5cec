import { defineStore } from 'pinia';
import { ref } from 'vue';
import { useCMSAPI, type CMSLayout } from '@/composables/services/useCMSAPI';

export const useCMSStore = defineStore('cms', () => {
  const layouts = ref<CMSLayout[]>([]);
  const caseLayout = ref<CMSLayout | null>(null);
  const customerLayout = ref<CMSLayout | null>(null);
  const locationLayout = ref<CMSLayout | null>(null);
  const contactLayout = ref<CMSLayout | null>(null);
  const usersLayout = ref<CMSLayout | null>(null);
  const productLayout = ref<CMSLayout | null>(null);
  const caseWorkOrderLayout = ref<CMSLayout | null>(null);
  const loading = ref(false);
  const error = ref<string | null>(null);

  const cmsAPI = useCMSAPI();

  /**
   * Fetch all CMS layouts for a specific partner
   */
  async function fetchAllLayouts(partnerId?: string) {
    loading.value = true;
    error.value = null;

    try {
      // Use provided partner ID or fall back to hardcoded default
      const partnerIdToUse = partnerId || 'H3F';
      const partnerSource = partnerId ? 'provided' : 'default fallback';

      const result = await cmsAPI.fetchLayouts({
        filter: [
          { property: 'id', value: '_no_filter_' },
          { property: 'partners_id', value: partnerIdToUse },
          { property: 'object', value: '_no_filter_' }
        ]
      });

      layouts.value = result.results;
      console.log(`CMS Store: Fetched all layouts for partner: ${partnerIdToUse} (${partnerSource}), Count: ${layouts.value.length}`);
      
      return result.results;
    } catch (err) {
      console.error('CMS Store: Error in fetchAllLayouts:', err);
      error.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Fetch the specific case layout for a partner
   */
  async function fetchCaseLayout(partnerId?: string) {
    loading.value = true;
    error.value = null;

    try {
      // Use provided partner ID or fall back to hardcoded default
      const partnerIdToUse = partnerId || 'H3F';
      const partnerSource = partnerId ? 'provided' : 'default fallback';

      const result = await cmsAPI.fetchLayouts({
        filter: [
          { property: 'id', value: '96f47064-16b3-428d-969c-79d561a286f6' },
          { property: 'partners_id', value: partnerIdToUse },
          { property: 'object', value: '_no_filter_' }
        ]
      });

      if (result.results.length > 0) {
        caseLayout.value = result.results[0];
        console.log(`CMS Store: Fetched case layout for partner: ${partnerIdToUse} (${partnerSource})`, caseLayout.value);
      } else {
        caseLayout.value = null;
        console.log(`CMS Store: No case layout found for partner: ${partnerIdToUse} (${partnerSource})`);
      }
      
      return caseLayout.value;
    } catch (err) {
      console.error('CMS Store: Error in fetchCaseLayout:', err);
      error.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Fetch the specific customer layout for a partner
   */
  async function fetchCustomerLayout(partnerId?: string) {
    loading.value = true;
    error.value = null;

    try {
      const partnerIdToUse = partnerId || 'H3F';
      const partnerSource = partnerId ? 'provided' : 'default fallback';

      const result = await cmsAPI.fetchLayouts({
        filter: [
          { property: 'id', value: 'add9456-72a1-4d49-b43a-5b4278d3e6f3' },
          { property: 'partners_id', value: partnerIdToUse },
          { property: 'object', value: '_no_filter_' }
        ]
      });

      if (result.results.length > 0) {
        customerLayout.value = result.results[0];
        console.log(`CMS Store: Fetched customer layout for partner: ${partnerIdToUse} (${partnerSource})`, customerLayout.value);
      } else {
        customerLayout.value = null;
        console.log(`CMS Store: No customer layout found for partner: ${partnerIdToUse} (${partnerSource})`);
      }
      
      return customerLayout.value;
    } catch (err) {
      console.error('CMS Store: Error in fetchCustomerLayout:', err);
      error.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Fetch the specific location layout for a partner
   */
  async function fetchLocationLayout(partnerId?: string) {
    loading.value = true;
    error.value = null;

    try {
      const partnerIdToUse = partnerId || 'H3F';
      const partnerSource = partnerId ? 'provided' : 'default fallback';

      const result = await cmsAPI.fetchLayouts({
        filter: [
          { property: 'id', value: 'f05d011a-6440-41d4-b904-8cc0ca3fe2b3' },
          { property: 'partners_id', value: partnerIdToUse },
          { property: 'object', value: '_no_filter_' }
        ]
      });

      if (result.results.length > 0) {
        locationLayout.value = result.results[0];
        console.log(`CMS Store: Fetched location layout for partner: ${partnerIdToUse} (${partnerSource})`, locationLayout.value);
      } else {
        locationLayout.value = null;
        console.log(`CMS Store: No location layout found for partner: ${partnerIdToUse} (${partnerSource})`);
      }
      
      return locationLayout.value;
    } catch (err) {
      console.error('CMS Store: Error in fetchLocationLayout:', err);
      error.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Fetch the specific contact layout for a partner
   */
  async function fetchContactLayout(partnerId?: string) {
    loading.value = true;
    error.value = null;

    try {
      const partnerIdToUse = partnerId || 'H3F';
      const partnerSource = partnerId ? 'provided' : 'default fallback';

      const result = await cmsAPI.fetchLayouts({
        filter: [
          { property: 'id', value: '726a2762-13fd-440e-89a9-fe6410a7628c' },
          { property: 'partners_id', value: partnerIdToUse },
          { property: 'object', value: '_no_filter_' }
        ]
      });

      if (result.results.length > 0) {
        contactLayout.value = result.results[0];
        console.log(`CMS Store: Fetched contact layout for partner: ${partnerIdToUse} (${partnerSource})`, contactLayout.value);
      } else {
        contactLayout.value = null;
        console.log(`CMS Store: No contact layout found for partner: ${partnerIdToUse} (${partnerSource})`);
      }
      
      return contactLayout.value;
    } catch (err) {
      console.error('CMS Store: Error in fetchContactLayout:', err);
      error.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Fetch the specific users layout for a partner
   */
  async function fetchUsersLayout(partnerId?: string) {
    loading.value = true;
    error.value = null;

    try {
      const partnerIdToUse = partnerId || 'H3F';
      const partnerSource = partnerId ? 'provided' : 'default fallback';

      const result = await cmsAPI.fetchLayouts({
        filter: [
          { property: 'id', value: 'users' },
          { property: 'partners_id', value: partnerIdToUse },
          { property: 'object', value: '_no_filter_' }
        ]
      });

      if (result.results.length > 0) {
        usersLayout.value = result.results[0];
        console.log(`CMS Store: Fetched users layout for partner: ${partnerIdToUse} (${partnerSource})`, usersLayout.value);
      } else {
        usersLayout.value = null;
        console.log(`CMS Store: No users layout found for partner: ${partnerIdToUse} (${partnerSource})`);
      }
      
      return usersLayout.value;
    } catch (err) {
      console.error('CMS Store: Error in fetchUsersLayout:', err);
      error.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Fetch the specific product layout for a partner
   */
  async function fetchProductLayout(partnerId?: string) {
    loading.value = true;
    error.value = null;

    try {
      const partnerIdToUse = partnerId || 'H3F';
      const partnerSource = partnerId ? 'provided' : 'default fallback';

      const result = await cmsAPI.fetchLayouts({
        filter: [
          { property: 'id', value: 'members_devices' },
          { property: 'partners_id', value: partnerIdToUse },
          { property: 'object', value: '_no_filter_' }
        ]
      });

      if (result.results.length > 0) {
        productLayout.value = result.results[0];
        console.log(`CMS Store: Fetched product layout for partner: ${partnerIdToUse} (${partnerSource})`, productLayout.value);
      } else {
        productLayout.value = null;
        console.log(`CMS Store: No product layout found for partner: ${partnerIdToUse} (${partnerSource})`);
      }
      
      return productLayout.value;
    } catch (err) {
      console.error('CMS Store: Error in fetchProductLayout:', err);
      error.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Fetch the specific case work order layout for a partner
   */
  async function fetchCaseWorkOrderLayout(partnerId?: string) {
    loading.value = true;
    error.value = null;

    try {
      const partnerIdToUse = partnerId || 'H3F';
      const partnerSource = partnerId ? 'provided' : 'default fallback';

      const result = await cmsAPI.fetchLayouts({
        filter: [
          { property: 'id', value: 'issues-3' },
          { property: 'partners_id', value: partnerIdToUse },
          { property: 'object', value: '_no_filter_' }
        ]
      });

      if (result.results.length > 0) {
        caseWorkOrderLayout.value = result.results[0];
        console.log(`CMS Store: Fetched case work order layout for partner: ${partnerIdToUse} (${partnerSource})`, caseWorkOrderLayout.value);
      } else {
        caseWorkOrderLayout.value = null;
        console.log(`CMS Store: No case work order layout found for partner: ${partnerIdToUse} (${partnerSource})`);
      }
      
      return caseWorkOrderLayout.value;
    } catch (err) {
      console.error('CMS Store: Error in fetchCaseWorkOrderLayout:', err);
      error.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Fetch a specific layout by ID
   */
  async function fetchLayoutById(layoutId: string, partnerId?: string) {
    loading.value = true;
    error.value = null;

    try {
      const partnerIdToUse = partnerId || 'H3F';
      const partnerSource = partnerId ? 'provided' : 'default fallback';

      const result = await cmsAPI.fetchLayouts({
        filter: [
          { property: 'id', value: layoutId },
          { property: 'partners_id', value: partnerIdToUse },
          { property: 'object', value: '_no_filter_' }
        ]
      });

      if (result.results.length > 0) {
        const layout = result.results[0];
        console.log(`CMS Store: Fetched layout ${layoutId} for partner: ${partnerIdToUse} (${partnerSource})`, layout);
        return layout;
      } else {
        console.log(`CMS Store: No layout found with ID ${layoutId} for partner: ${partnerIdToUse} (${partnerSource})`);
        return null;
      }
    } catch (err) {
      console.error(`CMS Store: Error in fetchLayoutById for ${layoutId}:`, err);
      error.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Get a layout by ID from the cached layouts
   */
  function getLayoutById(id: string): CMSLayout | undefined {
    return layouts.value.find(layout => layout.id === id);
  }

  /**
   * Clear all cached data
   */
  function clearCache() {
    layouts.value = [];
    caseLayout.value = null;
    customerLayout.value = null;
    locationLayout.value = null;
    contactLayout.value = null;
    usersLayout.value = null;
    productLayout.value = null;
    caseWorkOrderLayout.value = null;
    error.value = null;
  }

  return {
    // State
    layouts,
    caseLayout,
    customerLayout,
    locationLayout,
    contactLayout,
    usersLayout,
    productLayout,
    caseWorkOrderLayout,
    loading,
    error,
    
    // Actions
    fetchAllLayouts,
    fetchCaseLayout,
    fetchCustomerLayout,
    fetchLocationLayout,
    fetchContactLayout,
    fetchUsersLayout,
    fetchProductLayout,
    fetchCaseWorkOrderLayout,
    fetchLayoutById,
    getLayoutById,
    clearCache
  };
}); 