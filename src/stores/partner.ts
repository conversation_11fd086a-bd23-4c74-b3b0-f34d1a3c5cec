import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { partnerAPI } from '@/services/PartnerAPI';
import type { Product, Partner } from '@/types/partner';
import { usePartnerAPI } from '@/composables/services/usePartnerAPI';
import { useUserStore } from '@/stores/user';

export const usePartnerStore = defineStore('partner', () => {
    const products = ref<Product[]>([]);
    const partnerTeams = ref<any[]>([]);
    const currentPartner = ref<Partner | null>(null);
    const loading = ref(false);
    const loadingTeams = ref(false);
    const loadingCurrentPartner = ref(false);
    const error = ref<string | null>(null);
    const teamsError = ref<string | null>(null);
    const currentPartnerError = ref<string | null>(null);

    // Helper computed properties for partner type checking
    const isServiceProvider = computed(() => {
        return currentPartner.value?.types?.includes('service_[rovider') ?? false;
    });

    const isStandardPartner = computed(() => {
        return currentPartner.value?.types?.includes('standard') ?? false;
    });

    async function fetchProducts() {
        try {
            loading.value = true;
            error.value = null;
            const partnerAPI = usePartnerAPI();
            const response = await partnerAPI.fetchProducts();
            // debugger
            if (response.success) {
                products.value = response.partners_product_list.results;
            } else {
                throw new Error('Failed to fetch products');
            }
        } catch (err) {
            error.value = err instanceof Error ? err.message : 'Failed to fetch products';
            console.error('Error fetching products:', err);
        } finally {
            loading.value = false;
        }
    }

    async function fetchPartnerTeams(ecosystem: boolean = false) {
        try {
            loadingTeams.value = true;
            teamsError.value = null;
            
            const api = usePartnerAPI();
            const response = await api.fetchPartnerTeams(ecosystem);
            
            if (response.success) {
                let teams = response.pl__org_partners_teams || [];
                
                if (!ecosystem) {
                    // Get current user's organization from user store
                    const userStore = useUserStore();
                    const currentOrgId = userStore.userTeams?.[0]?.team_id?.substring(0, 3) || userStore.userTeams?.[0]?.substring(0, 3);
                    
                    // Filter teams to only include those from current organization
                    teams = teams.filter((team: any) => team.val.substring(0, 3) === currentOrgId);
                }
                
                partnerTeams.value = teams;
            } else {
                throw new Error('Failed to fetch partner teams');
            }
        } catch (err) {
            teamsError.value = err instanceof Error ? err.message : 'Failed to fetch partner teams';
            console.error('Error fetching partner teams:', err);
        } finally {
            loadingTeams.value = false;
        }
    }

    async function fetchCurrentPartner() {
        try {
            loadingCurrentPartner.value = true;
            currentPartnerError.value = null;
            
            const userStore = useUserStore();
            const partnerId = userStore.userData?.object_id;
            
            if (!partnerId) {
                throw new Error('No partner ID found in user data');
            }
            
            const api = usePartnerAPI();
            const response = await api.fetchCurrentPartner(partnerId);
            
            if (response.success && response.partners.results.length > 0) {
                currentPartner.value = response.partners.results[0];
                console.log('🏢 PartnerStore: Fetched current partner:', currentPartner.value);
            } else {
                throw new Error('No partner data found');
            }
        } catch (err) {
            currentPartnerError.value = err instanceof Error ? err.message : 'Failed to fetch current partner';
            console.error('Error fetching current partner:', err);
        } finally {
            loadingCurrentPartner.value = false;
        }
    }

    return {
        products,
        partnerTeams,
        currentPartner,
        loading,
        loadingTeams,
        loadingCurrentPartner,
        error,
        teamsError,
        currentPartnerError,
        isServiceProvider,
        isStandardPartner,
        fetchProducts,
        fetchPartnerTeams,
        fetchCurrentPartner
    };
}); 