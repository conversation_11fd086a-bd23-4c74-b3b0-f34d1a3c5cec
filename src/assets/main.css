body {
  margin: 0;
}

#app {
  max-width: 100%;
  margin: 0;
  font-weight: normal;
}

/* Global Scrollbar Styles */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--surface-300) var(--surface-0);
}

/* Webkit browsers (Chrome, Safari, Edge) */
*::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

*::-webkit-scrollbar-track {
  background: var(--surface-0);
  border-radius: 4px;
}

*::-webkit-scrollbar-thumb {
  background-color: var(--surface-300);
  border-radius: 8px;
  border: 1px solid var(--surface-0);
}

*::-webkit-scrollbar-thumb:hover {
  background-color: var(--surface-400);
}

*::-webkit-scrollbar-corner {
  background: var(--surface-0);
}

/* For horizontal scrollbars */
*::-webkit-scrollbar:horizontal {
  height: 8px;
}

/* Global Quill Editor List Fixes - Minimal override */
.ql-editor li[data-list="bullet"]:before {
  content: none !important;
  display: none !important;
}

/* Reduce list indentation */
.ql-editor li {
  padding-left: .2rem !important;
}

/* temp until journeys removes global styles */
.p-datatable {
  font-size: 1rem !important;
}

/* Override PrimeVue dialog styles for our custom modals */
.resolve-case-modal .p-dialog,
.delete-case-modal .p-dialog,
.unresolve-case-modal .p-dialog {
  border-radius: 8px !important;
}

.p-dialog-content,
.p-dialog-content,
.p-dialog-content {
  border-radius: 0 0 8px 8px !important;
}

.p-dialog-header,
.p-dialog-header,
.p-dialog-header {
  border-radius: 8px 8px 0 0 !important;
}

.p-dialog-footer,
.p-dialog-footer,
.p-dialog-footer {
  border-radius: 0 0 8px 8px !important;
}

