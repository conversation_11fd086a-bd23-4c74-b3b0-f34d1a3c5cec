import { useRouter, type RouteLocationNormalized } from 'vue-router'
import { usePageHistoryStore, PageType } from '@/stores/pageHistory'
// Import the actual stores to get real data
import { useCasesStore } from '@/stores/cases'
import { useKnowledgeStore } from '@/modules/knowledge/stores/knowledge'
import { useMemberStore } from '@/stores/member'
import { useUsersStore } from '@/stores/users'

export function usePageHistoryTracker() {
  const router = useRouter()
  const pageHistoryStore = usePageHistoryStore()
  
  // Get store instances
  const casesStore = useCasesStore()
  const knowledgeStore = useKnowledgeStore()
  const memberStore = useMemberStore()
  const usersStore = useUsersStore()

  function initializeTracking() {
    console.log('🔍 Initializing page history tracking')
    
    router.afterEach((to: RouteLocationNormalized, from: RouteLocationNormalized) => {
      console.log('🔍 Router afterEach triggered:', {
        toPath: to.path,
        toName: to.name,
        fromPath: from.path,
        fromName: from.name,
        params: to.params,
        query: to.query
      })
      
      trackPageVisit(to, from)
    })
  }

  function trackPageVisit(to: RouteLocationNormalized, from: RouteLocationNormalized) {
    console.log('📝 Tracking page visit:', {
      path: to.path,
      name: to.name,
      params: to.params,
      query: to.query
    })

    try {
      const pageType = determinePageType(to)
      console.log('📝 Determined page type:', pageType)
      
      if (pageType === null) {
        console.log('📝 Skipping tracking for page type: null')
        return
      }

      const replaced = from.name !== null && from.name !== undefined // Not replaced if coming from nowhere
      
      switch (pageType) {
        case PageType.CASE:
          trackCasePage(to, replaced)
          break
        case PageType.TASK:
          trackTaskPage(to, replaced)
          break
        case PageType.KB_ARTICLE:
          trackKnowledgeArticlePage(to, replaced)
          break
        case PageType.CUSTOMER:
          trackCustomerPage(to, replaced)
          break
        case PageType.LIST_VIEW:
          trackListViewPage(to, replaced)
          break
        case PageType.GENERAL:
          trackGeneralPage(to, replaced)
          break
      }
    } catch (error) {
      console.error('📝 Error tracking page visit:', error)
    }
  }

  function determinePageType(route: RouteLocationNormalized): PageType | null {
    const routeName = route.name as string
    const routePath = route.path
    
    console.log('🔍 Determining page type for route:', { name: routeName, path: routePath, params: route.params })
    
    // Case pages - check both route name and path pattern
    if (routeName === 'InboxCase' || routeName === 'inbox-case' || 
        (routePath.startsWith('/inbox/cases/') && route.params.id)) {
      console.log('🔍 Detected CASE page', { routeName, routePath, hasId: !!route.params.id })
      return PageType.CASE
    }
    
    // Task pages  
    if (routeName === 'InboxTask' || routeName === 'inbox-task' ||
        (routePath.startsWith('/inbox/tasks/') && route.params.id)) {
      console.log('🔍 Detected TASK page')
      return PageType.TASK
    }
    
    // Knowledge Base article pages
    if (routeName === 'ArticleView' || routeName === 'knowledge-article' ||
        (routePath.startsWith('/knowledge/articles/') && route.params.id)) {
      console.log('🔍 Detected KB_ARTICLE page')
      return PageType.KB_ARTICLE
    }
    
    // Customer pages
    if (routeName === 'ContactView' || routeName === 'customer-contact' || 
        routeName === 'CustomerView' || routeName === 'customer' ||
        (routePath.startsWith('/customers/') && route.params.id)) {
      console.log('🔍 Detected CUSTOMER page')
      return PageType.CUSTOMER
    }
    
    // List view pages
    if (routeName === 'InboxView' || routeName === 'inbox' ||
        routeName === 'ContactsListView' || routeName === 'contacts' ||
        routeName === 'CustomersListView' || routeName === 'customers' ||
        routeName === 'KnowledgeView' || routeName === 'knowledge' ||
        (routePath === '/inbox' || routePath === '/knowledge' || routePath === '/customers')) {
      console.log('🔍 Detected LIST_VIEW page')
      return PageType.LIST_VIEW
    }
    
    // All other pages are general
    console.log('🔍 Detected GENERAL page')
    return PageType.GENERAL
  }

  function trackCasePage(route: RouteLocationNormalized, replaced: boolean) {
    console.log('📝 Tracking case page:', route.params)
    
    const caseId = route.params.id as string
    if (!caseId) {
      console.warn('📝 No case ID found in route params')
      return
    }

    // Add a small delay to allow case data to load
    setTimeout(() => {
      trackCasePageWithData(route, caseId, replaced)
    }, 500)
  }

  function trackCasePageWithData(route: RouteLocationNormalized, caseId: string, replaced: boolean) {
    console.log('📝 Tracking case page with data:', { caseId, replaced })

    // Try to get case data from the current issue in the store
    let caseData = {
      refId: caseId,
      name: 'Loading...',
      status: null as string | null,
      location: undefined as string | undefined
    }

    // Check if we have the current case loaded
    console.log('📝 Checking current issue in store:', {
      currentIssueId: casesStore.currentIssue?.id,
      searchingForId: caseId,
      isMatch: casesStore.currentIssue?.id === caseId
    })
    
    if (casesStore.currentIssue && casesStore.currentIssue.id === caseId) {
      const issue = casesStore.currentIssue
      console.log('📝 Found current issue in store:', {
        id: issue.id,
        display_name: issue.display_name,
        reference_num: issue.reference_num,
        location: issue.location
      })
      
      // Extract case reference number and name
      const refId = issue.reference_num || (issue as any).c__conference_id || issue.display_name || caseId
      const caseName = issue.display_name || 'Unknown Case'
      
      // Extract status - only use c__status, can be null/blank
      const status = (issue as any).c__status || null
      
      // Extract location information - use location.site_name
      const locationName = issue.location?.site_name || 
                           (issue as any).c__location || 
                           (issue as any).c__name || 
                           issue.location?.name
      
      console.log('📝 Extracted case data from currentIssue:', {
        refId,
        caseName,
        status,
        locationName,
        hasLocation: !!issue.location,
        locationObj: issue.location
      })
      
      caseData = {
        refId: refId,
        name: caseName,
        status: status,
        location: locationName
      }
    } else {
      // Check if the case exists in any of the case arrays
      const allCases = [
        ...casesStore.issues,
        ...casesStore.myWorkIssues,
        ...casesStore.memberCases
      ]
      
      console.log('📝 Searching in case arrays:', {
        issuesCount: casesStore.issues.length,
        myWorkIssuesCount: casesStore.myWorkIssues.length,
        memberCasesCount: casesStore.memberCases.length,
        totalCases: allCases.length,
        searchingForId: caseId
      })
      
      const foundCase = allCases.find(issue => issue.id === caseId)
      if (foundCase) {
        console.log('📝 Found case in store arrays:', {
          id: foundCase.id,
          display_name: foundCase.display_name,
          reference_num: foundCase.reference_num,
          location: foundCase.location
        })
        
        // Extract case reference number and name
        const refId = foundCase.reference_num || (foundCase as any).c__conference_id || foundCase.display_name || caseId
        const caseName = foundCase.display_name || 'Unknown Case'
        
        // Extract status - only use c__status, can be null/blank
        const status = (foundCase as any).c__status || null
        
        // Extract location information - use location.site_name
        const locationName = foundCase.location?.site_name || 
                             (foundCase as any).c__location || 
                             (foundCase as any).c__name || 
                             foundCase.location?.name
        
        caseData = {
          refId: refId,
          name: caseName,
          status: status,
          location: locationName
        }
      } else {
        console.log('📝 Case not found in store, using fallback data')
        console.log('📝 Available cases in store:', {
          currentIssue: casesStore.currentIssue?.id,
          issuesCount: casesStore.issues.length,
          myWorkIssuesCount: casesStore.myWorkIssues.length,
          memberCasesCount: casesStore.memberCases.length,
          searchingForId: caseId
        })
        // Use fallback data with the case ID
        caseData = {
          refId: caseId,
          name: `Case ${caseId}`,
          status: null,
          location: undefined
        }
      }
    }

    console.log('📝 Final case data:', caseData)
    console.log('📝 Creating case page item with:', { route: route.path, caseData, replaced })
    
    const pageItem = pageHistoryStore.createCasePageItem(route, caseData, replaced)
    console.log('📝 Created page item:', pageItem)
    pageHistoryStore.addPageHistoryItem(pageItem)
  }

  function trackTaskPage(route: RouteLocationNormalized, replaced: boolean) {
    console.log('📝 Tracking task page:', route.params)
    
    const taskId = route.params.id as string
    if (!taskId) {
      console.warn('📝 No task ID found in route params')
      return
    }

    // TODO: Connect to task store when available
    const taskData = {
      name: `Task ${taskId}`,
      status: 'Unknown'
    }

    console.log('📝 Task data:', taskData)
    
    const pageItem = pageHistoryStore.createTaskPageItem(route, taskData, replaced)
    pageHistoryStore.addPageHistoryItem(pageItem)
  }

  function trackKnowledgeArticlePage(route: RouteLocationNormalized, replaced: boolean) {
    console.log('📝 Tracking knowledge article page:', route.params)
    
    const articleId = route.params.id as string
    if (!articleId) {
      console.warn('📝 No article ID found in route params')
      return
    }

    // Add a small delay to allow article data to load
    setTimeout(() => {
      trackKnowledgeArticlePageWithData(route, articleId, replaced)
    }, 500)
  }

  function trackKnowledgeArticlePageWithData(route: RouteLocationNormalized, articleId: string, replaced: boolean) {
    console.log('📝 Tracking knowledge article page with data:', { articleId, replaced })

    // Try to get article data from the knowledge store
    let articleData = {
      title: `Article ${articleId}`,
      status: 'Unknown',
      subtitle: undefined as string | undefined
    }

    // Check if we have the current article loaded
    console.log('📝 Checking current article in store:', {
      currentArticleId: knowledgeStore.currentArticle?.id,
      searchingForId: articleId,
      isMatch: knowledgeStore.currentArticle?.id === articleId
    })
    
    if (knowledgeStore.currentArticle && knowledgeStore.currentArticle.id === articleId) {
      const article = knowledgeStore.currentArticle
      console.log('📝 Found current article in store:', {
        id: article.id,
        title: article.title,
        lbl: article.lbl,
        c__d_status: article.c__d_status,
        sub_title: article.sub_title
      })
      
      articleData = {
        title: article.title || article.lbl || `Article ${articleId}`,
        status: article.c__d_status || 'Unknown',
        subtitle: article.sub_title || undefined
      }
    } else {
      // Check if the article exists in the items array
      console.log('📝 Searching in knowledge store arrays:', {
        itemsCount: knowledgeStore.items.length,
        currentListCount: knowledgeStore.currentList.length,
        searchingForId: articleId
      })
      
      const foundArticle = knowledgeStore.items.find(item => item.id === articleId)
      if (foundArticle) {
        console.log('📝 Found article in items array:', {
          id: foundArticle.id,
          title: foundArticle.title,
          lbl: foundArticle.lbl,
          c__d_status: foundArticle.c__d_status,
          sub_title: foundArticle.sub_title
        })
        
        articleData = {
          title: foundArticle.title || foundArticle.lbl || `Article ${articleId}`,
          status: foundArticle.c__d_status || 'Unknown', 
          subtitle: foundArticle.sub_title || undefined
        }
      } else {
        // Check current list
        const foundInList = knowledgeStore.currentList.find(item => item.id === articleId)
        if (foundInList) {
          console.log('📝 Found article in current list:', {
            id: foundInList.id,
            title: foundInList.title,
            lbl: foundInList.lbl,
            c__d_status: foundInList.c__d_status,
            sub_title: foundInList.sub_title
          })
          
          articleData = {
            title: foundInList.title || foundInList.lbl || `Article ${articleId}`,
            status: foundInList.c__d_status || 'Unknown',
            subtitle: foundInList.sub_title || undefined
          }
        } else {
          console.log('📝 Article not found in store, using fallback data')
          console.log('📝 Available articles in store:', {
            currentArticle: knowledgeStore.currentArticle?.id,
            itemsCount: knowledgeStore.items.length,
            currentListCount: knowledgeStore.currentList.length,
            searchingForId: articleId
          })
        }
      }
    }

    console.log('📝 Article data:', articleData)
    
    const pageItem = pageHistoryStore.createKBArticlePageItem(route, articleData, replaced)
    pageHistoryStore.addPageHistoryItem(pageItem)
  }

  function trackCustomerPage(route: RouteLocationNormalized, replaced: boolean) {
    console.log('📝 Tracking customer page:', route.params)
    
    const customerId = route.params.id as string
    if (!customerId) {
      console.warn('📝 No customer ID found in route params')
      return
    }

    // TODO: Connect to customer/member store data when available
    // For now, we'll use basic data structure
    const customerData = {
      name: `Customer ${customerId}`,
      status: 'Unknown'
    }

    console.log('📝 Customer data:', customerData)
    
    const pageItem = pageHistoryStore.createCustomerPageItem(route, customerData, replaced)
    pageHistoryStore.addPageHistoryItem(pageItem)
  }

  function trackListViewPage(route: RouteLocationNormalized, replaced: boolean) {
    console.log('📝 Tracking list view page:', route.name, route.query)
    
    // Extract list view information from route and query params
    let viewType = 'Unknown'
    const filters: string[] = []
    let query = ''
    let page = 1
    let totalResults = 0

    // Determine view type from route name
    const routeName = route.name as string
    if (routeName === 'InboxView' || routeName === 'inbox') {
      viewType = 'Cases'
    } else if (routeName === 'ContactsListView' || routeName === 'contacts') {
      viewType = 'Contacts'
    } else if (routeName === 'CustomersListView' || routeName === 'customers') {
      viewType = 'Customers'
    } else if (routeName === 'KnowledgeView' || routeName === 'Knowledge' || routeName === 'knowledge') {
      // Determine if it's articles or templates based on the knowledge store
      const contentType = knowledgeStore.selectedContentType || 'articles'
      viewType = contentType === 'templates' ? 'Templates List' : 'Articles List'
      
      console.log('📝 Knowledge page detected:', {
        routeName,
        selectedContentType: knowledgeStore.selectedContentType,
        viewType,
        totalCount: knowledgeStore.totalCount,
        currentListLength: knowledgeStore.currentList.length,
        searchQuery: knowledgeStore.searchQuery,
        libraries: knowledgeStore.libraries.length
      })
    }

    // Extract filters and search from query params
    if (route.query.search || route.query.q) {
      query = (route.query.search || route.query.q) as string
    }
    
    // Knowledge-specific query extraction
    if (routeName === 'KnowledgeView' || routeName === 'Knowledge' || routeName === 'knowledge') {
      // Extract knowledge-specific filters from query params
      if (route.query.library) {
        const libraryId = route.query.library as string
        const library = knowledgeStore.libraries.find(lib => lib.id === libraryId)
        if (library) {
          filters.push(`Library: ${library.name}`)
        } else {
          filters.push(`Library: ${libraryId}`)
        }
      }
      if (route.query.category) {
        filters.push(`Category: ${route.query.category}`)
      }
      if (route.query.status) {
        filters.push(`Status: ${route.query.status}`)
      }
      if (route.query.label) {
        filters.push(`Label: ${route.query.label}`)
      }
      
      // Extract search query from knowledge store if not in URL
      if (!query && knowledgeStore.searchQuery) {
        query = knowledgeStore.searchQuery
      }
    } else {
      // General filters for other page types
      if (route.query.status) {
        filters.push(`Status: ${route.query.status}`)
      }
      if (route.query.assignee) {
        filters.push(`Assignee: ${route.query.assignee}`)
      }
    }
    
    if (route.query.page) {
      page = parseInt(route.query.page as string) || 1
    }

    // Try to get total results from relevant stores
    if (viewType === 'Cases') {
      totalResults = casesStore.totalCount || casesStore.myWorkReadyTotalCount || 0
    } else if (viewType === 'Articles List' || viewType === 'Templates List') {
      totalResults = knowledgeStore.totalCount || knowledgeStore.currentList.length || 0
    }

    const listViewData = {
      filters,
      query,
      page,
      totalResults,
      viewType
    }

    console.log('📝 List view data:', listViewData)
    
    const pageItem = pageHistoryStore.createListViewPageItem(route, listViewData, replaced)
    pageHistoryStore.addPageHistoryItem(pageItem)
  }

  function trackGeneralPage(route: RouteLocationNormalized, replaced: boolean) {
    console.log('📝 Tracking general page:', route.name)
    
    // Determine title and section from route
    let title = 'Unknown Page'
    let section = 'General'

    const routeName = route.name as string
    
    // Map common route names to readable titles
    const routeTitleMap: Record<string, { title: string; section: string }> = {
      'Home': { title: 'Dashboard', section: 'Home' },
      'HomeView': { title: 'Dashboard', section: 'Home' },
      'home': { title: 'Dashboard', section: 'Home' },
      'AnalyticsView': { title: 'Analytics', section: 'Analytics' },
      'Analytics': { title: 'Analytics', section: 'Analytics' },
      'analytics': { title: 'Analytics', section: 'Analytics' },
      'SettingsView': { title: 'Settings', section: 'Settings' },
      'Settings': { title: 'Settings', section: 'Settings' },
      'settings': { title: 'Settings', section: 'Settings' },
      'UserProfile': { title: 'User Profile', section: 'Profile' },
      'Profile': { title: 'User Profile', section: 'Profile' },
      'profile': { title: 'User Profile', section: 'Profile' },
      'AIStudioView': { title: 'AI Studio', section: 'AI Studio' },
      'aistudio': { title: 'AI Studio', section: 'AI Studio' },
      'JourneysView': { title: 'Journeys', section: 'Journeys' },
      'Journeys': { title: 'Journeys', section: 'Journeys' },
      'journeys': { title: 'Journeys', section: 'Journeys' }
    }

    if (routeTitleMap[routeName]) {
      title = routeTitleMap[routeName].title
      section = routeTitleMap[routeName].section
    } else if (routeName) {
      // Convert camelCase/PascalCase to readable title
      title = routeName
        .replace(/([A-Z])/g, ' $1')
        .replace(/^./, str => str.toUpperCase())
        .replace(/View$/, '')
        .trim()
      section = 'General'
    }

    const generalData = {
      title,
      section
    }

    console.log('📝 General page data:', generalData)
    
    const pageItem = pageHistoryStore.createGeneralPageItem(route, generalData, replaced)
    pageHistoryStore.addPageHistoryItem(pageItem)
  }

  return {
    initializeTracking
  }
} 