import { ref } from 'vue'
import * as summarizerApi from '@/composables/services/useSummarizerApi'
import type { Message } from '@/modules/comms/types'

export function useAIAssistant() {
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Convert communication messages to API format
  const convertMessagesToAPIFormat = (messages: Message[]): summarizerApi.Message[] => {
    return messages.map(msg => ({
      senderId: msg.senderId || 'unknown',
      content: msg.content || ''
    }))
  }

  // Summarize conversation
  const summarizeConversation = async (messages: Message[]): Promise<string> => {
    isLoading.value = true
    error.value = null

    try {
      const apiMessages = convertMessagesToAPIFormat(messages)
      const response = await summarizerApi.summarizeConversation({ messages: apiMessages })
      return response.result
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to summarize conversation'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Generate reply
  const generateReply = async (messages: Message[]): Promise<string> => {
    isLoading.value = true
    error.value = null

    try {
      const apiMessages = convertMessagesToAPIFormat(messages)
      const response = await summarizerApi.generateReply({ messages: apiMessages })
      return response.result
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to generate reply'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Reformat response for email (with enhanced pre/post-processing for professional email format)
  const reformatEmail = async (content: string): Promise<string> => {
    isLoading.value = true
    error.value = null

    try {
      const response = await summarizerApi.reformatEmail({ content })
      return response.result
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to reformat email'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Reformat response for chat (with enhanced pre/post-processing for concise chat format)
  const reformatChat = async (content: string): Promise<string> => {
    isLoading.value = true
    error.value = null

    try {
      const response = await summarizerApi.reformatChat({ content })
      return response.result
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to reformat chat response'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Reformat response for knowledge base (with enhanced pre/post-processing for comprehensive KB articles)
  const reformatKnowledgeBase = async (content: string): Promise<string> => {
    isLoading.value = true
    error.value = null

    try {
      const response = await summarizerApi.reformatKnowledgeBase({ content })
      return response.result
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to reformat knowledge base article'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Legacy method for backward compatibility - will use reformatEmail by default
  const reformatResponse = async (content: string): Promise<string> => {
    // Default to email formatting for backward compatibility
    return await reformatEmail(content)
  }

  // Cleanup response (minimal grammar and spelling fixes)
  const cleanupResponse = async (content: string): Promise<string> => {
    isLoading.value = true
    error.value = null

    try {
      const response = await summarizerApi.cleanupEmail({ content })
      return response.result
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to cleanup response'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // General chat completion
  const chatCompletion = async (request: summarizerApi.GeneralOpenAIRequest): Promise<string> => {
    isLoading.value = true
    error.value = null

    try {
      const response = await summarizerApi.chatCompletion(request)
      return response.result
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to complete chat request'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  return {
    isLoading,
    error,
    summarizeConversation,
    generateReply,
    reformatEmail,
    reformatChat,
    reformatKnowledgeBase,
    reformatResponse, // Legacy method for backward compatibility
    cleanupResponse,
    chatCompletion
  }
} 