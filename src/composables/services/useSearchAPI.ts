import { getHttpClient } from '@/services/httpClientProvider';
import { useAuthStore } from '@/stores/auth';
import type { Issue } from './useIssuesAPI';
import type { KnowledgeItem } from './types/knowledge';

// Search scope types
export type SearchScope = 'kb' | 'issues' | 'members_locations' | 'members_users';

// Base search response interface
export interface BaseSearchResponse {
  success: boolean;
  current_server_time: string;
}

// Knowledge base search response
export interface KnowledgeSearchResponse extends BaseSearchResponse {
  kb: KnowledgeItem[];
  order: string[];
  scores: { kb: number };
  result_counts: { kb: number };
  total_counts: { kb: number };
}

// Issues search response
export interface IssuesSearchResponse extends BaseSearchResponse {
  issues: Issue[];
  order: string[];
  scores: { issues: number };
  result_counts: { issues: number };
  total_counts: { issues: number };
}

// Member devices removed - no longer needed

// Member locations search response
export interface MemberLocation {
  id: string;
  members_id: string;
  site_name: string;
  street_1: string;
  street_2: string;
  city: string;
  state_id: number;
  zipcode: string;
  country_id: number;
  latitude: number;
  longitude: number;
  contact: string;
  phone: string | null;
  notes: string;
  created: string;
  updated: string;
  status: number;
  active: boolean;
  c__name: string;
  c__nickname: string;
  c__lbl: string;
  c__issue_count: number;
  c__user_count: number;
  c__technology_count: number;
  c__team_count: number;
  c__d_status: string;
  clazz: string;
  score: number;
  _highlightmap: Record<string, string>;
  tags: any[];
  [key: string]: any;
}

export interface MemberLocationsSearchResponse extends BaseSearchResponse {
  members_locations: MemberLocation[];
  order: string[];
  scores: { members_locations: number };
  result_counts: { members_locations: number };
  total_counts: { members_locations: number };
}

// Member users search response
export interface MemberUsersSearchResponse extends BaseSearchResponse {
  members_users: any[];
  order: string[];
  scores: { members_users: number };
  result_counts: { members_users: number };
  total_counts: { members_users: number };
}

// Union type for all search responses
export type SearchResponse = 
  | KnowledgeSearchResponse 
  | IssuesSearchResponse 
  | MemberLocationsSearchResponse 
  | MemberUsersSearchResponse;

// Search parameters interface
export interface SearchParams {
  query: string;
  scope: SearchScope[];
  searchAll?: boolean;
  limit?: number;
}

// Combined search result interface
export interface SearchResult {
  type: SearchScope;
  items: any[];
  totalCount: number;
  score: number;
}

export interface CombinedSearchResponse {
  success: boolean;
  results: SearchResult[];
  currentServerTime: string;
}

/**
 * Composable that provides access to Search API functions.
 * This safely initializes the httpClient with authStore only when called 
 * inside components or other composables after Pinia is ready.
 */
export function useSearchAPI() {
  const authStore = useAuthStore();
  const httpClient = getHttpClient(authStore);

  return {
    async searchKnowledge(params: SearchParams): Promise<KnowledgeSearchResponse> {
      try {
        const queryParams = new URLSearchParams({
          sAction: 'search',
          query: params.query,
          searchAll: params.searchAll ? 'true' : 'false',
          limit: (params.limit || 5).toString()
        });

        // Add scope parameters
        params.scope.forEach(scope => {
          if (scope === 'kb') {
            queryParams.append('scope[]', 'kb');
          }
        });

        const data = await httpClient.get<KnowledgeSearchResponse>(`admin/v4/core/?${queryParams.toString()}`);

        if (data.success) {
          return data;
        }

        throw new Error('Failed to search knowledge base');
      } catch (error) {
        console.error('❌ SearchAPI: Error searching knowledge base:', error);
        throw error;
      }
    },

    async searchIssues(params: SearchParams): Promise<IssuesSearchResponse> {
      try {
        const queryParams = new URLSearchParams({
          sAction: 'search',
          query: params.query,
          searchAll: params.searchAll ? 'true' : 'false',
          limit: (params.limit || 5).toString()
        });

        // Add scope parameters
        params.scope.forEach(scope => {
          if (scope === 'issues') {
            queryParams.append('scope[]', 'issues');
          }
        });

        const data = await httpClient.get<IssuesSearchResponse>(`admin/v4/core/?${queryParams.toString()}`);

        if (data.success) {
          return data;
        }

        throw new Error('Failed to search issues');
      } catch (error) {
        console.error('❌ SearchAPI: Error searching issues:', error);
        throw error;
      }
    },

    // searchMemberDevices removed - no longer needed

    async searchMemberLocations(params: SearchParams): Promise<MemberLocationsSearchResponse> {
      try {
        const queryParams = new URLSearchParams({
          sAction: 'search',
          query: params.query,
          searchAll: params.searchAll ? 'true' : 'false',
          limit: (params.limit || 5).toString()
        });

        // Add scope parameters
        params.scope.forEach(scope => {
          if (scope === 'members_locations') {
            queryParams.append('scope[]', 'members_locations');
          }
        });

        const data = await httpClient.get<MemberLocationsSearchResponse>(`admin/v4/core/?${queryParams.toString()}`);

        if (data.success) {
          return data;
        }

        throw new Error('Failed to search member locations');
      } catch (error) {
        console.error('❌ SearchAPI: Error searching member locations:', error);
        throw error;
      }
    },

    async searchMemberUsers(params: SearchParams): Promise<MemberUsersSearchResponse> {
      try {
        const queryParams = new URLSearchParams({
          sAction: 'search',
          query: params.query,
          searchAll: params.searchAll ? 'true' : 'false',
          limit: (params.limit || 5).toString()
        });

        // Add scope parameters
        params.scope.forEach(scope => {
          if (scope === 'members_users') {
            queryParams.append('scope[]', 'members_users');
          }
        });

        const data = await httpClient.get<MemberUsersSearchResponse>(`admin/v4/core/?${queryParams.toString()}`);

        if (data.success) {
          return data;
        }

        throw new Error('Failed to search member users');
      } catch (error) {
        console.error('❌ SearchAPI: Error searching member users:', error);
        throw error;
      }
    },

    async searchAll(params: SearchParams): Promise<CombinedSearchResponse> {
      try {
        const promises: Promise<SearchResponse>[] = [];
        const scopes = params.scope;

        // Create individual search promises for each scope
        if (scopes.includes('kb')) {
          promises.push(this.searchKnowledge({ ...params, scope: ['kb'] }));
        }
        if (scopes.includes('issues')) {
          promises.push(this.searchIssues({ ...params, scope: ['issues'] }));
        }
        // members_devices_dict removed - no longer needed
        if (scopes.includes('members_locations')) {
          promises.push(this.searchMemberLocations({ ...params, scope: ['members_locations'] }));
        }
        if (scopes.includes('members_users')) {
          promises.push(this.searchMemberUsers({ ...params, scope: ['members_users'] }));
        }

        const responses = await Promise.all(promises);
        
        // Transform responses into combined format
        const results: SearchResult[] = [];
        let currentServerTime = '';

        responses.forEach((response) => {
          currentServerTime = response.current_server_time;
          
          if ('kb' in response && response.kb.length > 0) {
            results.push({
              type: 'kb',
              items: response.kb,
              totalCount: response.total_counts.kb,
              score: response.scores.kb
            });
          }
          
          if ('issues' in response && response.issues.length > 0) {
            results.push({
              type: 'issues',
              items: response.issues,
              totalCount: response.total_counts.issues,
              score: response.scores.issues
            });
          }
          
          // members_devices_dict processing removed - no longer needed
          
          if ('members_locations' in response && response.members_locations.length > 0) {
            results.push({
              type: 'members_locations',
              items: response.members_locations,
              totalCount: response.total_counts.members_locations,
              score: response.scores.members_locations
            });
          }
          
          if ('members_users' in response && response.members_users.length > 0) {
            results.push({
              type: 'members_users',
              items: response.members_users,
              totalCount: response.total_counts.members_users,
              score: response.scores.members_users
            });
          }
        });

        return {
          success: true,
          results,
          currentServerTime
        };
      } catch (error) {
        console.error('❌ SearchAPI: Error performing combined search:', error);
        throw error;
      }
    }
  };
} 