import { getHttpClient } from '@/services/httpClientProvider';
import { useAuthStore } from '@/stores/auth';

export interface View {
    id: string;
    label: string;
    type?: number;
    status?: number;
    object?: string;
    sort_object?: string;
    sort_dir?: string;
    group_by?: string;
    filters?: Array<{
        filter_field: string;
        filter_operator: string;
        filter_field_lbl: string;
        filter_operator_lbl: string;
        filter_compare_field: any;
        filter_compare_field_lbl: string;
        filter_compare_field2_lbl?: string;
    }>;
}

export interface SettingsResponse {
    success: boolean;
    message?: string;
    relay_views?: {
        results: View[];
    };
}

/**
 * Composable that provides access to Settings API functions.
 * This safely initializes the httpClient with authStore only when called 
 * inside components or other composables after Pi<PERSON> is ready.
 */
export function useSettingsAPI() {
    const authStore = useAuthStore();
    const httpClient = getHttpClient(authStore);

    return {
        async fetchViews(): Promise<View[]> {
            try {            
                const data = await httpClient.get<SettingsResponse>('admin/v4/settings/', {
                    sAction: 'viewsListing',
                    page: '1',
                    start: '0',
                    limit: '50',
                });

                if (data.success && data.relay_views?.results) {
                    return data.relay_views.results;
                }
                
                console.error('❌ SettingsAPI: No views data in response', data);
                throw new Error(data.message || 'Failed to fetch views');
            } catch (error) {
                console.error('❌ SettingsAPI: Error fetching views:', error);
                throw error;
            }
        }
    };
} 