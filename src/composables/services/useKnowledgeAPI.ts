import { getHttpClient } from '@/services/httpClientProvider';
import { useAuthStore } from '@/stores/auth';
import type {
    KnowledgeNode,
    KnowledgeItem,
    KnowledgeResponse,
    FetchKnowledgeParams,
    ListingParams,
    KnowledgeListingResult,
    KnowledgeRevision,
    RevisionResponse,
    KnowledgeLibrary,
    KnowledgeLabel,
    RelatedArticlesResult,
    ArticleRevision,
    ArticleRevisionsResult,
    KnowledgeEventsResult,
    FileUploadResponse,
    TeamAccess,
    KnowledgeBaseArticle,
    KnowledgeListingResponse,
    ArticleRevisionsResponse,
    LabelsResponse,
    RelatedArticlesResponse,
    RelatedArticle,
    EventsResponse,
    KnowledgeEvent,
    KnowledgeBasesResponse,
    KBTreeParams,
    KBTreeItem,
} from './types/knowledge';

// Define interfaces for API responses
interface GenericResponse {
    success: boolean;
    message?: string;
    [key: string]: any;
}

/**
 * Composable that provides access to Knowledge API functions.
 * This safely initializes the httpClient with authStore only when called
 * inside components or other composables after <PERSON><PERSON> is ready.
 */
export function useKnowledgeAPI() {
    const authStore = useAuthStore();
    const httpClient = getHttpClient(authStore);

    return {
        async fetchKnowledgeTree(params: FetchKnowledgeParams = {}): Promise<KnowledgeNode[]> {
            const filterParam = params.filter
                ? JSON.stringify(params.filter)
                : JSON.stringify([
                      { property: 'partner_ids', value: null },
                      { property: 'root_parent_id', value: null },
                  ]);

            const queryParams: Record<string, string> = {
                sAction: 'listingTree',
                filter: filterParam,
                node: params.node || 'root',
            };

            try {
                const data = await httpClient.get<KnowledgeResponse>('admin/v4/kb/', queryParams);

                if (data.success && data.children) {
                    return data.children;
                }

                throw new Error(data.message || 'Failed to fetch knowledge tree');
            } catch (error) {
                console.error('Error fetching knowledge tree:', error);
                throw error;
            }
        },

        async fetchKnowledgeItem(id: string): Promise<KnowledgeResponse> {
            try {
                const data = await httpClient.get<KnowledgeResponse>('admin/v4/kb/', {
                    sAction: 'read',
                    id: id,
                });

                if (data.success) {
                    return data;
                }

                throw new Error(data.message || 'Failed to fetch knowledge item');
            } catch (error) {
                console.error('Error fetching knowledge item:', error);
                throw error;
            }
        },

        async fetchKnowledgeListing(params: ListingParams = {}): Promise<KnowledgeListingResult> {
            // Only use the default filter if no filter is provided
            const filterParam = params.filter
                ? JSON.stringify(params.filter)
                : JSON.stringify([
                      { property: 'type', value: 0 },
                      { property: 'id', value: '_no_filter_' },
                      { property: 'parent_id', value: null },
                      { property: 'partner_ids', operator: 'intersect_set', value: null },
                      { property: 'visibility', operator: '=', value: null },
                      { property: 'status', operator: '=', value: null },
                      { property: 'owner_partner_id', value: '_no_filter_' },
                      { property: 'dict_id', value: null },
                      { property: 'tag_ids', value: null },
                  ]);

            const queryParams: Record<string, string> = {
                sAction: 'listing',
                page: String(params.page || 1),
                start: String(params.start || 0),
                limit: String(params.limit || 50),
                filter: filterParam,
            };

            // Add query parameter if provided
            if (params.query) {
                queryParams.query = params.query;
            }

            // Add sort parameter if provided
            if (params.sort) {
                queryParams.sort = params.sort;
            }

            try {
                const response = await httpClient.get<KnowledgeListingResponse>('admin/v4/kb/', queryParams);

                // Prioritize the kb.results format since that's what the API actually returns
                if (response.success) {
                    if (response.kb?.results) {
                        // Convert to proper array if it's not already
                        const results = Array.isArray(response.kb.results)
                            ? response.kb.results
                            : Object.values(response.kb.results);
                        return {
                            items: results as KnowledgeItem[],
                            totalCount: response.kb.totalCount || 0,
                        };
                    }

                    // Fallback to data.items format just in case
                    if (response.data?.items) {
                        // Convert to proper array if it's not already
                        const items = Array.isArray(response.data.items)
                            ? response.data.items
                            : Object.values(response.data.items);
                        return {
                            items: items as KnowledgeItem[],
                            totalCount: response.data.totalCount || 0,
                        };
                    }
                    console.warn('Response was successful but missing expected data structure:', response);
                }

                throw new Error(response.message || 'Failed to fetch knowledge listing');
            } catch (error) {
                console.error('Error fetching knowledge listing:', error);
                throw error;
            }
        },

        async putRevisions(revisions: KnowledgeRevision[]): Promise<RevisionResponse> {
            try {
                // Create URLSearchParams object for form-encoded data
                const payload = new URLSearchParams();
                payload.append('kb_revisions', JSON.stringify(revisions));
                payload.append('stringify', 'false');

                const data = await httpClient.post<RevisionResponse>(
                    'admin/v4/kb/',
                    payload,
                    {
                        sAction: 'revisionsPut',
                    },
                    {
                        'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
                    }
                );

                if (data.success) {
                    return data;
                }

                throw new Error(data.message || 'Failed to update knowledge revisions');
            } catch (error) {
                console.error('Error updating knowledge revisions:', error);
                throw error;
            }
        },

        async deleteRevision(revisions: KnowledgeRevision[]): Promise<any> {
            try {
                // Create URLSearchParams object for form-encoded data
                const payload = new URLSearchParams();
                payload.append('kb_revisions', JSON.stringify(revisions));
                payload.append('stringify', 'false');

                const data = await httpClient.post<RevisionResponse>(
                    'admin/v4/kb/',
                    payload,
                    {
                        sAction: 'revisionsPut',
                    },
                    {
                        'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
                    }
                );

                if (data.success) {
                    return data;
                }

                throw new Error(data.message || 'Failed to update knowledge revisions');
            } catch (error) {
                console.error('Error updating knowledge revisions:', error);
                throw error;
            }
        },

        async fetchKnowledgeLibraries(): Promise<KnowledgeLibrary[]> {
            try {
                const queryParams: Record<string, string> = {
                    sAction: 'metaKBLibraries',
                    page: '1',
                    start: '0',
                    limit: '25',
                    filter: JSON.stringify([{ property: 'exclude_shared_library', value: true }]),
                };

                // Use the proper API endpoint
                const response = await httpClient.get<any>('admin/v4/core/', queryParams);

                // The API returns libraries in pl__kb_libraries
                if (response && response.success && response.pl__kb_libraries) {
                    const libraries = response.pl__kb_libraries.map((lib: any) => ({
                        id: lib.id || lib.val,
                        name: lib.lbl,
                        description: lib.description,
                        short_name: lib.short_name,
                        partner_id: lib.owner_partner_id,
                    }));

                    return libraries;
                }

                // Try different response formats that the API might return
                if (response && response.success) {
                    // Try different response properties that might contain libraries
                    if (response.data?.results) {
                        const results = Array.isArray(response.data.results)
                            ? response.data.results
                            : Object.values(response.data.results);
                        return results as KnowledgeLibrary[];
                    }

                    if (response.libraries) {
                        const results = Array.isArray(response.libraries)
                            ? response.libraries
                            : Object.values(response.libraries);
                        return results as KnowledgeLibrary[];
                    }

                    if (response.results) {
                        const results = Array.isArray(response.results)
                            ? response.results
                            : Object.values(response.results);
                        return results as KnowledgeLibrary[];
                    }

                    // Try to find libraries in any property that might be an array or object
                    for (const key in response) {
                        if (response[key] && typeof response[key] === 'object' && Array.isArray(response[key])) {
                            const potentialLibraries = response[key];

                            if (
                                potentialLibraries.length > 0 &&
                                (potentialLibraries[0].id || potentialLibraries[0].val) &&
                                potentialLibraries[0].lbl
                            ) {
                                return potentialLibraries.map((lib: any) => ({
                                    id: lib.id || lib.val,
                                    name: lib.lbl,
                                    description: lib.description,
                                    short_name: lib.short_name,
                                    partner_id: lib.owner_partner_id,
                                }));
                            }
                        }
                    }

                    // If we can't find libraries data but the response is successful,
                    // create a mock library using the current category/folder
                    console.warn('Libraries data not found in the expected format, creating mock library');
                    return [
                        {
                            id: 'default',
                            name: 'Default Library',
                        },
                    ];
                }

                throw new Error(response?.message || 'Failed to fetch knowledge libraries');
            } catch (error) {
                console.error('Error fetching knowledge libraries:', error);
                // Return a mock library instead of throwing to prevent UI errors
                return [
                    {
                        id: 'default',
                        name: 'Default Library',
                    },
                ];
            }
        },

        async createKnowledgeArticle(articleData: any): Promise<any> {
            try {
                const payload = {
                    kb: JSON.stringify([articleData]),
                };

                const response = await httpClient.post<RevisionResponse>('admin/v4/kb/', payload, {
                    sAction: 'put',
                });

                if (response.success) {
                    // The API often returns a success message like "Article with id=X saved successfully"
                    // Let's try to extract ID from there first if no direct ID is available
                    if (response.message && response.message.includes('id=')) {
                        const idMatch = response.message.match(/id=([^&\s]+)/);
                        if (idMatch && idMatch[1]) {
                            const extractedId = idMatch[1];

                            // Create a response object with the ID to simplify handling
                            return {
                                success: true,
                                id: extractedId,
                                message: response.message,
                                extractedFromMessage: true,
                            };
                        }
                    }

                    // The API might return the article data in different formats
                    // Let's handle all possible scenarios

                    // Format 1: direct data object with id
                    if (response.data && response.data.id) {
                        return response.data;
                    }

                    // Format 2: data array with objects
                    if (response.data && Array.isArray(response.data) && response.data.length > 0) {
                        return response.data[0];
                    }

                    // Format 3: kb property with item
                    if (response.kb && response.kb.id) {
                        return response.kb;
                    }

                    // Format 4: kb array with objects
                    if (response.kb && Array.isArray(response.kb) && response.kb.length > 0) {
                        return response.kb[0];
                    }

                    // Format 5: result in items property
                    if (response.items && Array.isArray(response.items) && response.items.length > 0) {
                        return response.items[0];
                    }

                    // Format 6: kb_ids array - common response format
                    if (response.kb_ids && Array.isArray(response.kb_ids) && response.kb_ids.length > 0) {
                        return {
                            success: true,
                            id: response.kb_ids[0],
                            message: response.message || 'Article created successfully',
                            extractedFromKbIds: true,
                        };
                    }

                    // If we can't find the article data in any known format, return the whole response
                    return response;
                }

                throw new Error(response.message || 'Failed to create knowledge article');
            } catch (error) {
                console.error('Error creating knowledge article:', error);
                throw error;
            }
        },

        async archiveArticle(articleId: string): Promise<any> {
            try {
                const payload = {
                    kb: JSON.stringify([
                        {
                            _archive: true,
                            id: articleId,
                        },
                    ]),
                };

                const response = await httpClient.post<any>('admin/v4/kb/', payload, {
                    sAction: 'put',
                });

                if (response.success) {
                    return response;
                }

                throw new Error(response.message || 'Failed to archive knowledge article');
            } catch (error) {
                console.error('Error archiving knowledge article:', error);
                throw error;
            }
        },

        async deleteArticle(articleId: string): Promise<any> {
            try {
                const payload = {
                    kb: JSON.stringify([
                        {
                            id: articleId,
                            _archive: true,
                            status: 98,
                        },
                    ]),
                };

                const response = await httpClient.post<any>('admin/v4/kb/', payload, {
                    sAction: 'put',
                });

                if (response.success) {
                    return response;
                }

                throw new Error(response.message || 'Failed to delete knowledge article');
            } catch (error) {
                console.error('Error deleting knowledge article:', error);
                throw error;
            }
        },

        async fetchKnowledgeLabels(rootKbId: string | null = null): Promise<KnowledgeLabel[]> {
            try {
                const filterParams = [
                    { property: 'root_kb_id', value: rootKbId },
                    { property: 'excludeId', value: 'kb.KBTreeModel-1' },
                    { property: 'excludeChildrenId', value: true },
                ];

                const queryParams: Record<string, string> = {
                    sAction: 'metaKBLabels',
                    filter: JSON.stringify(filterParams),
                };

                const response = await httpClient.get<any>('admin/v4/core/', queryParams);

                // The API returns labels in pl__kb_labels format similar to libraries
                if (response && response.success && response.pl__kb_labels) {
                    const labels = response.pl__kb_labels.map((label: any) => ({
                        id: label.id || label.val,
                        title: label.lbl || label.title,
                        parent_id: label.parent_id,
                        root_kb_id: label.root_kb_id,
                        short_name: label.short_name,
                        sub_title: label.sub_title,
                        status: label.status,
                        owner_partner_id: label.owner_partner_id,
                        leaf: label.leaf,
                        parentId: label.parentId,
                    }));

                    // If querying for a specific library, only return labels for that library
                    if (rootKbId) {
                        const filteredLabels = labels.filter(
                            (label: KnowledgeLabel) =>
                                label.root_kb_id === rootKbId ||
                                (label.root_kb_id === undefined && label.id?.includes(rootKbId))
                        );
                        return filteredLabels;
                    }

                    return labels;
                }

                // Try different response formats that the API might return
                if (response && response.success) {
                    // Try different response properties that might contain labels
                    if (response.kb_labels) {
                        return Array.isArray(response.kb_labels)
                            ? response.kb_labels
                            : Object.values(response.kb_labels);
                    }

                    if (response.labels) {
                        return Array.isArray(response.labels) ? response.labels : Object.values(response.labels);
                    }

                    // Try to find labels in any property that might be an array or object
                    for (const key in response) {
                        if (response[key] && typeof response[key] === 'object' && Array.isArray(response[key])) {
                            const potentialLabels = response[key];

                            if (
                                potentialLabels.length > 0 &&
                                ((potentialLabels[0].id && potentialLabels[0].title) ||
                                    (potentialLabels[0].val && potentialLabels[0].lbl))
                            ) {
                                return potentialLabels.map((label: any) => ({
                                    id: label.id || label.val,
                                    title: label.title || label.lbl,
                                    parent_id: label.parent_id,
                                    root_kb_id: label.root_kb_id,
                                    short_name: label.short_name,
                                    sub_title: label.sub_title,
                                    status: label.status,
                                    owner_partner_id: label.owner_partner_id,
                                    leaf: label.leaf,
                                    parentId: label.parentId,
                                }));
                            }
                        }
                    }

                    // If we can't find any labels, return an empty array
                    console.warn('Labels data not found in the expected format');
                    return [];
                }

                throw new Error(response?.message || 'Failed to fetch knowledge labels');
            } catch (error) {
                console.error('Error fetching knowledge labels:', error);
                // Return empty array instead of throwing to prevent UI errors
                return [];
            }
        },

        async createKnowledgeLabel(labelData: KnowledgeLabel): Promise<any> {
            try {
                const payload = {
                    kb_labels: JSON.stringify([labelData]),
                };

                const response = await httpClient.post<LabelsResponse>('admin/v4/kb/', payload, {
                    sAction: 'labelsPut',
                });

                if (response.success) {
                    // Handle different possible response formats
                    if (response.data) {
                        return response.data;
                    }

                    if (response.kb_labels && Array.isArray(response.kb_labels) && response.kb_labels.length > 0) {
                        return response.kb_labels[0];
                    }

                    if (
                        response.kb_label_ids &&
                        Array.isArray(response.kb_label_ids) &&
                        response.kb_label_ids.length > 0
                    ) {
                        return {
                            success: true,
                            id: response.kb_label_ids[0],
                            message: response.message || 'Label created successfully',
                        };
                    }

                    // If we can't find the label data in any known format, return the whole response
                    return response;
                }

                throw new Error(response.message || 'Failed to create knowledge label');
            } catch (error) {
                console.error('Error creating knowledge label:', error);
                throw error;
            }
        },

        async fetchArticleRevisions(articleId: string, params: ListingParams = {}): Promise<ArticleRevisionsResult> {
            try {
                // Set default values for pagination
                const page = params.page || 1;
                const start = params.start || 0;
                const limit = params.limit || 25;

                // Create filter with article ID
                const filter = [{ property: 'kb_id', value: articleId }];

                // Create sort parameters - sort by state ASC then created DESC
                const sort = JSON.stringify([
                    { property: 'state', direction: 'ASC' },
                    { property: 'created', direction: 'DESC' },
                ]);

                const queryParams: Record<string, string> = {
                    sAction: 'revisionsListing',
                    page: String(page),
                    start: String(start),
                    limit: String(limit),
                    sort: sort,
                    filter: JSON.stringify(filter),
                };

                const response = await httpClient.get<ArticleRevisionsResponse>('admin/v4/kb/', queryParams);

                if (response.success) {
                    // Handle different possible response formats
                    if (response.kb_revisions?.results) {
                        const results = Array.isArray(response.kb_revisions.results)
                            ? response.kb_revisions.results
                            : Object.values(response.kb_revisions.results);

                        return {
                            items: results as ArticleRevision[],
                            totalCount: response.kb_revisions.totalCount || 0,
                        };
                    }

                    if (response.data?.items) {
                        const items = Array.isArray(response.data.items)
                            ? response.data.items
                            : Object.values(response.data.items);

                        return {
                            items: items as ArticleRevision[],
                            totalCount: response.data.totalCount || 0,
                        };
                    }

                    // If the API returned success but we can't find the data in any of the expected formats
                    console.warn('Response was successful but missing expected data structure:', response);
                    return {
                        items: [],
                        totalCount: 0,
                    };
                }

                throw new Error(response.message || 'Failed to fetch article revisions');
            } catch (error) {
                console.error('Error fetching article revisions:', error);
                throw error;
            }
        },

        async fetchRelatedArticles(
            revisionId: string,
            revisionUpdated?: string,
            limit: number = 25
        ): Promise<RelatedArticlesResult> {
            try {
                // Build the query array with revision_id
                const queryArray = [{ property: 'revision_id', value: revisionId }];

                // Add revision_updated if provided
                if (revisionUpdated) {
                    queryArray.push({ property: 'revision_updated', value: revisionUpdated });
                }

                const queryParams: Record<string, string> = {
                    sAction: 'metaRelatedArticles',
                    page: '1',
                    start: '0',
                    limit: String(limit),
                    query: JSON.stringify(queryArray),
                };

                const response = await httpClient.get<RelatedArticlesResponse>('admin/v4/kb/', queryParams);

                if (response.success) {
                    // Handle different possible response formats
                    if (response.pl__related_articles) {
                        const results = Array.isArray(response.pl__related_articles)
                            ? response.pl__related_articles
                            : Object.values(response.pl__related_articles);

                        return {
                            items: results as RelatedArticle[],
                            totalCount: results.length || 0,
                        };
                    }

                    if (response.data?.items) {
                        const items = Array.isArray(response.data.items)
                            ? response.data.items
                            : Object.values(response.data.items);

                        return {
                            items: items as RelatedArticle[],
                            totalCount: response.data.totalCount || 0,
                        };
                    }

                    // If the API returned success but we can't find the data in any of the expected formats
                    console.warn('Response was successful but missing expected data structure:', response);
                    return {
                        items: [],
                        totalCount: 0,
                    };
                }

                throw new Error(response.message || 'Failed to fetch related articles');
            } catch (error) {
                console.error('Error fetching related articles:', error);
                return {
                    items: [],
                    totalCount: 0,
                };
            }
        },

        async fetchRevisionById(revisionId: string): Promise<ArticleRevision> {
            try {
                const queryParams: Record<string, string> = {
                    sAction: 'revisionsRead',
                    id: revisionId,
                };

                const response = await httpClient.get<any>('admin/v4/kb/', queryParams);

                if (response.success) {
                    // The API might return the revision data in different formats
                    if (response.data) {
                        return response.data as ArticleRevision;
                    }

                    if (response.kb_revision) {
                        return response.kb_revision as ArticleRevision;
                    }

                    // If we can't find the revision in any known format but the response is successful,
                    // throw an error with the raw response for debugging
                    throw new Error(`Revision data not found in response: ${JSON.stringify(response)}`);
                }

                throw new Error(response.message || 'Failed to fetch revision');
            } catch (error) {
                console.error('Error fetching revision:', error);
                throw error;
            }
        },

        async fetchArticleEvents(articleId: string, params: ListingParams = {}): Promise<KnowledgeEventsResult> {
            try {
                // Set default values for pagination
                const page = params.page || 1;
                const start = params.start || 0;
                const limit = params.limit || 15;

                // Use provided filter if available, otherwise create default filter
                let filter;
                if (params.filter && Array.isArray(params.filter) && params.filter.length > 0) {
                    // Use the filter provided in params
                    filter = params.filter;

                    // Ensure kb_id is set correctly
                    const hasKbId = filter.some(f => f.property === 'kb_id');
                    if (!hasKbId) {
                        filter.push({ property: 'kb_id', value: articleId });
                    }

                    // Ensure limit_diff_log is set
                    const hasLimitDiffLog = filter.some(f => f.property === 'limit_diff_log');
                    if (!hasLimitDiffLog) {
                        filter.push({ value: true, property: 'limit_diff_log' });
                    }
                } else {
                    // Create default filter
                    filter = [
                        { property: 'kb_id', value: articleId },
                        { property: 'root_kb_id', value: '_no_filter_' },
                        { property: 'type', operator: 'ne', value: '_no_filter_' },
                        { value: true, property: 'limit_diff_log' },
                    ];
                }

                // Use provided sort if available, otherwise use default
                const sort = params.sort || JSON.stringify([{ property: 'created', direction: 'DESC' }]);

                const queryParams: Record<string, string> = {
                    sAction: 'listingLog',
                    page: String(page),
                    start: String(start),
                    limit: String(limit),
                    sort: typeof sort === 'string' ? sort : JSON.stringify(sort),
                    filter: JSON.stringify(filter),
                };

                const response = await httpClient.get<EventsResponse>('admin/v4/kb/', queryParams);

                if (response.success) {
                    // Handle different possible response formats
                    if (response.kb_log?.results) {
                        const results = Array.isArray(response.kb_log.results)
                            ? response.kb_log.results
                            : Object.values(response.kb_log.results);

                        // Process events to add icon and color information
                        const processedEvents = results.map((event: unknown) => {
                            const knowledgeEvent = event as KnowledgeEvent;
                            // Determine icon and color based on event type
                            // const eventInfo = this.getEventDisplayInfo(knowledgeEvent.type || '');
                            return {
                                ...knowledgeEvent,
                                type: knowledgeEvent.c__d_type,
                                icon: undefined,
                                color: undefined,
                            };
                        });

                        return {
                            items: processedEvents as KnowledgeEvent[],
                            totalCount: response.kb_log.totalCount || 0,
                        };
                    }

                    if (response.data?.items) {
                        const items = Array.isArray(response.data.items)
                            ? response.data.items
                            : Object.values(response.data.items);

                        // Process events to add icon and color information
                        const processedEvents = items.map((event: unknown) => {
                            const knowledgeEvent = event as KnowledgeEvent;
                            // Determine icon and color based on event type
                            // this.getEventDisplayInfo(knowledgeEvent.type || '', eventInfo);
                            return {
                                ...knowledgeEvent,
                                icon: undefined,
                                color: undefined,
                            };
                        });

                        return {
                            items: processedEvents as KnowledgeEvent[],
                            totalCount: response.data.totalCount || 0,
                        };
                    }

                    // If the API returned success but we can't find the data in any of the expected formats
                    console.warn('Response was successful but missing expected data structure:', response);
                    return {
                        items: [],
                        totalCount: 0,
                    };
                }

                throw new Error(response.message || 'Failed to fetch article events');
            } catch (error) {
                console.error('Error fetching article events:', error);
                return {
                    items: [],
                    totalCount: 0,
                };
            }
        },

        async archiveArticles(articles: object[]): Promise<any> {
            const payload = {
                kb: JSON.stringify(articles),
            };

            return await httpClient.post<RevisionResponse>('admin/v4/kb/', payload, {
                sAction: 'put',
            });
        },

        async unarchiveArticles(articles: object[]): Promise<any> {
            const payload = {
                kb: JSON.stringify(articles),
            };

            return await httpClient.post<RevisionResponse>('admin/v4/kb/', payload, {
                sAction: 'put',
            });
        },

        async unpublishArticles(articles: object[]): Promise<any> {
            const payload = {
                kb: JSON.stringify(articles),
            };

            return await httpClient.post<RevisionResponse>('admin/v4/kb/', payload, {
                sAction: 'put',
            });
        },

        async publishArticles(articles: object[]): Promise<any> {
            const payload = {
                kb: JSON.stringify(articles),
            };

            return await httpClient.post<RevisionResponse>('admin/v4/kb/', payload, {
                sAction: 'put',
            });
        },

        async getPartnerMetadata(): Promise<any> {
            return await httpClient.get<Response>('admin/v4/core/?sAction=metaPartners');
        },

        async shareArticles(articles: any): Promise<any> {
            const payload = {
                kb: JSON.stringify(articles),
            };

            return await httpClient.post<RevisionResponse>('admin/v4/kb/', payload, {
                sAction: 'put',
            });
        },

        async tagArticles(articles: any): Promise<any> {
            const payload = {
                kb: JSON.stringify(articles),
            };

            return await httpClient.post<RevisionResponse>('admin/v4/kb/', payload, {
                sAction: 'put',
            });
        },

        async updateProducts(articles: any): Promise<any> {
            const payload = {
                kb: JSON.stringify(articles),
            };

            return await httpClient.post<RevisionResponse>('admin/v4/kb/', payload, {
                sAction: 'put',
            });
        },

        async loadProducts(query: any): Promise<any> {
            return await httpClient.get<any>(
                'admin/v4/partners/',
                {
                    sAction: 'listingTemplate',
                    page: '1',
                    start: '0',
                    limit: '1000',
                }
            );
        },

        async loadLabels(query: any): Promise<any> {
            return await httpClient.get<any>('admin/v4/core/', query);
        },

        async loadPartnersTeams(query: any): Promise<any> {
            return await httpClient.get<any>('admin/v4/core/', query);
        },

        async fetchKnowledgeFileManager(params: ListingParams = {}): Promise<KnowledgeListingResult> {
            try {
                const queryParams: Record<string, string> = {
                    sAction: 'listingMgr',
                    page: String(params.page || 1),
                    start: String(params.start || 0),
                    limit: String(params.limit || 25),
                };

                if (params.filter) {
                    queryParams.filter = JSON.stringify(params.filter);
                }

                if (params.query) {
                    queryParams.query = params.query;
                }

                if (params.sort) {
                    queryParams.sort = params.sort;
                }

                const response = await httpClient.get<KnowledgeListingResponse>('admin/v4/files/', queryParams);

                if (response.success) {
                    const results = response.upload_files?.results || response.data?.items || [];
                    const totalCount = response.upload_files?.totalCount || response.data?.totalCount || 0;

                    return {
                        items: Array.isArray(results) ? results : Object.values(results),
                        totalCount,
                    };
                }

                throw new Error(response.message || 'Failed to fetch knowledge listing');
            } catch (error) {
                console.error('Error fetching knowledge listing:', error);
                throw error;
            }
        },

        /**
         * Upload a file to the file manager
         * @param file The file to upload
         * @param objectType The type of object the file is associated with (e.g., 'kb_library')
         * @param objectId The ID of the object the file is associated with
         * @param fileTag The tag for the file (e.g., 'managed', 'image', 'attachment')
         * @param onProgress Optional callback for upload progress (0-100)
         * @returns Promise with the upload response
         */
        async postFileManagerFile(
            file: File,
            objectType: string,
            objectId: string,
            fileTag: string = 'managed',
            onProgress?: (progress: number) => void
        ): Promise<FileUploadResponse> {
            try {
                // Create FormData object
                const formData = new FormData();

                // Refresh the CSRF token to ensure we have the latest one
                try {
                    // Get the latest user status which will update the CSRF token
                    await authStore.refreshCsrfToken();

                } catch (tokenError) {
                    console.warn('Failed to refresh CSRF token before upload:', tokenError);
                    // Continue with the current token
                }

                // Add CSRF token - now using the refreshed token
                formData.append('_csrf_token', httpClient.csrf);

                // Add client instance ID
                formData.append('_client_instance_id', httpClient.instanceId);

                // Add file metadata
                formData.append('file_tag', fileTag);
                formData.append('object', objectType);
                formData.append('object_id', objectId);

                // Add the file
                formData.append('fileAttachment[]', file, file.name);



                // Use httpClient for file upload with optional progress tracking
                const response = await httpClient.post<FileUploadResponse>(
                    'admin/v4/files/',
                    formData,
                    { sAction: 'putFile' },
                    undefined, // headers
                    onProgress // progress callback
                );

                if (response.success) {
                    return response as FileUploadResponse;
                }

                throw new Error(response.message || 'Failed to upload file');
            } catch (error) {
                console.error('Error uploading file:', error);
                throw error;
            }
        },

        /**
         * Delete a file from the file manager
         * @param fileId The ID of the file to delete
         * @param objectType The type of object the file is associated with (e.g., 'kb_library')
         * @param objectId The ID of the object the file is associated with
         * @param removeFromArticles Whether to remove the file from articles
         * @returns Promise with the delete response
         */
        async deleteFileManagerFile(
            fileId: string | number,
            objectType: string,
            objectId: string,
            removeFromArticles: boolean = false
        ): Promise<any> {
            try {
                // Prepare the payload for file deletion
                const payload = {
                    sAction: 'mgrDeleteReplace',
                    managed_object: objectType,
                    managed_object_id: objectId,
                    remove_from_articles: removeFromArticles ? 'true' : 'false',
                    upload_files: JSON.stringify([{ id: fileId }]),
                };



                // Make the API call to delete the file
                const response = await fetch('/admin/v4/files/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'x-boomtown-client-instance-id': httpClient.instanceId,
                        'x-boomtown-csrf-token': httpClient.csrf,
                        'x-request-id': crypto.randomUUID(),
                    },
                    body: new URLSearchParams(payload).toString(),
                    credentials: 'include',
                    mode: 'cors',
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (data.success) {
                    return data;
                }

                throw new Error(data.message || 'Failed to delete file');
            } catch (error) {
                console.error('Error deleting file:', error);
                throw error;
            }
        },

        async addLabel(data: object[]): Promise<RevisionResponse> {
            const payload = {
                kb_labels: JSON.stringify(data),
            };

            return await httpClient.post<RevisionResponse>('admin/v4/kb/', payload, {
                sAction: 'labelsPut',
            });
        },

        async deleteLabel(data: object[]) {
            const payload = {
                kb_labels: JSON.stringify(data),
            };

            const response = await httpClient.post<RevisionResponse>('admin/v4/kb/', payload, {
                sAction: 'labelsDelete',
            });

            return response;
        },

        async editLabel(data: object[]) {
            const payload = {
                kb_labels: JSON.stringify(data),
            };

            const response = await httpClient.post<RevisionResponse>('admin/v4/kb/', payload, {
                sAction: 'labelsPut',
            });

            return response;
        },

        // In your tokens.ts or similar file
        async getTokens(partnerId: string): Promise<{ id: string | number; label: string }[]> {
            try {
                if (!partnerId) {
                    console.warn('No partner ID provided for token filtering');
                    return [];
                }

                // Format query params
                const queryParams: Record<string, string> = {
                    sAction: 'metaTokens',
                    page: '1',
                    start: '0',
                    limit: '50',
                    query: JSON.stringify([
                        {
                            property: 'partners_id',
                            value: partnerId,
                        },
                        '',
                    ]),
                };

                // Use httpClient to fetch tokens
                const response = await httpClient.get('admin/v4/core/', queryParams);

                if (response && typeof response === 'object' && 'pl__tokens' in response) {
                    // Process tokens
                    const tokens = Array.isArray(response.pl__tokens)
                        ? response.pl__tokens
                        : Array.isArray(response.pl__tokens)
                          ? response.pl__tokens
                          : (response.pl__tokens as Record<string, unknown>);

                    return (tokens as any[]).map((token: any) => ({
                        id: token.val || token.id,
                        label: token.lbl || String(token.id || token.val),
                    }));
                } else {
                    console.warn('No tokens found in API response:', response);
                    return [];
                }
            } catch (error) {
                console.error('Error fetching tokens:', error);
                return [];
            }
        },

        async fetchTeams(): Promise<TeamAccess[]> {
            try {
                const queryParams: Record<string, string> = {
                    sAction: 'metaPartnersTeamsLinked',
                    page: '1',
                    start: '0',
                    limit: '25',
                    filter: JSON.stringify([{ property: 'isSupportPartner', value: true, operator: '=' }]),
                };

                const response = await httpClient.get<any>('admin/v4/core/', queryParams);

                // Handle the response format from the API
                if (response && response.success) {
                    // Check for the pl__partners_teams_link property first (based on the latest API response)
                    if (response.pl__partners_teams_link) {
                        // The teams are in the format { val: "id", lbl: "name" }
                        return response.pl__partners_teams_link.map((team: any) => ({
                            id: team.val,
                            name: team.lbl,
                            val: team.val,
                            lbl: team.lbl,
                            description: team.description,
                        }));
                    }

                    // Try different response properties that might contain teams
                    if (response.pl__teams) {
                        const teams = response.pl__teams.map((team: any) => ({
                            id: team.id || team.val,
                            name: team.name || team.lbl,
                            val: team.val || team.id,
                            lbl: team.lbl || team.name,
                            description: team.description,
                        }));
                        return teams;
                    }

                    if (response.data?.results) {
                        const results = Array.isArray(response.data.results)
                            ? response.data.results
                            : Object.values(response.data.results);

                        const teams = results.map((team: any) => ({
                            id: team.id || team.val,
                            name: team.name || team.lbl,
                            val: team.val || team.id,
                            lbl: team.lbl || team.name,
                            description: team.description,
                        }));

                        return teams;
                    }

                    // If we can't find teams data but the response is successful,
                    // return an empty array
                    console.warn('Teams data not found in the expected format');
                    return [];
                }

                throw new Error(response?.message || 'Failed to fetch teams');
            } catch (error) {
                console.error('Error fetching teams:', error);
                // Return empty array instead of throwing to prevent UI errors
                return [];
            }
        },

        /**
         * Fetches organizations that can be used for article access control
         * @returns Promise with an array of organizations with lbl/val properties
         */
        async fetchOrganizations(): Promise<{ lbl: string; val: string | number }[]> {
            try {
                const queryParams: Record<string, string> = {
                    sAction: 'metaPartners',
                };

                const response = await httpClient.get<any>('admin/v4/core/', queryParams);

                // The API returns partner organizations in pl__all_partners
                if (response && response.success && response.pl__all_partners) {
                    const organizations = response.pl__all_partners.map((org: any) => ({
                        lbl: org.name || org.lbl,
                        val: org.id || org.val,
                    }));

                    return organizations;
                }

                // Fallback to pl__partners if pl__all_partners is not available
                if (response && response.success && response.pl__partners) {
                    const organizations = response.pl__partners.map((org: any) => ({
                        lbl: org.name || org.lbl,
                        val: org.id || org.val,
                    }));

                    return organizations;
                }

                // Try different response formats that the API might return
                if (response && response.success) {
                    // Try other possible response properties
                    if (response.partners) {
                        return response.partners.map((org: any) => ({
                            lbl: org.name || org.lbl,
                            val: org.id || org.val,
                        }));
                    }

                    // Check any property that might be an array of organization objects
                    for (const key in response) {
                        if (response[key] && typeof response[key] === 'object' && Array.isArray(response[key])) {
                            const potentialOrgs = response[key];

                            if (
                                potentialOrgs.length > 0 &&
                                (potentialOrgs[0].id || potentialOrgs[0].val) &&
                                (potentialOrgs[0].name || potentialOrgs[0].lbl)
                            ) {
                                return potentialOrgs.map((org: any) => ({
                                    lbl: org.name || org.lbl,
                                    val: org.id || org.val,
                                }));
                            }
                        }
                    }
                }

                console.warn('Could not find organizations in API response:', response);
                return [];
            } catch (error) {
                console.error('Error fetching organizations:', error);
                throw error;
            }
        },

        async fetchAvailableKnowledgeBases(currentKbIds: string[] = []): Promise<KnowledgeBaseArticle[]> {
            try {
                const query = [
                    {
                        property: 'current_kb_id',
                        bind: { value: '{relatedArticles}' },
                        operator: 'in_set',
                        value: null,
                    },
                    {
                        property: 'status',
                        value: [0, 1],
                        operator: 'in_set',
                    },
                ];

                const queryParams: Record<string, string> = {
                    sAction: 'metaKBs',
                    page: '1',
                    start: '0',
                    limit: '500',
                    query: JSON.stringify(query),
                };

                const response = await httpClient.get<KnowledgeBasesResponse>('admin/v4/core/', queryParams);

                if (response.success && response.pl__kbs) {
                    return response.pl__kbs;
                }

                throw new Error(response.message || 'Failed to fetch available knowledge bases');
            } catch (error) {
                console.error('Error fetching available knowledge bases:', error);
                return [];
            }
        },

        async fetchKnowledgeBaseTree(
            node: string = 'root',
            query?: string,
            filters: { property: string; value: any }[] = []
        ): Promise<any[]> {
            try {
                // Ensure auth store is initialized
                await authStore.initialize();

                // Format base query parameters
                const queryParams: Record<string, string> = {
                    sAction: 'metaKBsTree',
                    node: node
                };

                    // Build the complete filter array
                    const fullFilters = [...filters];

                    // Add search query if provided
                    if (query && query.trim()) {
                        fullFilters.push({
                            property: 'query',
                            value: query.trim(),
                        });
                    }

                    // Add filters if any exist
                    if (fullFilters.length > 0) {
                                            queryParams.filter = JSON.stringify(fullFilters);
                }

                // Make the API request using httpClient with trailing slash to ensure proper URL formatting
                const response = await httpClient.get('admin/v4/core/', queryParams);

                // Check if we have a valid response with the expected data structure
                if (response && typeof response === 'object' && 'tree__kbs' in response) {
                    return Array.isArray(response.tree__kbs)
                        ? response.tree__kbs
                        : (response.tree__kbs as Record<string, any>) ? Object.values(response.tree__kbs as Record<string, any>) : [];
                } else {
                    console.warn('No knowledge base tree found in API response:', response);
                    return [];
                }
            } catch (error) {
                console.error('Error fetching knowledge base tree:', error);
                // Return empty array instead of throwing to prevent UI errors
                return [];
            }
        },

        async updateArticleField(revisionId: string, field: string, value: any) {
            try {
                const revision: KnowledgeRevision & Record<string, any> = { id: revisionId };
                revision[field] = value;
                const response = await httpClient.post<RevisionResponse>(
                    'admin/v4/kb/',
                    { kb_revisions: JSON.stringify([revision]) },
                    {
                        sAction: 'revisionsPut',
                    }
                );
                return response;
            } catch (error) {
                console.error('Error updating article field:', error);
                throw error;
            }
        },

        async updateArticleLibrary(article: any, rootParentId: string): Promise<RevisionResponse> {
            const payload = new URLSearchParams();
            payload.append('kb', JSON.stringify([{ root_parent_id: rootParentId, id: article.kb_id, title: article.title }]));
            payload.append('stringify', 'false');
            return await httpClient.post<RevisionResponse>(
                'admin/v4/kb/',
                payload,
                { sAction: 'put' },
                { 'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' }
            );
        },

        async fetchKnowledgeBaseOptionTree(
            node: string,
            query?: string,
            filters: { property: string; value: any }[] = []
        ): Promise<any[]> {
            try {
                // Format base query parameters
                const queryParams: Record<string, string> = {
                    sAction: 'metaKBsTree',
                    node: node,
                };

                // Build the complete filter array
                const fullFilters = [...filters];

                // Add search query if provided
                if (query && query.trim()) {
                    fullFilters.push({
                        property: 'query',
                        value: query.trim(),
                    });
                }

                // Add filters if any exist
                if (fullFilters.length > 0) {
                    queryParams.filter = JSON.stringify(fullFilters);
                }

                // Make the API request using httpClient
                const response = await httpClient.get('admin/v4/core', queryParams);

                // Check if we have a valid response with the expected data structure
                if (response && typeof response === 'object' && 'tree__kbs' in response) {
                    return Array.isArray(response.tree__kbs)
                        ? response.tree__kbs
                        : (response.tree__kbs as Record<string, any>)
                          ? Object.values(response.tree__kbs as Record<string, any>)
                          : [];
                } else {
                    console.warn('No knowledge base tree found in API response:', response);
                    return [];
                }
            } catch (error) {
                console.error('Error fetching knowledge base tree:', error);
                throw error;
            }
        },

        async getKBsTree(params: KBTreeParams = {}): Promise<KBTreeItem[]> {
            try {
                // Base query params
                const queryParams: Record<string, string> = {
                    sAction: 'metaKBsTree',
                    node: params.node || 'root',
                };

                // Add node parameter if provided
                //   if (params.node) {
                //     queryParams.node = params.node;
                //   }

                // Build filter array
                const filters = [];

                // Add search query if provided
                if (params.query) {
                    filters.push({
                        property: 'query',
                        value: params.query,
                    });
                }

                // Add additional filters if provided
                if (params.filters && params.filters.length) {
                    filters.push(...params.filters);
                }

                // Only add filter parameter if we have filters
                if (filters.length > 0) {
                    queryParams.filter = JSON.stringify(filters);
                }

                // Use httpClient to fetch the KB tree
                const response = await httpClient.get('admin/v4/core/', queryParams);

                if (response && typeof response === 'object' && 'tree__kbs' in response) {
                    // Extract the tree data from the response
                    const kbTree = response.tree__kbs;

                    if (Array.isArray(kbTree)) {
                        return kbTree;
                    } else {
                        console.warn('KB tree data is not an array:', kbTree);
                        return [];
                    }
                } else {
                    console.warn('No KB tree found in API response:', response);
                    return [];
                }
            } catch (error) {
                console.error('Error fetching KB tree:', error);
                return [];
            }
        },

        async fetchKnowledgeListingArticle(params: ListingParams = {}): Promise<KnowledgeListingResult> {
            // Only use the default filter if no filter is provided
            const filterParam = params.filter
                ? JSON.stringify(params.filter)
                : JSON.stringify([
                      { property: 'type', value: 0 },
                      { property: 'id', value: '_no_filter_' },
                      { property: 'parent_id', value: null },
                      { property: 'partner_ids', operator: 'intersect_set', value: null },
                      { property: 'visibility', operator: '=', value: null },
                      { property: 'status', operator: '=', value: null },
                      { property: 'owner_partner_id', value: '_no_filter_' },
                      { property: 'dict_id', value: null },
                      { property: 'tag_ids', value: null },
                  ]);

            const queryParams: Record<string, string> = {
                sAction: 'listing',
                page: String(params.page || 1),
                start: String(params.start || 0),
                limit: String(params.limit || 50),
                filter: filterParam,
            };

            // Add query parameter if provided
            if (params.query) {
                queryParams.query = params.query;
            }

            // Add sort parameter if provided
            if (params.sort) {
                queryParams.sort = params.sort;
            }

            try {
                const response = await httpClient.get<KnowledgeListingResponse>('admin/v4/kb/', queryParams);

                // Prioritize the kb.results format since that's what the API actually returns
                if (response.success) {
                    if (response.kb?.results) {
                        // Convert to proper array if it's not already
                        const results = Array.isArray(response.kb.results)
                            ? response.kb.results
                            : Object.values(response.kb.results);
                        return {
                            items: results as KnowledgeItem[],
                            totalCount: response.kb.totalCount || 0,
                        };
                    }

                    // Fallback to data.items format just in case
                    if (response.data?.items) {
                        // Convert to proper array if it's not already
                        const items = Array.isArray(response.data.items)
                            ? response.data.items
                            : Object.values(response.data.items);
                        return {
                            items: items as KnowledgeItem[],
                            totalCount: response.data.totalCount || 0,
                        };
                    }
                    console.warn('Response was successful but missing expected data structure:', response);
                }

                throw new Error(response.message || 'Failed to fetch knowledge listing');
            } catch (error) {
                console.error('Error fetching knowledge listing:', error);
                throw error;
            }
        },

        getEventDisplayInfo(eventType: string): { icon: string; color: string } {
            // Map event types to appropriate icons and colors
            switch (eventType.toLowerCase()) {
                case 'create':
                    return { icon: 'pi pi-plus-circle', color: '#4CAF50' }; // Green for creation
                case 'update':
                case 'edit':
                    return { icon: 'pi pi-pencil', color: '#2196F3' }; // Blue for updates
                case 'publish':
                    return { icon: 'pi pi-check-circle', color: '#8BC34A' }; // Light green for publishing
                case 'draft':
                    return { icon: 'pi pi-file', color: '#FF9800' }; // Orange for drafts
                case 'archive':
                    return { icon: 'pi pi-inbox', color: '#9E9E9E' }; // Gray for archive
                case 'delete':
                    return { icon: 'pi pi-trash', color: '#F44336' }; // Red for deletion
                case 'view':
                    return { icon: 'pi pi-eye', color: '#673AB7' }; // Purple for views
                case 'comment':
                    return { icon: 'pi pi-comment', color: '#00BCD4' }; // Cyan for comments
                default:
                    return { icon: 'pi pi-info-circle', color: '#607D8B' }; // Default blue-gray
            }
        },

        async fetchKnowledgeComments(params: ListingParams = {}): Promise<EventsResponse> {
            try {
                // Set default parameters if not provided
                const defaultParams: ListingParams = {
                    page: 1,
                    start: 0,
                    limit: 10,
                    sort: JSON.stringify([{ property: 'created', direction: 'DESC' }]),
                    filter: [
                        { property: 'root_kb_id', value: '_no_filter_' },
                        { property: 'type', value: 22 },
                        { property: 'comment_resolved', value: '', operator: 'nl' },
                    ],
                };

                // Use provided filter as-is if present, otherwise use default
                const mergedParams = {
                    ...defaultParams,
                    ...params,
                    filter: params.filter ? params.filter : defaultParams.filter,
                };

                // Prepare query params for the API call
                const queryParams: Record<string, string> = {
                    sAction: 'listingLog',
                    page: String(mergedParams.page),
                    start: String(mergedParams.start),
                    limit: String(mergedParams.limit),
                    sort: typeof mergedParams.sort === 'string' ? mergedParams.sort : JSON.stringify(mergedParams.sort),
                    filter: JSON.stringify(mergedParams.filter),
                };

                const response = await httpClient.get<EventsResponse>('admin/v4/kb/', queryParams);

                if (response.success) {
                    return response;
                }

                throw new Error(response.message || 'Failed to fetch knowledge comments');
            } catch (error) {
                console.error('Error fetching knowledge comments:', error);
                throw error;
            }
            return Promise.reject(new Error('fetchKnowledgeComments: unexpected exit'));
        },

        async resolveComment(commentId: string): Promise<any> {
            try {
                const payload = {
                    kb_log: JSON.stringify([{ _resolveComment: true, id: commentId }]),
                };

                const response = await httpClient.post<any>(
                    'admin/v4/kb/',
                    payload,
                    { sAction: 'putLog' },
                    { 'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' }
                );

                if (response.success) {
                    return response;
                }
                throw new Error(response.message || 'Failed to resolve comment');
            } catch (error) {
                console.error('Error resolving comment:', error);
                throw error;
            }
            return Promise.reject(new Error('resolveComment: unexpected exit'));
        },

        async addComment({
            kb_id,
            root_kb_id,
            notes,
        }: {
            kb_id: string;
            root_kb_id: string;
            notes: string;
        }): Promise<any> {
            try {
                const payload = {
                    kb_log: JSON.stringify([
                        {
                            kb_id,
                            root_kb_id,
                            type: 22,
                            notes,
                        },
                    ]),
                };
                const response = await httpClient.post<any>(
                    'admin/v4/kb/',
                    payload,
                    { sAction: 'putLog' },
                    { 'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' }
                );
                if (response.success) {
                    return response;
                }
                throw new Error(response.message || 'Failed to add comment');
            } catch (error) {
                console.error('Error adding comment:', error);
                throw error;
            }
            return Promise.reject(new Error('addComment: unexpected exit'));
        },
    };
}
