import { ref, onMounted } from 'vue';
import { useAuthStore } from '@/stores/auth';

/**
 * Composable for managing remember me functionality
 */
export function useRememberMe() {
    const authStore = useAuthStore();
    const rememberedEmail = ref('');
    const isRemembered = ref(false);

    /**
     * Get the remembered email from localStorage
     */
    function getRememberedEmail(): string {
        try {
            const email = localStorage.getItem('rememberedEmail') || '';
            return email;
        } catch (error) {
            console.warn('🔐 RememberMe: Failed to get remembered email from localStorage:', error);
            return '';
        }
    }

    /**
     * Set the remembered email in localStorage
     */
    function setRememberedEmail(email: string): void {
        try {
            if (email) {
                localStorage.setItem('rememberedEmail', email);
            } else {
                localStorage.removeItem('rememberedEmail');
            }
        } catch (error) {
            console.warn('🔐 RememberMe: Failed to set remembered email in localStorage:', error);
        }
    }

    /**
     * Check if remember me cookie exists and load remembered email
     */
    function checkRememberMe(): void {
        isRemembered.value = authStore.hasRememberMeCookie();
        
        if (isRemembered.value) {
            rememberedEmail.value = getRememberedEmail();
        }
    }

    /**
     * Handle remember me on successful login
     */
    function handleRememberMe(email: string, shouldRemember: boolean): void {
        if (shouldRemember) {
            setRememberedEmail(email);
            authStore.setRememberMeCookie();
        } else {
            // If user unchecks remember me, clear the stored email
            setRememberedEmail('');
            authStore.clearRememberMeCookie();
        }
    }

    // Initialize on mount
    onMounted(() => {
        checkRememberMe();
    });

    return {
        rememberedEmail,
        isRemembered,
        getRememberedEmail,
        setRememberedEmail,
        checkRememberMe,
        handleRememberMe
    };
} 