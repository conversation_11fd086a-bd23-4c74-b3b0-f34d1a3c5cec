import { useUserStore } from '@/stores/user';
import { watch, ref } from 'vue';

export function useMessenger() {
  const userStore = useUserStore();
  const lastIdentifiedUser = ref<string | null>(null);

  /**
   * Identify the current user to the messenger
   */
  function identifyUser(retryCount = 0) {
    const userData = userStore.userData;
    
    if (!userData) {
      console.warn('No user data available for messenger identification');
      return;
    }

    // Create a unique identifier for the current user data
    const userIdentifier = `${userData.email || ''}-${userData.first_name || ''}-${userData.full_name || ''}`;
    
    // Skip if we've already identified this exact user data
    if (lastIdentifiedUser.value === userIdentifier) {
      return;
    }

    // Prepare user data for messenger (simplified since global function handles the details)
    const messengerUserData = {
      email: userData.email || '',
      phone: userData.phone_number || '',
      firstName: userData.first_name || userData.full_name || userData.name || '',
    };

    // Use the global function defined in messenger-init.js
    if (typeof (window as any).identifyMessengerUser === 'function') {
      (window as any).identifyMessengerUser(messengerUserData);
      lastIdentifiedUser.value = userIdentifier;
    } else {
      // Add retry logic if the function is not available yet
      if (retryCount < 5) {
        setTimeout(() => {
          identifyUser(retryCount + 1);
        }, 1000);
      } else {
        console.warn('identifyMessengerUser function failed to load after 5 attempts, giving up');
      }
    }
  }

  /**
   * Show the messenger widget
   */
  function showMessenger() {
    const messenger = (window as any).OvationMessenger;
    if (messenger && typeof messenger.show === 'function') {
      messenger.show();
    } else {
      console.error('OvationMessenger is not available');
    }
  }

  /**
   * Hide the messenger widget
   */
  function hideMessenger() {
    const messenger = (window as any).OvationMessenger;
    if (messenger && typeof messenger.hide === 'function') {
      messenger.hide();
    } else {
      console.warn('OvationMessenger hide function is not available');
    }
  }

  /**
   * Initialize messenger integration
   */
  function initializeMessenger() {
    // Watch for user data changes and identify user when available
    watch(
      () => userStore.userData,
      (newUserData) => {
        if (newUserData) {
          // Add a small delay to ensure messenger is loaded
          setTimeout(() => {
            identifyUser();
          }, 1000);
        } else {
          // Clear the last identified user when user logs out
          lastIdentifiedUser.value = null;
        }
      },
      { immediate: true }
    );
  }

  return {
    identifyUser,
    showMessenger,
    hideMessenger,
    initializeMessenger,
  };
} 