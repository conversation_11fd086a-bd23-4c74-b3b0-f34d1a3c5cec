import { create<PERSON><PERSON>, setActive<PERSON>inia } from 'pinia';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { useKnowledgeStore } from '../../../src/modules/knowledge/stores/knowledge';

// Mock the knowledge API
const mockKnowledgeAPI = {
    fetchKnowledgeTree: vi.fn(),
    fetchKnowledgeItem: vi.fn(),
    fetchKnowledgeListing: vi.fn(),
    putRevisions: vi.fn(),
};

// Mock the entire composable
vi.mock('@/composables/services/useKnowledgeAPI', () => ({
    useKnowledgeAPI: () => mockKnowledgeAPI
}));

describe('Knowledge Store', () => {
    beforeEach(() => {
        setActivePinia(createPinia());
        vi.clearAllMocks();
    });

    describe('fetchKnowledgeTree', () => {
        it('should update treeNodes with fetched data', async () => {
            const store = useKnowledgeStore();
            const mockTreeNodes = [
                {
                    id: '1',
                    text: 'Root Node',
                    leaf: false,
                    children: [
                        {
                            id: '1-1',
                            text: 'Child Node',
                            leaf: true,
                        },
                    ],
                },
            ];

            mockKnowledgeAPI.fetchKnowledgeTree.mockResolvedValue(mockTreeNodes);

            await store.fetchKnowledgeTree();

            expect(store.treeNodes).toEqual(mockTreeNodes);
            expect(store.loading).toBe(false);
            expect(store.error).toBeNull();
        });

        it('should set error state when fetch fails', async () => {
            const store = useKnowledgeStore();
            const errorMessage = 'Failed to fetch tree';

            mockKnowledgeAPI.fetchKnowledgeTree.mockRejectedValue(new Error(errorMessage));

            await store.fetchKnowledgeTree();

            expect(store.error).toBe(errorMessage);
            expect(store.loading).toBe(false);
        });
    });

    describe('fetchKnowledgeItem', () => {
        it('should update currentNode with fetched data', async () => {
            const store = useKnowledgeStore();
            const mockItem = {
                id: '123',
                title: 'Test Item',
                content: 'Test content',
            };
            const mockResponse = {
                success: true,
                data: mockItem,
            };

            mockKnowledgeAPI.fetchKnowledgeItem.mockResolvedValue(mockResponse);

            await store.fetchKnowledgeItem('123');

            expect(store.currentNode).toEqual(mockItem);
            expect(store.loading).toBe(false);
            expect(store.error).toBeNull();
        });

        it('should set error state when fetch fails', async () => {
            const store = useKnowledgeStore();
            const errorMessage = 'Failed to fetch item';

            mockKnowledgeAPI.fetchKnowledgeItem.mockRejectedValue(new Error(errorMessage));

            await expect(store.fetchKnowledgeItem('123')).rejects.toThrow();
            expect(store.error).toBe(errorMessage);
            expect(store.loading).toBe(false);
        });
    });

    describe('fetchKnowledgeListing', () => {
        it('should update items with fetched listing data', async () => {
            const store = useKnowledgeStore();
            const nodeId = '10b50a34-4f2d-4ed3-957d-23c5b835639b';
            const mockItems = [
                {
                    id: '1',
                    title: 'Article 1',
                    content: 'Content 1',
                    type: 'Article',
                    status: 'published',
                },
                {
                    id: '2',
                    title: 'Article 2',
                    content: 'Content 2',
                    type: 'File',
                    status: 'draft',
                },
            ];

            // Use the new KnowledgeListingResult format
            const mockResponse = {
                items: mockItems,
                totalCount: 42, // Total count from API
            };

            mockKnowledgeAPI.fetchKnowledgeListing.mockResolvedValue(mockResponse);

            await store.fetchKnowledgeListing({
                rootParentId: nodeId,
                parentId: null,
            });

            expect(store.items).toEqual(mockItems);
            expect(store.currentRootId).toBe(nodeId);
            expect(store.totalCount).toBe(42);
            expect(store.loadingContent).toBe(false);
            expect(store.error).toBeNull();

            // Verify that the API was called with correct filter parameters
            expect(mockKnowledgeAPI.fetchKnowledgeListing).toHaveBeenCalledWith(
                expect.objectContaining({
                    filter: expect.arrayContaining([
                        expect.objectContaining({
                            property: 'root_parent_id',
                            value: nodeId,
                        }),
                    ]),
                })
            );
        });

        it('should set error state when fetch fails', async () => {
            const store = useKnowledgeStore();
            const errorMessage = 'Failed to fetch listing';
            const nodeId = '10b50a34-4f2d-4ed3-957d-23c5b835639b';

            mockKnowledgeAPI.fetchKnowledgeListing.mockRejectedValue(new Error(errorMessage));

            await expect(
                store.fetchKnowledgeListing({
                    rootParentId: nodeId,
                    parentId: null,
                })
            ).rejects.toThrow();
            expect(store.error).toBe(errorMessage);
            expect(store.loadingContent).toBe(false);
        });
    });

    describe('filteredItems', () => {
        it('should filter items by search query', () => {
            const store = useKnowledgeStore();

            store.items = [
                { id: '1', title: 'API Documentation', content: '', status: 'published' },
                { id: '2', title: 'Database Schema', content: '', status: 'published' },
            ];

            store.searchQuery = 'api';

            expect(store.filteredItems.length).toBe(1);
            expect(store.filteredItems[0].id).toBe('1');
        });

        it('should filter items by status', () => {
            const store = useKnowledgeStore();

            store.items = [
                { id: '1', title: 'Article 1', content: '', status: 'published' },
                { id: '2', title: 'Article 2', content: '', status: 'draft' },
            ];

            store.statusFilter = 'draft';

            expect(store.filteredItems.length).toBe(1);
            expect(store.filteredItems[0].id).toBe('2');
        });
    });

    describe('updateItem', () => {
        it('should update an item in the store', () => {
            const store = useKnowledgeStore();
            const mockItem = {
                id: '123',
                title: 'Original Title',
                content: 'Original content',
                status: 'draft',
            };

            store.items = [mockItem];

            store.updateItem('123', { title: 'Updated Title' }, '456');

            expect(store.items[0].title).toBe('Updated Title');
            expect(store.items[0].content).toBe('Original content');
        });

        it('should call saveItemRevision when content is updated', () => {
            const store = useKnowledgeStore();
            const saveRevisionSpy = vi.spyOn(store, 'saveItemRevision').mockImplementation(() => Promise.resolve());

            const mockItem = {
                id: '123',
                title: 'Test Article',
                content: 'Original content',
                status: 'draft',
            };

            store.items = [mockItem];

            const newContent = 'Updated content';
            store.updateItem('123', { content: newContent }, '456');

            expect(store.items[0].content).toBe(newContent);
            expect(saveRevisionSpy).toHaveBeenCalledWith('123', newContent, '456', false, false, undefined, undefined);
        });

        it('should not call saveItemRevision when content is not updated', () => {
            const store = useKnowledgeStore();
            const saveRevisionSpy = vi.spyOn(store, 'saveItemRevision').mockImplementation(() => Promise.resolve());

            const mockItem = {
                id: '123',
                title: 'Test Article',
                content: 'Original content',
                status: 'draft',
            };

            store.items = [mockItem];

            store.updateItem('123', { title: 'New Title' }, '456');

            expect(store.items[0].title).toBe('New Title');
            expect(saveRevisionSpy).not.toHaveBeenCalled();
        });
    });

    describe('saveItemRevision', () => {
        it('should save a revision using the API', async () => {
            const store = useKnowledgeStore();
            const mockResponse = { success: true, message: 'Revision saved' };

            vi.mocked(mockKnowledgeAPI.putRevisions).mockResolvedValue(mockResponse);

            const result = await store.saveItemRevision('123', '<bt/><div>Updated content</div>', '456');

            // Check that putRevisions was called with the correct structure
            expect(mockKnowledgeAPI.putRevisions).toHaveBeenCalledWith([
                expect.objectContaining({
                    id: '456',
                    body: expect.stringContaining('<bt/>')
                })
            ]);

            // Verify the body contains the expected content elements
            const calledArgs = vi.mocked(mockKnowledgeAPI.putRevisions).mock.calls[0][0];
            const body = calledArgs[0].body;
            
            // Should contain the original content
            expect(body).toContain('<div>Updated content</div>');
            // Should start with <bt/> (from saveItemRevision method)
            expect(body).toMatch(/^<bt\/>/);

            expect(result).toEqual(mockResponse);
        });

        it('should handle errors without throwing', async () => {
            const store = useKnowledgeStore();
            const errorMessage = 'Failed to save revision';

            vi.mocked(mockKnowledgeAPI.putRevisions).mockRejectedValue(new Error(errorMessage));

            await store.saveItemRevision('123', '<bt/><div>Content</div>', '456');

            expect(store.error).toBe(errorMessage);
            // The method should not throw, so the test should complete without errors
        });
    });
});
