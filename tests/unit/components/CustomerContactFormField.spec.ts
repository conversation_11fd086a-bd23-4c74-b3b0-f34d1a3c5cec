import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import CustomerContactForm<PERSON>ield from '@/modules/inbox/components/CustomerContactFormField.vue'
import { createTestingPinia } from '@pinia/testing'
import PrimeVue from 'primevue/config'
import ToastService from 'primevue/toastservice'

// Mock the BravoFormField component
vi.mock('@/modules/knowledge/components/BravoFormField.vue', () => ({
  default: {
    name: 'BravoFormField',
    template: `
      <div data-testid="bravo-form-field">
        <slot name="footer"></slot>
      </div>
    `,
    props: [
      'label', 'fieldName', 'value', 'displayValue', 'inputType', 'displayType',
      'options', 'optionLabel', 'optionValue', 'isLoading', 'isHorizontal',
      'isEditing', 'isSaving', 'noValueText', 'dataTestId'
    ],
    emits: ['update', 'save', 'cancel'],
    expose: () => ({
      handleSaveComplete: vi.fn(),
      startEditing: vi.fn()
    })
  }
}))

// Mock PrimeVue Dialog component
vi.mock('primevue/dialog', () => ({
  default: {
    name: 'Dialog',
    props: {
      visible: Boolean,
      modal: Boolean,
      header: String,
      style: Object,
      class: [String, Object, Array]
    },
    template: `
      <div v-if="visible" data-testid="dialog">
        <div class="p-dialog-header">{{ header }}</div>
        <div class="p-dialog-content"><slot /></div>
        <div class="p-dialog-footer"><slot name="footer" /></div>
      </div>
    `,
    emits: ['update:visible']
  }
}))

// Mock BravoDialog component
vi.mock('@services/ui-component-library/components/BravoDialog.vue', () => ({
  default: {
    name: 'BravoDialog',
    props: {
      visible: Boolean,
      modal: Boolean,
      header: String,
      style: Object,
      class: [String, Object, Array]
    },
    template: `
      <div v-if="visible" data-testid="bravo-dialog">
        <div class="dialog-header">{{ header }}</div>
        <div class="dialog-content"><slot /></div>
        <div class="dialog-footer"><slot name="footer" /></div>
      </div>
    `,
    emits: ['update:visible']
  }
}))

// Mock the meta store
const mockMetaStore = {
  fetchMembersUsers: vi.fn().mockResolvedValue({
    pl__members_users: [
      { id: '1', lbl: 'John Doe', val: 'user1' },
      { id: '2', lbl: 'Jane Smith', val: 'user2' }
    ]
  })
}

vi.mock('@/stores/meta', () => ({
  useMetaStore: () => mockMetaStore
}))

describe('CustomerContactFormField', () => {
  let wrapper: any

  const defaultProps = {
    label: 'Customer Contact',
    fieldName: 'members_users_id',
    value: 'user1',
    displayValue: 'John Doe',
    issue: {
      id: 'test-issue',
      members_id: 'member1',
      members_locations_id: 'location1',
      sponsor_partners_id: 'partner1',
      owner_partners_id: 'partner1'
    }
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  const createWrapper = (props = {}) => {
    return mount(CustomerContactFormField, {
      props: { ...defaultProps, ...props },
      global: {
        plugins: [
          createTestingPinia({
            createSpy: vi.fn,
            stubActions: false
          }),
          PrimeVue,
          ToastService
        ],
        stubs: {
          BravoDialog: true,
          AddContact: true,
          Dialog: true,
          Button: true
        }
      }
    })
  }

  it('renders correctly', () => {
    wrapper = createWrapper()
    expect(wrapper.find('[data-testid="bravo-form-field"]').exists()).toBe(true)
  })

  it('has footer slot with Add Contact functionality', () => {
    wrapper = createWrapper()
    // Since the BravoFormField mock renders the footer slot, we can check for its presence
    const bravoFormField = wrapper.find('[data-testid="bravo-form-field"]')
    expect(bravoFormField.exists()).toBe(true)
    // The actual button rendering depends on the dropdown being open, which is complex to test
    // in a unit test environment. We'll verify the component structure instead.
  })

  it('handles add contact functionality', async () => {
    wrapper = createWrapper()
    
    // Test the handleAddContact method directly since the button is in a complex dropdown
    await wrapper.vm.handleAddContact()
    
    expect(wrapper.emitted('add-contact')).toBeTruthy()
    expect(wrapper.emitted('add-contact')).toHaveLength(1)
  })

  it('fetches members users on mount when issue is provided', async () => {
    wrapper = createWrapper()
    
    // Wait for the component to mount and fetch data
    await wrapper.vm.$nextTick()
    
    expect(mockMetaStore.fetchMembersUsers).toHaveBeenCalledWith({
      members_id: 'member1',
      members_locations_id: 'location1',
      sponsor_id: 'partner1',
      context_org_id: 'partner1'
    })
  })

  it('computes display value correctly when user is found', async () => {
    wrapper = createWrapper({ value: 'user1' })
    
    // Wait for data to be fetched
    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 0))
    
    expect(wrapper.vm.computedDisplayValue).toBe('John Doe')
  })

  it('shows empty state when user is not found', async () => {
    wrapper = createWrapper({ value: 'unknown-user' })
    
    // Wait for data to be fetched
    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 0))
    
    expect(wrapper.vm.computedDisplayValue).toBe('—')
  })

  it('shows loading state when fetching data', () => {
    wrapper = createWrapper()
    
    // Initially should show loading
    expect(wrapper.vm.computedDisplayValue).toBe('Loading...')
  })

  it('forwards update event correctly', () => {
    wrapper = createWrapper()
    
    wrapper.vm.handleFieldUpdate('members_users_id', 'user2')
    
    expect(wrapper.emitted('update')).toBeTruthy()
    expect(wrapper.emitted('update')[0]).toEqual(['members_users_id', 'user2'])
  })

  it('forwards save event correctly', () => {
    wrapper = createWrapper()
    
    wrapper.vm.handleFieldSave('members_users_id', 'user2')
    
    expect(wrapper.emitted('save')).toBeTruthy()
    expect(wrapper.emitted('save')[0]).toEqual(['members_users_id', 'user2'])
  })

  it('forwards cancel event correctly', () => {
    wrapper = createWrapper()
    
    wrapper.vm.handleCancel()
    
    expect(wrapper.emitted('cancel')).toBeTruthy()
  })

  it('exposes handleSaveComplete method', () => {
    wrapper = createWrapper()
    
    expect(typeof wrapper.vm.handleSaveComplete).toBe('function')
  })

  it('exposes startEditing method', () => {
    wrapper = createWrapper()
    
    expect(typeof wrapper.vm.startEditing).toBe('function')
  })
}) 