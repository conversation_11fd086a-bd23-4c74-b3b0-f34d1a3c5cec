# BravoFormField Enhancements

## Overview

This document describes the enhancements made to the `BravoFormField.vue` component to support proper formatting of date, datetime, and currency fields across the application.

## Changes Made

### 1. Added `formattedDisplayValue` Computed Property

A new computed property was added that automatically formats the display value based on the input type:

- **Date fields (`inputType='date'`)**: Formats date values using `toLocaleDateString()` 
  - Example: `"2024-03-15"` → `"3/15/2024"`

- **DateTime fields (`inputType='datetime'`)**: Formats datetime values using `toLocaleString()` with specific options
  - Example: `"2024-03-15T10:30:00Z"` → `"3/15/2024, 10:30 AM"`

- **Currency fields (`inputType='currency'`)**: Formats numeric values as USD currency
  - Example: `1234.5` → `"$1,234.50"`

### 2. Updated Template to Use Formatted Values

All display sections in the template now use `formattedDisplayValue` instead of `displayValue`:
- Text display
- Custom display slots
- URL display
- HTML display
- Chips display
- Tag display

### 3. Existing Timezone Handling

The component already includes proper timezone handling for datetime fields when saving:
- Converts local datetime to UTC format for API compatibility
- Preserves the user's intended time regardless of timezone

## Usage

No changes are required to existing components using BravoFormField. The formatting is applied automatically based on the `inputType` prop:

```vue
<!-- Date field - will automatically format the display -->
<BravoFormField
  :label="'Due Date'"
  :field-name="'due_date'"
  :value="dateValue"
  :display-value="dateValue"
  input-type="date"
  display-type="text"
/>

<!-- DateTime field - will automatically format with time -->
<BravoFormField
  :label="'Scheduled At'"
  :field-name="'scheduled_at'"
  :value="datetimeValue"
  :display-value="datetimeValue"
  input-type="datetime"
  display-type="text"
/>

<!-- Currency field - will automatically format as USD -->
<BravoFormField
  :label="'Amount'"
  :field-name="'amount'"
  :value="1234.5"
  :display-value="1234.5"
  input-type="currency"
  display-type="text"
/>
```

## Benefits

1. **Consistency**: All date, datetime, and currency fields across the application now display in a consistent, user-friendly format
2. **Localization**: Uses browser's locale settings for date/time formatting
3. **No Breaking Changes**: Existing components continue to work without modification
4. **Automatic Formatting**: No need to manually format values in each component

## What Stayed in CaseInfoPanel

The following field-specific logic remains in CaseInfoPanel.vue as it's specific to that component:

1. **Field Dependencies**:
   - Owner team/user relationship (clearing user when team changes)
   - Block config field groupings

2. **API-Specific Transformations**:
   - `idr_resolution` array/string conversion
   - `resolve_issue` field mapping

3. **Meta Store Dependencies**:
   - `bc__tags_support` filtering based on team/category
   - Category options from meta store
   - Members users lookup and filtering 